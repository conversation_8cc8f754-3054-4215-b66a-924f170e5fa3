<!DOCTYPE html>
<html>

	<head>
		<meta charset="utf-8">
		<title>Hello MUI</title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">

		<!--标准mui.css-->
		<link rel="stylesheet" href="../css/mui.min.css">
		<!--App自定义的css-->
		<!--<link rel="stylesheet" type="text/css" href="../css/app.css"/>-->
		<style>
			.mui-row.mui-fullscreen>[class*="mui-col-"] {
				height: 100%;
			}

			#head {
				line-height: 20px;
			}

			.head-img {
				width: 32px;
				height: 32px;
				margin: 10px;
			}

			#head-img1 {
				position: absolute;
				bottom: 10px;
				right: 40px;
				width: 40px;
				height: 40px;
			}

			.mui-backdrop {
				position: fixed;
				top: 0;
				right: 0;
				bottom: 0;
				left: 0;
				z-index: 998;
				background-color: rgba(0, 0, 0, .3);
			}

			.container {

				display: flex;
				/* 将容器设置为弹性盒子 */
				justify-content: center;
				/* 将弹性盒子内的内容水平居中 */
				color: white;
				margin: 10px;

			}

			.switchtext {

				display: flex;
				/* 将容器设置为弹性盒子 */
				justify-content: center;
				/* 将弹性盒子内的内容水平居中 */
				color: white;
				margin: 10px;
				background-color: #6d6d72;
				border-radius: 20px;

			}
		</style>

	</head>
	<body style=" background-color: black;">
		<header class="mui-bar mui-bar-nav" style=" background-color: black;">
			<a class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></a>
			<h1 class="mui-title " style="color: white;">敲击</h1>
		</header>
		<div class="mui-content mui-content-padded" style=" background-color: black;">

			<div style="
				border: 2px  dashed white;
				border-radius:20px;">
				<div class=" container">
					<img src="../image/knocking.png" />
				</div>
				<div class=" container">
					<h5>提示：运动跑跳时，建议关掉敲击功能</h4>
				</div>
			</div>
		</div>
		<div id="knocking" class="switchtext">
			敲击
			<div class="mui-switch mui-switch-mini  mui-switch-blue">
				<div class="mui-switch-handle"></div>
			</div>
		</div>
	</body>
	<script src="../js/mui.min.js"></script>
	<script>
		mui.init({
			swipeBack: false //启用右滑关闭功能
		});

		document.getElementById("knocking").addEventListener("toggle", function(event) {
			if (event.detail.isActive) {
				// console.log("AT+CK=1\r\n");
				SPP_sendAT("AT+CT=1\r\n");
			} else {
				// console.log("AT+CK=0\r\n");
				SPP_sendAT("AT+CT=0\r\n");
			}
		});
	</script>
</html>