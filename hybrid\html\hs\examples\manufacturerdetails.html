<!DOCTYPE html>
<html>

	<head>
		<meta charset="utf-8">
		<title>Hello MUI</title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<!--标准mui.css-->
		<link rel="stylesheet" href="../css/mui.min.css">
		<!--App自定义的css-->
		<!--<link rel="stylesheet" type="text/css" href="../css/app.css"/>-->
		<style>
			.mui-row.mui-fullscreen>[class*="mui-col-"] {
				height: 100%;
			}

			.title {
				margin: 20px 15px 10px;
				color: #6d6d72;
				font-size: 15px;
			}

			#head {
				line-height: 20px;
			}

			.head-img {
				width: 32px;
				height: 32px;
				margin: 10px;
			}

			#head-img1 {
				position: absolute;
				bottom: 10px;
				right: 40px;
				width: 40px;
				height: 40px;
			}

			.code {
				word-wrap: break-word;
				word-break: normal;
				font-size: 90%;
				color: #c7254e;
				background-color: #f9f2f4;
				border-radius: 4px;
			}
		</style>

	</head>
	<body>
		<header class="mui-bar mui-bar-nav">
			<a class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></a>
			<h1 class="mui-title">更多</h1>
		</header>
		<div class="mui-content">
			<div class="mui-card">
				<div class="mui-card-content">
					<ul class="mui-table-view">
						<li class="mui-table-view-cell">产品型号
							<div class="mui-pull-right">
								H2SUltra
							</div>
						</li>

						<li class=" mui-table-view-cell">芯片型号
							<div class="mui-pull-right">
								277
							</div>
						</li>

						<li class="mui-table-view-cell">生产厂家
							<div class="mui-pull-right">
								华上
							</div>
						</li>

						<li class="mui-table-view-cell">生产日期
							<div class="mui-pull-right">
								20231036
							</div>
						</li>

						<li class="mui-table-view-cell">蓝牙MAC地址
							<div class="mui-pull-right">
								<p id="address"></p>
							</div>
						</li>



					</ul>
				</div>
			</div>






	</body>
	<script src="../js/mui.min.js"></script>
	<script>
		document.getElementById("address").textContent = SPP.getMacAddress()
	</script>


</html>