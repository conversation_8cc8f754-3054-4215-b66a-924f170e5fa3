<template>
	<view class="content">
		<!-- <u-navbar title="自由说模式" leftIcon="arrow-left" :autoBack="true" @rightClick="rightClick" :placeholder="true" bgColor="#f3f4f6"  rightIcon="list-dot"></u-navbar> -->
		<navname :targetlanguage="targetlanguagee" :sourcelanguage="sourcelanguagee"></navname>
		
		
		
	</view>
</template>

<script>
	import nav from "@/components/nav.vue"
	export default {
		data() {
			return {
				targetlanguagee:"",//目标语言
				sourcelanguagee:"",//源语言
			}
		},
		components:{
			navname : nav
		},
		onShow() {
			  this.targetlanguagee= uni.getStorageSync('targetlanguagee');
			  this.sourcelanguagee= uni.getStorageSync('sourcelanguagee');
		},
		methods: {
			
		}
	}
</script>

<style lang="scss">
	page{
		height: 100%;
		background-color: #f8f8f8;
	}
	.content{
		width: 100%;
		
	}
</style>
