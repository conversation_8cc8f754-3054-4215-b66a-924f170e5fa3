export default {

	// 公共跳转方法
	jump(url, type = 1) {
		// 保留当前页面，跳转到应用内的某个页面
		if (type == 1) {
			uni.navigateTo({
				url: url
			})
		}

		// 关闭当前页面，跳转到应用内的某个页面
		if (type == 2) {
			uni.redirectTo({
				url: url
			})
		}
		// 关闭所有页面，打开到应用内的某个页面
		if (type == 3) {
			uni.reLaunch({
				url: url
			})
		}

		// 跳转到 tabBar 页面，并关闭其他所有非 tabBar 页面
		if (type == 4) {
			uni.switchTab({
				url: url
			})
		}
	},
	tostring(value) {
		return JSON.stringify(value);
	},
	toast(title, icon = 'none') {
		uni.showToast({
			title: title,
			icon: icon,
			duration: 1500
		});
	},
	copy(res, isToast = true) {
		uni.setClipboardData({
			data: res,
			success: function() {
				if (isToast) {
					uni.showToast({
						title: "复制成功",
						duration: 2000,
						icon: "none",
					});
				}
			},
			fail() {
				console.log('copy fail')
			}
		});

	},
	myCache(key, value, seconds = 3600 * 24) {
		let nowTime = Date.parse(new Date()) / 1000;
		if (key && value) {
			let expire = nowTime + Number(seconds);
			uni.setStorageSync(key, JSON.stringify(value) + '|' + expire)
		} else if (key && !value) {
			let val = uni.getStorageSync(key);
			if (val) {
				let temp = val.split('|')
				if (!temp[1] || temp[1] <= nowTime) {
					uni.removeStorageSync(key)
					return '';
				} else {
					return JSON.parse(temp[0]);
				}
			}
		}
	},
	// 将文本中的网址样式修改为蓝色，并添加链接
	formatText(text) {
		const urlRegex = /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/ig;
		const urls = text.match(urlRegex);
		let formattedText = text;
		if (urls) {
			urls.forEach((url) => {
				if (formattedText.includes(`<img src="${url}"`) || formattedText.includes(`<img src='${url}'`)) {
				        return; // Skip processing the URL if it's within an <img> tag
				}
				const link = `<a href="${url}" style="color: #aaaaff;">${url}</a>`;
				formattedText = formattedText.replace(url, link);
			});
		}
		return formattedText;
	},
	//url路径解密函数
	decryptUrl(encryptedUrl) {
	  var decryptionKey = 'linfengcommunitySYKey'; // 与后端相同的解密密钥
	  // 自定义解密逻辑
	  // 这里使用与加密相反的操作，将密文的每个字符减去密钥的字符来还原明文
	  var decryptedUrl = '';
	  for (var i = 0; i < encryptedUrl.length; i++) {
	    var encryptedChar = encryptedUrl.charAt(i);
	    var decryptedChar = String.fromCharCode(encryptedChar.charCodeAt(0) - decryptionKey.charCodeAt(i % decryptionKey.length));
	    decryptedUrl += decryptedChar;
	  }
	  return decryptedUrl;
	},
	
	// 节流函数
	throttle(func, wait) {
	
	    let previous = 0;
	
	    return function() {
	        let now = +new Date();
	        let context = this;
	
	        if (now - previous >= wait) {
	            func.apply(context, arguments);
	            previous = now; // 执行后更新 previous 值
	        }
	    }
	},
	
	/*
	 * 打开[ios/安卓]GPS定位权限
	 */
	 openGps(fun) {
		let system = uni.getSystemInfoSync(); // 获取系统信息
		if (system.platform === 'android') { // 判断平台
			var context = plus.android.importClass("android.content.Context");
			var locationManager = plus.android.importClass("android.location.LocationManager");
			var main = plus.android.runtimeMainActivity();
			var mainSvr = main.getSystemService(context.LOCATION_SERVICE);
			if (!mainSvr.isProviderEnabled(locationManager.GPS_PROVIDER)) {
				uni.showModal({
					title: '提示',
					content: '请打开定位服务功能',
					showCancel: false, // 不显示取消按钮
					success() {
						var Intent = plus.android.importClass('android.content.Intent');
						var Settings = plus.android.importClass('android.provider.Settings');
						var intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
						main.startActivity(intent); // 打开系统设置GPS服务页面
	
					}
				});
			}
		} else if (system.platform === 'ios') {
			var cllocationManger = plus.ios.import("CLLocationManager");
			var enable = cllocationManger.locationServicesEnabled();
			var status = cllocationManger.authorizationStatus();
			plus.ios.deleteObject(cllocationManger);
			console.log("手机系统的定位没有打开");
			uni.showModal({
				title: '提示',
				content: '请打开定位服务功能',
				showCancel: false, // 不显示取消按钮
				success() {
					var UIApplication = plus.ios.import("UIApplication");
					var application2 = UIApplication.sharedApplication();
					var NSURL2 = plus.ios.import("NSURL");
					var setting2 = NSURL2.URLWithString("App-Prefs:root=Privacy&path=LOCATION");
					application2.openURL(setting2);
					plus.ios.deleteObject(setting2);
					plus.ios.deleteObject(NSURL2);
					plus.ios.deleteObject(application2);
				}
			});
		}
	}

}