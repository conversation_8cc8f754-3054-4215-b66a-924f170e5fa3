﻿<template>
  <view class="root-container">
    <!-- 椤堕儴瀵艰埅鏍?-->
    <view class="nav-bar">
      <view class="nav-content">
        <view class="back-btn" @click="goBack">
          <u-icon name="arrow-left" color="#4B7BEC" size="36"></u-icon>
        </view>
        <view class="nav-title">
          <text>灏忓彾鍚屽</text>
          <view class="online-indicator">
            <view class="dot"></view>
            <text>鍦ㄧ嚎</text>
          </view>
        </view>
        <view class="nav-right">
          <u-icon name="plus" color="#4B7BEC" size="40" @click="clearMessages"></u-icon>
        </view>
      </view>
    </view>
    
    <!-- 鑱婂ぉ娑堟伅鍖哄煙 -->
    <scroll-view
      class="chat-container"
      scroll-y
      :scroll-into-view="scrollIntoViewId"
      :scroll-with-animation="true"
      :enable-back-to-top="false"
      :enhanced="true"
      :bounces="false"
      :show-scrollbar="false"
      :fast-deceleration="false"
      scroll-anchoring
      @scrolltoupper="loadMoreMessages"
      @scroll="onScroll"
      upper-threshold="200"
      lower-threshold="50"
    >
      <!-- 娆㈣繋娑堟伅鍜屾彁绀?-->
      <view class="welcome-tips" v-if="messages.length === 0">
        <image src="/static/aiku/ai-assistant.png" class="ai-avatar-large" mode="aspectFit"></image>
        <text class="welcome-title">鎴戞槸灏忓彾鍚屽</text>
        <text class="welcome-desc">鎮ㄧ殑鏅鸿兘AI鍔╂墜锛屾垜鍙互鍥炵瓟闂銆佹彁渚涘缓璁紝闄偍鑱婂ぉ</text>
        
        <!-- 鎺ㄨ崘闂 -->
        <view class="suggested-questions">
          <view class="question-item" v-for="(item, index) in suggestedQuestions" :key="index" @click="askSuggestedQuestion(item)">
            <text>{{item}}</text>
          </view>
        </view>
      </view>
      
      <!-- 娑堟伅鍒楄〃 -->
      <block v-for="(item, index) in messages" :key="index">
        <!-- 鏃堕棿鍒嗗壊绾?-->
        <view class="time-divider" v-if="showTimeDivider(item, index)">
          <text>{{ formatMessageTime(item.timestamp) }}</text>
        </view>
        
        <!-- 鐢ㄦ埛娑堟伅 -->
        <view class="message-item user-message" v-if="item.role === 'user'">
          <view class="avatar-container">
            <image :src="userInfo.face || userImage" class="avatar" mode="aspectFill"></image>
          </view>
          <view class="message-content">
            <text>{{ item.content }}</text>
          </view>
        </view>
        
        <!-- AI娑堟伅 -->
        <view class="message-item ai-message" v-else>
          <view class="avatar-container">
            <image src="/static/aiku/ai-assistant.png" class="avatar" mode="aspectFill"></image>
          </view>
          <view class="message-content">
            <text selectable>{{ item.content }}</text>
            <view class="typing-indicator" v-if="item.isTyping">
              <view class="typing-dot"></view>
              <view class="typing-dot"></view>
              <view class="typing-dot"></view>
            </view>
          </view>
        </view>
      </block>
      
      <view id="chat-bottom" class="bottom-space"></view>
    </scroll-view>
    
    <!-- 搴曢儴杈撳叆鍖哄煙 -->
    <view class="input-container">
      <!-- 璇煶/鏂囧瓧杈撳叆鍒囨崲鎸夐挳 -->
      <view class="input-type-btn" @click="toggleInputMode">
        <u-icon :name="isVoiceMode ? 'list-dot' : 'mic'" size="46" color="#4B7BEC"></u-icon>
      </view>
      
      <!-- 鏂囨湰杈撳叆妗?-->
      <view class="input-box" v-if="!isVoiceMode">
        <textarea 
          class="input-textarea" 
          v-model="inputText" 
          :disabled="isSending"
          auto-height 
          :maxlength="-1"
          :cursor-spacing="20"
          @confirm="sendMessage"
          :placeholder="isSending ? '鍙戦€佷腑...' : '璇疯緭鍏ラ棶棰?..'"
          confirm-type="send"
        />
      </view>
      
      <!-- 璇煶鎸夐挳 -->
      <view 
        class="voice-btn" 
        v-else 
        @touchstart="startRecording" 
        @touchend="stopRecording"
        @touchcancel="cancelRecording"
      >
        <text>{{ recording ? '鏉惧紑鍙戦€? : '鎸変綇璇磋瘽' }}</text>
      </view>
      
      <!-- 鍙戦€佹寜閽?-->
      <view class="send-btn" :class="{'send-btn-active': canSend}" @click="sendMessage">
        <u-icon name="arrow-upward" color="#FFFFFF" size="48"></u-icon>
      </view>
    </view>
    
    <!-- 褰曢煶鎻愮ず灞?-->
    <view class="recording-mask" v-if="recording">
      <view class="recording-indicator">
        <view class="recording-wave" :class="{'recording-wave-active': recordingVolume > 0}">
          <view class="wave-item" v-for="i in 5" :key="i" :style="{height: 10 + recordingVolume * 2 + 'rpx'}"></view>
        </view>
        <text>{{ recordingTime }}s</text>
        <text class="cancel-text">涓婃粦鍙栨秷</text>
      </view>
    </view>
  </view>
</template>

<script>
import configs from '@/config/config'
import aiChatService from '@/api/aiChatService.js'
export default {
  data() {
    return {
      // 椤甸潰鏁版嵁
      configs,
      userImage:configs.defaultUserPhoto,
      userInfo: {},
      messages: [],
      inputText: '',

      // 璞嗗寘寮忔粴鍔ㄦ帶鍒剁郴缁?      scrollIntoViewId: '', // 鐢ㄤ簬瀹氫綅鐨勫厓绱營D
      isAutoScrollEnabled: true, // 鏄惁鍚敤鑷姩婊氬姩
      isUserScrolling: false, // 鐢ㄦ埛鏄惁姝ｅ湪鎵嬪姩婊氬姩
      lastScrollTop: 0, // 涓婃婊氬姩浣嶇疆
      scrollEndTimer: null, // 婊氬姩缁撴潫妫€娴嬭鏃跺櫒
      autoScrollTimer: null, // 鑷姩婊氬姩璁℃椂鍣?      scrollAnimationFrame: null, // 婊氬姩鍔ㄧ敾甯?      isNearBottom: true, // 鏄惁鎺ヨ繎搴曢儴
      scrollThreshold: 100, // 搴曢儴闃堝€硷紙rpx锛?      userScrollTimeout: 2000, // 鐢ㄦ埛婊氬姩鍚庡涔呮仮澶嶈嚜鍔ㄦ粴鍔紙ms锛?
      // 骞虫粦婊氬姩鎺у埗
      smoothScrollTimer: null, // 骞虫粦婊氬姩璁℃椂鍣?      scrollTargetTop: 0, // 鐩爣婊氬姩浣嶇疆
      currentScrollTop: 0, // 褰撳墠婊氬姩浣嶇疆
      scrollSpeed: 0.3, // 婊氬姩閫熷害绯绘暟锛?-1锛?      isSmoothing: false, // 鏄惁姝ｅ湪骞虫粦婊氬姩

      // 鎵撳瓧鏁堟灉鎺у埗
      typingScrollTimer: null, // 鎵撳瓧鏃舵粴鍔ㄨ鏃跺櫒
      isTyping: false, // 鏄惁姝ｅ湪鎵撳瓧
      realTimeScrollTimer: null, // 瀹炴椂婊氬姩璁℃椂鍣?
      // 鍏朵粬鐘舵€?      isVoiceMode: false,
      recording: false,
      recordingTime: 0,
      recordingVolume: 0,
      recordTimer: null,
      volumeTimer: null,
      isSending: false,
      recorderManager: null,
      
      // 绀轰緥鎺ㄨ崘闂
      suggestedQuestions: [
        '浣犺兘鍋氫粈涔堬紵',
        '璇疯涓瑧璇?,
        '浠婂ぉ澶╂皵鎬庝箞鏍凤紵',
        '浣犳槸璋侊紵'
      ],
      
      // 娴嬭瘯鏁版嵁锛堢敤浜庡睍绀猴級
      testMessages: [
        {
          role: 'assistant',
          content: '浣犲ソ锛佹垜鏄皬鍙跺悓瀛︼紝寰堥珮鍏磋璇嗕綘銆傛湁浠€涔堟垜鍙互甯姪浣犵殑鍚楋紵',
          timestamp: Date.now() - 24 * 60 * 60 * 1000 // 鏄ㄥぉ
        },
        {
          role: 'user',
          content: '浣犲ソ锛屾垜鎯充簡瑙ｄ竴涓嬩綘鑳藉仛浠€涔堬紵',
          timestamp: Date.now() - 24 * 60 * 60 * 1000 + 1000
        },
        {
          role: 'assistant',
          content: '鎴戝彲浠ュ府鍔╀綘鍥炵瓟闂銆佹彁渚涗俊鎭€佽繘琛屾棩甯稿璇濄€佽绗戣瘽銆佹彁渚涘涔犲府鍔╃瓑銆備綘鏈変粈涔堝叿浣撻渶瑕佸悧锛?,
          timestamp: Date.now() - 24 * 60 * 60 * 1000 + 5000
        },
        {
          role: 'user',
          content: '缁欐垜璁蹭釜绗戣瘽鍚?,
          timestamp: Date.now() - 5 * 60 * 1000 // 5鍒嗛挓鍓?        },
        {
          role: 'assistant',
          content: '濂界殑锛岃繖鏄竴涓叧浜庣▼搴忓憳鐨勭瑧璇濓細\n\n绋嬪簭鍛樺幓闈㈣瘯锛岄潰璇曞畼闂細"浣犳湁5骞寸粡楠屽悧锛?\n绋嬪簭鍛橈細"鎴戞湁1骞寸粡楠岀敤浜?骞淬€?',
          timestamp: Date.now() - 5 * 60 * 1000 + 3000
        }
      ]
    }
  },
  computed: {
    // 鍒ゆ柇鏄惁鍙互鍙戦€佹秷鎭?    canSend() {
      return this.inputText.trim().length > 0 && !this.isSending;
    },
	// 浼樺寲鍚庣殑娑堟伅鍒楄〃锛堝彲浠ユ坊鍔犺櫄鎷熸粴鍔ㄦ敮鎸侊級
	  optimizedMessages() {
	    // 濡傛灉娑堟伅杩囧锛屽彧娓叉煋鏈€杩戠殑N鏉?	    const maxVisible = 50;
	    if (this.messages.length > maxVisible) {
	      return this.messages.slice(-maxVisible);
	    }
	    return this.messages;
	  }
  },
  watch: {
    // 鐩戝惉娑堟伅鏁扮粍鍙樺寲锛屾櫤鑳芥粴鍔ㄥ埌搴曢儴
    messages: {
      deep: true,
      handler(newVal, oldVal) {
        if (newVal.length > 0) {
          // 濡傛灉鏄柊澧炴秷鎭紝涓旂敤鎴锋病鏈夊湪婊氬姩锛屽垯鑷姩婊氬姩
          if (!oldVal || newVal.length > oldVal.length) {
            this.$nextTick(() => {
              setTimeout(() => {
                this.smoothScrollToBottom('new_message');
              }, 50);
            });
          }

          // 濡傛灉鏄秷鎭唴瀹瑰彉鍖栵紙鎵撳瓧杩囩▼锛夛紝涔熻妫€鏌ユ粴鍔?          if (oldVal && newVal.length === oldVal.length) {
            // 妫€鏌ユ渶鍚庝竴鏉℃秷鎭槸鍚︽湁鍐呭鍙樺寲
            const lastNew = newVal[newVal.length - 1];
            const lastOld = oldVal[oldVal.length - 1];
            if (lastNew && lastOld && lastNew.content !== lastOld.content) {
              // 鍐呭鍙樺寲鏃剁殑瀹炴椂婊氬姩
              this.checkAndScrollToBottom();
            }
          }
        }
      }
    },

    // 鐩戝惉鎵撳瓧鐘舵€佸彉鍖?    isTyping: {
      handler(newVal) {
        if (newVal) {
          // 寮€濮嬫墦瀛楁椂锛屽惎鐢ㄥ疄鏃舵粴鍔ㄧ洃鎺?          this.startRealTimeScroll();
        } else {
          // 鎵撳瓧缁撴潫鏃讹紝鍋滄鐩戞帶骞剁‘淇濇粴鍔ㄥ埌搴曢儴
          this.stopRealTimeScroll();
          this.$nextTick(() => {
            setTimeout(() => {
              this.smoothScrollToBottom('typing_end');
            }, 200);
          });
        }
      }
    }
  },
  mounted() {
    this.userInfo = this.$options.filters.isLogin() || {};

    // 鍔犺浇娴嬭瘯鏁版嵁
    // this.loadTestData();

    // 鍒濆鍖栧綍闊崇鐞嗗櫒
    this.initRecorderManager();

    // 鍒濆鍖栬眴鍖呭紡婊氬姩绯荤粺
    this.initScrollSystem();

    // 鐩戝惉椤甸潰鏄剧ず浜嬩欢
    uni.$on('pageShow', this.handlePageShow);
    uni.$on('pageHide', this.handlePageHide);

    // 椤甸潰鏄剧ず鏃剁‘淇濇粴鍔ㄥ埌搴曢儴
    this.$nextTick(() => {
      setTimeout(() => {
        this.smartScrollToBottom('page_mount');
      }, 300);
    });
  },

  beforeDestroy() {
    // 娓呯悊瀹氭椂鍣ㄥ拰浜嬩欢鐩戝惉
    this.clearAllTimers();
    uni.$off('pageShow', this.handlePageShow);
    uni.$off('pageHide', this.handlePageHide);
  },
  methods: {
    // 杩斿洖涓婁竴椤?    goBack() {
      uni.navigateBack();
    },
    
    // 鍔犺浇娴嬭瘯鏁版嵁
    loadTestData() {
      setTimeout(() => {
        this.messages = [...this.testMessages];
        this.$nextTick(() => {
          this.smartScrollToBottom('load_test_data');
        });
      }, 500);
    },

    // 娓呯┖瀵硅瘽璁板綍
    clearMessages() {
      this.messages = [];
    },
    
    // 鍙戦€佹秷鎭?	sendMessage() {
	  if (!this.canSend) return;
	  
	  const content = this.inputText.trim();
	  this.inputText = '';
	  
	  // 鎵归噺鏇存柊娑堟伅锛屽噺灏戞覆鏌撴鏁?	  const userMessage = {
	    role: 'user',
	    content,
	    timestamp: Date.now()
	  };
	  
	  const assistantMessage = {
	    role: 'assistant',
	    content: '',
	    isTyping: true,
	    timestamp: Date.now()
	  };
	  
	  // 涓€娆℃€ф坊鍔犱袱鏉℃秷鎭?	  this.messages.push(userMessage, assistantMessage);
	  
	  this.isSending = true;
	  
	  // 纭繚婊氬姩鍒板簳閮紝浣跨敤鏂扮殑鏅鸿兘婊氬姩
	  this.$nextTick(() => {
	    setTimeout(() => {
	      this.smartScrollToBottom('new_message');
	    }, 50);
	  });
	  
	  // 璋冪敤鏄熺伀璁ょ煡澶фā鍨婣PI鑾峰彇鍥炲
	  aiChatService.sendMessage(content)
	    .catch(error => {
	      console.error('AI鍥炲鍑洪敊:', error);
	      // 鍑洪敊鏃舵洿鏂版秷鎭姸鎬?	      assistantMessage.isTyping = false;
	      assistantMessage.content = '鎶辨瓑锛屾垜閬囧埌浜嗕竴浜涢棶棰橈紝璇风◢鍚庡啀璇曘€?;
	      this.isSending = false;
	    });
	},
    
    // 妯℃嫙鐢熸垚鍥炲
    generateResponse(question) {
      // 杩欓噷鏄ā鎷熺殑鍥炲閫昏緫锛屽疄闄呴」鐩腑搴旇璋冪敤API
      question = question.toLowerCase();
      
      if (question.includes('浣犳槸璋?) || question.includes('浣犲彨浠€涔?) || question.includes('浣犵殑鍚嶅瓧')) {
        return '鎴戞槸灏忓彾鍚屽锛屾偍鐨凙I鏅鸿兘鍔╂墜銆傛垜闅忔椂鍑嗗涓烘偍鎻愪緵甯姪鍜屾湇鍔★紒';
      } else if (question.includes('绗戣瘽') || question.includes('娈靛瓙')) {
        return '浠婂ぉ缁欐偍甯︽潵涓€涓湁瓒ｇ殑鏁呬簨锛歕n\n鏈変竴浣嶈€佷汉锛屾瘡澶╅兘鍘诲叕鍥暎姝ワ紝姣忓ぉ閮藉甫鐫€甯藉瓙銆傛湁涓€澶╋紝涓€浣嶅皬鏈嬪弸闂粬锛?鐖风埛锛屾偍涓轰粈涔堟瘡澶╅兘鎴村附瀛愬憿锛?\n鑰佷汉寰瑧鐫€鍥炵瓟锛?鍥犱负鎴戞槰澶╁繕鎴翠簡锛屾壘浜嗕竴澶╀篃娌℃壘鍒拌嚜宸便€?';
      } else if (question.includes('澶╂皵')) {
        return '鐩墠灏忓彾鍚屽涓嶈兘鑾峰彇瀹炴椂澶╂皵鏁版嵁锛屽缓璁偍鏌ョ湅澶╂皵棰勬姤APP鎴栫綉绔欒幏鍙栨渶鍑嗙‘鐨勫ぉ姘斾俊鎭€傛湭鏉ユ垜浼氱户缁崌绾э紝涓烘偍鎻愪緵鏇村鏈嶅姟锛?;
      } else if (question.includes('鑳藉仛浠€涔?) || question.includes('鍔熻兘') || question.includes('甯垜')) {
        return '鎴戞槸鎮ㄧ殑AI鍔╂墜灏忓彾鍚屽锛屽彲浠ワ細\n1. 鍥炵瓟鎮ㄧ殑闂鍜岀枒鎯慭n2. 鎻愪緵淇℃伅鍜岀煡璇哱n3. 鍜屾偍杩涜鏃ュ父瀵硅瘽\n4. 璁茬瑧璇濆拰鏈夎叮鐨勬晠浜媆n5. 鏈潵浼氭敮鎸佹洿澶氬姛鑳絓n\n璇烽棶鏈変粈涔堟垜鍙互甯姪鎮ㄧ殑鍚楋紵';
      } else {
        return '鎰熻阿鎮ㄧ殑鎻愰棶銆備綔涓烘偍鐨凙I鍔╂墜锛屾垜浼氬敖鍔涗负鎮ㄦ彁渚涘府鍔┿€傛偍鐨勯棶棰樺緢鏈変环鍊硷紝甯屾湜鎴戠殑鍥炵瓟鑳藉瀵规偍鏈夋墍甯姪銆傚鏋滈渶瑕佹洿澶氫俊鎭紝璇烽殢鏃跺憡璇夋垜锛?;
      }
    },
    
    // 澶勭悊AI娴佸紡杈撳嚭鐨勬墦瀛楁晥鏋?    handleAiStreamResponse() {
      this.isTyping = true;

      // 鍚姩瀹炴椂婊氬姩鐩戞帶
      this.startRealTimeScroll();
      
      // 鑾峰彇鏈€鍚庝竴鏉I娑堟伅(搴旇鏄鍦ㄧ瓑寰呭洖澶嶇殑娑堟伅)
      const latestMessage = this.messages[this.messages.length - 1];
      if (!latestMessage || latestMessage.role !== 'assistant') {
        console.error('鎵句笉鍒拌鏇存柊鐨凙I娑堟伅');
        return;
      }

      // 閰嶇疆鏄熺伀AI鑱婂ぉ鏈嶅姟鍥炶皟
      aiChatService.setCallbacks({
        onMessageStart: () => {
          console.log('AI寮€濮嬬敓鎴愬洖澶?..');
        },
        onMessageStream: (content, isComplete) => {
          // 鏇存柊褰撳墠娑堟伅鍐呭
          latestMessage.content = content;
          
          // 瑙﹀彂瀹炴椂婊氬姩妫€鏌?          this.checkAndScrollToBottom();
          
          // 濡傛灉宸插畬鎴?          if (isComplete) {
            latestMessage.isTyping = false;
            this.isTyping = false;
            this.isSending = false;
            
            // 鍋滄瀹炴椂婊氬姩鐩戞帶
            this.stopRealTimeScroll();
            
            // 纭繚鏈€缁堟粴鍔ㄥ埌搴曢儴
            this.$nextTick(() => {
              setTimeout(() => {
                this.smoothScrollToBottom('typing_complete');
              }, 100);
            });
          }
        },
        onMessageComplete: (finalContent) => {
          // 纭繚娑堟伅鍐呭瀹屾暣鏇存柊
          latestMessage.content = finalContent;
          latestMessage.isTyping = false;
          this.isTyping = false;
          this.isSending = false;
        },
        onError: (error) => {
          console.error('AI鑱婂ぉ鏈嶅姟閿欒:', error);
          latestMessage.isTyping = false;
          latestMessage.content = '鎶辨瓑锛屾垜閬囧埌浜嗕竴浜涢棶棰橈紝璇风◢鍚庡啀璇曘€?;
          this.isTyping = false;
          this.isSending = false;
        }
      });
    },
    
    // ==================== 璞嗗寘寮忔粴鍔ㄧ郴缁熸牳蹇冩柟娉?====================

    // 鍒濆鍖栨粴鍔ㄧ郴缁?    initScrollSystem() {
      try {
        // 纭繚 handleScroll 鏂规硶瀛樺湪
        if (typeof this.handleScroll === 'function' && typeof this.throttle === 'function') {
          // 鍒涘缓鑺傛祦鐨勬粴鍔ㄥ鐞嗗嚱鏁?          this.throttledScrollHandler = this.throttle(this.handleScroll.bind(this), 16); // 60fps
        } else {
          console.error('婊氬姩绯荤粺鍒濆鍖栧け璐ワ細缂哄皯蹇呰鐨勬柟娉?);
        }
      } catch (error) {
        console.error('婊氬姩绯荤粺鍒濆鍖栧嚭閿?', error);
      }
    },

    // 骞虫粦婊氬姩鍒板簳閮?- 鏍稿績鏂规硶
    smoothScrollToBottom(trigger = 'default') {
      // 濡傛灉鐢ㄦ埛姝ｅ湪婊氬姩涓斾笉鏄己鍒惰Е鍙戯紝鍒欎笉鑷姩婊氬姩
      if (this.isUserScrolling && !['typing_complete', 'new_message', 'page_mount'].includes(trigger)) {
        return;
      }

      // 浣跨敤 scroll-into-view 鏂瑰紡杩涜骞虫粦婊氬姩
      this.performSmoothScroll();
    },

    // 鎵ц骞虫粦婊氬姩
    performSmoothScroll() {
      // 娓呴櫎涔嬪墠鐨勬粴鍔ㄨ鏃跺櫒
      this.clearScrollTimers();

      // 閲嶇疆婊氬姩ID
      this.scrollIntoViewId = '';

      // 浣跨敤 requestAnimationFrame 纭繚鍦ㄤ笅涓€甯ф墽琛?      this.scrollAnimationFrame = requestAnimationFrame(() => {
        this.$nextTick(() => {
          // 寤舵椂璁剧疆婊氬姩ID锛岀‘淇滵OM鏇存柊
          this.autoScrollTimer = setTimeout(() => {
            this.scrollIntoViewId = 'chat-bottom';
          }, 16); // 鍑忓皯寤舵椂锛屾彁楂樺搷搴旈€熷害
        });
      });
    },

    // 妫€鏌ュ苟婊氬姩鍒板簳閮紙鐢ㄤ簬鎵撳瓧杩囩▼涓級
    checkAndScrollToBottom() {
      if (this.isAutoScrollEnabled && this.isNearBottom && !this.isUserScrolling) {
        // 浣跨敤鏇撮绻佺殑婊氬姩妫€鏌?        this.performSmoothScroll();
      }
    },

    // 鏅鸿兘婊氬姩鍒板簳閮紙鍏煎鏃ф柟娉曞悕锛?    smartScrollToBottom(trigger = 'default') {
      this.smoothScrollToBottom(trigger);
    },

    // 鎵ц婊氬姩鍒板簳閮紙鍏煎鏃ф柟娉曞悕锛?    performScrollToBottom() {
      this.performSmoothScroll();
    },

    // 澶勭悊鐢ㄦ埛婊氬姩浜嬩欢
    handleScroll(e) {
      const { scrollTop, scrollHeight, clientHeight } = e.detail;

      // 璁＄畻鏄惁鎺ヨ繎搴曢儴
      const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
      this.isNearBottom = distanceFromBottom <= this.scrollThreshold;

      // 妫€娴嬬敤鎴锋槸鍚﹀湪涓诲姩婊氬姩
      const isScrollingUp = scrollTop < this.lastScrollTop;
      const isScrollingDown = scrollTop > this.lastScrollTop;

      if (isScrollingUp || (isScrollingDown && !this.isNearBottom)) {
        // 鐢ㄦ埛涓诲姩婊氬姩锛屾殏鍋滆嚜鍔ㄦ粴鍔?        this.setUserScrolling(true);
      } else if (this.isNearBottom) {
        // 鐢ㄦ埛婊氬姩鍒板簳閮ㄩ檮杩戯紝鎭㈠鑷姩婊氬姩
        this.setUserScrolling(false);
      }

      this.lastScrollTop = scrollTop;
    },

    // 璁剧疆鐢ㄦ埛婊氬姩鐘舵€?    setUserScrolling(isScrolling) {
      this.isUserScrolling = isScrolling;
      this.isAutoScrollEnabled = !isScrolling || this.isNearBottom;

      // 娓呴櫎涔嬪墠鐨勬仮澶嶈鏃跺櫒
      if (this.scrollEndTimer) {
        clearTimeout(this.scrollEndTimer);
      }

      // 濡傛灉鐢ㄦ埛鍋滄婊氬姩锛岃缃欢鏃舵仮澶嶈嚜鍔ㄦ粴鍔?      if (isScrolling) {
        this.scrollEndTimer = setTimeout(() => {
          if (this.isNearBottom) {
            this.isUserScrolling = false;
            this.isAutoScrollEnabled = true;
          }
        }, this.userScrollTimeout);
      }
    },
    
    // 鍔犺浇鏇村鍘嗗彶娑堟伅
    loadMoreMessages() {
      // 瀹為檯椤圭洰涓繖閲屽簲璇ュ姞杞藉巻鍙叉秷鎭?      console.log('鍔犺浇鏇村鍘嗗彶娑堟伅');
    },
    
    // 鏍煎紡鍖栨秷鎭椂闂?    formatMessageTime(timestamp) {
      const date = new Date(timestamp);
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      
      // 鍒ゆ柇鏄粖澶┿€佹槰澶╄繕鏄洿鏃?      if (date.toDateString() === today.toDateString()) {
        return this.formatTime(date); // 浠婂ぉ锛屾樉绀烘椂闂?      } else if (date.toDateString() === yesterday.toDateString()) {
        return '鏄ㄥぉ ' + this.formatTime(date); // 鏄ㄥぉ
      } else {
        return `${date.getMonth() + 1}鏈?{date.getDate()}鏃?${this.formatTime(date)}`; // 鏇存棭
      }
    },
    
    // 鏍煎紡鍖栨椂闂翠负 HH:MM
    formatTime(date) {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    },
    
    // 鍒ゆ柇鏄惁鏄剧ず鏃堕棿鍒嗛殧绾?    showTimeDivider(message, index) {
      if (index === 0) return true;
      
      // 涓庝笂涓€鏉℃秷鎭浉姣旓紝濡傛灉鏃堕棿闂撮殧瓒呰繃5鍒嗛挓锛屽垯鏄剧ず鏃堕棿鍒嗛殧绾?      const prevMessage = this.messages[index - 1];
      return message.timestamp - prevMessage.timestamp > 5 * 60 * 1000;
    },
    
    // 鍒囨崲杈撳叆妯″紡锛堣闊?鏂囧瓧锛?    toggleInputMode() {
      this.isVoiceMode = !this.isVoiceMode;
    },
    
    // 鍒濆鍖栧綍闊崇鐞嗗櫒
    initRecorderManager() {
      // #ifdef APP-PLUS || MP-WEIXIN
      this.recorderManager = uni.getRecorderManager();
      
      this.recorderManager.onStart(() => {
        console.log('褰曢煶寮€濮?);
        this.recordingTime = 0;
        this.recordTimer = setInterval(() => {
          this.recordingTime++;
          
          // 鏈€闀垮綍闊?0绉?          if (this.recordingTime >= 60) {
            this.stopRecording();
          }
        }, 1000);
        
        // 妯℃嫙褰曢煶闊抽噺
        this.volumeTimer = setInterval(() => {
          this.recordingVolume = Math.floor(Math.random() * 10);
        }, 200);
      });
      
      this.recorderManager.onStop((res) => {
        console.log('褰曢煶缁撴潫', res);
        clearInterval(this.recordTimer);
        clearInterval(this.volumeTimer);
        this.recording = false;
        
        // 妯℃嫙璇煶璇嗗埆
        setTimeout(() => {
          // 闅忔満閫夋嫨涓€涓ず渚嬮棶棰樹綔涓鸿闊宠瘑鍒粨鏋?          const recognizedText = this.suggestedQuestions[Math.floor(Math.random() * this.suggestedQuestions.length)];
          this.inputText = recognizedText;
          this.sendMessage();
        }, 1000);
      });
      // #endif
    },
    
    // 寮€濮嬪綍闊?    startRecording() {
      // #ifdef APP-PLUS || MP-WEIXIN
      this.recording = true;
      this.recorderManager.start({
        duration: 60000, // 鏈€闀?0s
        sampleRate: 16000,
        numberOfChannels: 1,
        encodeBitRate: 64000,
        format: 'mp3'
      });
      // #endif
      
      // #ifdef H5
      // H5鐜涓嬫ā鎷熷綍闊宠涓?      this.recording = true;
      this.recordingTime = 0;
      this.recordTimer = setInterval(() => {
        this.recordingTime++;
        if (this.recordingTime >= 60) {
          this.stopRecording();
        }
      }, 1000);
      
      this.volumeTimer = setInterval(() => {
        this.recordingVolume = Math.floor(Math.random() * 10);
      }, 200);
      // #endif
    },
    
    // 鍋滄褰曢煶
    stopRecording() {
      // #ifdef APP-PLUS || MP-WEIXIN
      if (this.recording) {
        this.recorderManager.stop();
      }
      // #endif
      
      // #ifdef H5
      // H5鐜涓嬫ā鎷熷仠姝㈠綍闊?      clearInterval(this.recordTimer);
      clearInterval(this.volumeTimer);
      this.recording = false;
      
      // 妯℃嫙璇煶璇嗗埆
      setTimeout(() => {
        // 闅忔満閫夋嫨涓€涓ず渚嬮棶棰樹綔涓鸿闊宠瘑鍒粨鏋?        const recognizedText = this.suggestedQuestions[Math.floor(Math.random() * this.suggestedQuestions.length)];
        this.inputText = recognizedText;
        this.sendMessage();
      }, 1000);
      // #endif
    },
    
    // 鍙栨秷褰曢煶
    cancelRecording() {
      // #ifdef APP-PLUS || MP-WEIXIN
      if (this.recording) {
        this.recorderManager.stop();
        clearInterval(this.recordTimer);
        clearInterval(this.volumeTimer);
        this.recording = false;
      }
      // #endif
      
      // #ifdef H5
      clearInterval(this.recordTimer);
      clearInterval(this.volumeTimer);
      this.recording = false;
      uni.showToast({
        title: '褰曢煶宸插彇娑?,
        icon: 'none'
      });
      // #endif
    },
    
    // 璇㈤棶鎺ㄨ崘闂
    askSuggestedQuestion(question) {
      this.inputText = question;
      this.sendMessage();
    },

    // ==================== 璞嗗寘寮忔粴鍔ㄨ緟鍔╂柟娉?====================

    // 鍚姩瀹炴椂婊氬姩鐩戞帶
    startRealTimeScroll() {
      // 娓呴櫎涔嬪墠鐨勮鏃跺櫒
      this.stopRealTimeScroll();

      // 姣?0ms妫€鏌ヤ竴娆℃槸鍚﹂渶瑕佹粴鍔紙鏇撮绻佺殑妫€鏌ワ級
      this.realTimeScrollTimer = setInterval(() => {
        if (this.isTyping && this.isAutoScrollEnabled && !this.isUserScrolling) {
          this.checkAndScrollToBottom();
        }
      }, 50);
    },

    // 鍋滄瀹炴椂婊氬姩鐩戞帶
    stopRealTimeScroll() {
      if (this.realTimeScrollTimer) {
        clearInterval(this.realTimeScrollTimer);
        this.realTimeScrollTimer = null;
      }
    },

    // 鍚敤鎵撳瓧鏃剁殑瀹炴椂婊氬姩锛堜繚鐣欏吋瀹规€э級
    enableTypingScroll() {
      this.startRealTimeScroll();
    },

    // 绂佺敤鎵撳瓧婊氬姩锛堜繚鐣欏吋瀹规€э級
    disableTypingScroll() {
      this.stopRealTimeScroll();
    },

    // 澶勭悊鎵撳瓧鏃剁殑婊氬姩锛堜繚鐣欏吋瀹规€э級
    handleTypingScroll() {
      this.checkAndScrollToBottom();
    },

    // 椤甸潰鏄剧ず澶勭悊
    handlePageShow() {
      this.$nextTick(() => {
        setTimeout(() => {
          this.smartScrollToBottom('page_show');
        }, 200);
      });
    },

    // 椤甸潰闅愯棌澶勭悊
    handlePageHide() {
      this.clearAllTimers();
    },

    // 娓呯悊鎵€鏈夎鏃跺櫒
    clearAllTimers() {
      if (this.autoScrollTimer) {
        clearTimeout(this.autoScrollTimer);
        this.autoScrollTimer = null;
      }
      if (this.scrollEndTimer) {
        clearTimeout(this.scrollEndTimer);
        this.scrollEndTimer = null;
      }
      if (this.typingScrollTimer) {
        clearInterval(this.typingScrollTimer);
        this.typingScrollTimer = null;
      }
      if (this.realTimeScrollTimer) {
        clearInterval(this.realTimeScrollTimer);
        this.realTimeScrollTimer = null;
      }
      if (this.smoothScrollTimer) {
        clearInterval(this.smoothScrollTimer);
        this.smoothScrollTimer = null;
      }
      if (this.scrollAnimationFrame) {
        cancelAnimationFrame(this.scrollAnimationFrame);
        this.scrollAnimationFrame = null;
      }
    },

    // 娓呯悊婊氬姩鐩稿叧璁℃椂鍣?    clearScrollTimers() {
      if (this.autoScrollTimer) {
        clearTimeout(this.autoScrollTimer);
        this.autoScrollTimer = null;
      }
    },

    // 鐩戝惉婊氬姩浜嬩欢 - 鍏ュ彛鏂规硶
    onScroll(e) {
      // 纭繚鑺傛祦澶勭悊鍑芥暟宸插垵濮嬪寲
      if (!this.throttledScrollHandler) {
        // 濡傛灉杩樻病鍒濆鍖栵紝鍏堝垵濮嬪寲婊氬姩绯荤粺
        this.initScrollSystem();
      }

      // 浣跨敤鑺傛祦澶勭悊鍑芥暟澶勭悊婊氬姩浜嬩欢
      if (this.throttledScrollHandler && typeof this.throttledScrollHandler === 'function') {
        try {
          this.throttledScrollHandler(e);
        } catch (error) {
          console.error('婊氬姩澶勭悊鍑洪敊:', error);
          // 閲嶆柊鍒濆鍖栨粴鍔ㄧ郴缁?          this.initScrollSystem();
        }
      } else {
        // 澶囩敤澶勭悊锛氱洿鎺ヨ皟鐢?handleScroll
        if (typeof this.handleScroll === 'function') {
          try {
            this.handleScroll(e);
          } catch (error) {
            console.error('澶囩敤婊氬姩澶勭悊鍑洪敊:', error);
          }
        }
      }
    },

    // ==================== 宸ュ叿鏂规硶 ====================

    // 鑺傛祦鍑芥暟
    throttle(func, limit) {
      let inThrottle;
      return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
          func.apply(context, args);
          inThrottle = true;
          setTimeout(() => inThrottle = false, limit);
        }
      }
    },

	// 闃叉姈鍑芥暟锛堜繚鐣欏師鏈夌殑锛屼互闃插叾浠栧湴鏂逛娇鐢級
	debounce(func, wait) {
	  let timeout;
	  return function executedFunction(...args) {
	    const later = () => {
	      clearTimeout(timeout);
	      func(...args);
	    };
	    clearTimeout(timeout);
	    timeout = setTimeout(later, wait);
	  };
	},
  }
}
</script>

<style lang="scss" scoped>
/* 椤甸潰鏁翠綋鏍峰紡 */
page {
  height: 100%;
  overflow: hidden; /* 闃叉椤甸潰鏁翠綋婊氬姩 */
}

/* 瑙ｅ喅灏忕▼搴忓拰app婊氬姩鏉＄殑闂 */
/* #ifdef MP-WEIXIN || APP-PLUS */
::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
/* #endif */

/* 瑙ｅ喅H5鐨勯棶棰?*/
/* #ifdef H5 */
uni-scroll-view .uni-scroll-view::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
/* #endif */

/* 鏍瑰鍣ㄦ牱寮?*/
.root-container {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #F2F7FF 0%, #FFFFFF 50%);
}

/* 瀵艰埅鏍忔牱寮?*/
.nav-bar {
  width: 100%;
  padding: 20rpx 32rpx;
  padding-top: calc(20rpx + var(--status-bar-height));
  box-sizing: border-box;
  background: #FFFFFF;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 100;
  
  .nav-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80rpx;
    
    .back-btn {
      width: 80rpx;
      height: 80rpx;
      display: flex;
      align-items: center;
    }
    
    .nav-title {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      
      text {
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
      }
      
      .online-indicator {
        display: flex;
        align-items: center;
        margin-top: 4rpx;
        
        .dot {
          width: 16rpx;
          height: 16rpx;
          border-radius: 50%;
          background-color: #4CD964;
          margin-right: 8rpx;
        }
        
        text {
          font-size: 22rpx;
          color: #666;
          font-weight: normal;
        }
      }
    }
    
    .nav-right {
      width: 80rpx;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }
}

/* 鑱婂ぉ瀹瑰櫒鏍峰紡 - 璞嗗寘寮忔粴鍔ㄤ紭鍖?*/
.chat-container {
  flex: 1;
  width: 100%;
  padding: 20rpx 32rpx;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
  height: 0;

  /* 璞嗗寘寮忔粴鍔ㄦ€ц兘浼樺寲 */
  -webkit-overflow-scrolling: touch;
  will-change: scroll-position;
  transform: translateZ(0);
  backface-visibility: hidden;

  /* 婊氬姩鏉￠殣钘?*/
  scrollbar-width: none;
  -ms-overflow-style: none;

  /* 瓒呭钩婊戞粴鍔ㄤ紭鍖?*/
  scroll-behavior: smooth;
  scroll-snap-type: none;

  /* 闃叉婊氬姩鏃剁殑闂儊鍜屾姈鍔?*/
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);

  /* 浼樺寲婊氬姩鍔ㄧ敾鏇茬嚎 */
  transition: scroll-behavior 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  /* 浼樺寲瑙︽懜婊氬姩 */
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  /* 闃叉杩囧害婊氬姩 */
  overscroll-behavior: contain;
}

/* 娆㈣繋鎻愮ず鏍峰紡 */
.welcome-tips {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx;
  margin: 40rpx 0;
  
  .ai-avatar-large {
    width: 120rpx;
    height: 120rpx;
    border-radius: 60rpx;
    margin-bottom: 20rpx;
    background-color: #fff;
  }
  
  .welcome-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 16rpx;
  }
  
  .welcome-desc {
    font-size: 28rpx;
    color: #666;
    text-align: center;
    margin-bottom: 30rpx;
    line-height: 1.5;
  }
}

/* 鎺ㄨ崘闂鏍峰紡 */
.suggested-questions {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-top: 20rpx;
  
  .question-item {
    background: #F2F7FF;
    border: 2rpx solid rgba(75, 123, 236, 0.2);
    padding: 24rpx;
    border-radius: 16rpx;
    text-align: left;
    font-size: 28rpx;
    color: #4B7BEC;
    position: relative;
    transition: all 0.3s;
    
    &:active {
      transform: scale(0.98);
      background: rgba(75, 123, 236, 0.1);
    }
    
    &::after {
      content: "";
      position: absolute;
      width: 16rpx;
      height: 16rpx;
      border-top: 4rpx solid #4B7BEC;
      border-right: 4rpx solid #4B7BEC;
      transform: rotate(45deg);
      top: 50%;
      right: 24rpx;
      margin-top: -8rpx;
    }
  }
}

/* 鏃堕棿鍒嗗壊绾?*/
.time-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 30rpx 0;
  
  text {
    font-size: 24rpx;
    color: #999;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 4rpx 16rpx;
    border-radius: 10rpx;
  }
}

/* 娑堟伅椤规牱寮?*/
.message-item {
  display: flex;
  margin-bottom: 30rpx;
  position: relative;
  
  .avatar-container {
    width: 80rpx;
    height: 80rpx;
    flex-shrink: 0;
    
    .avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      border: 2rpx solid #f5f5f5;
    }
  }
  
  .message-content {
    max-width: 70%;
    padding: 16rpx;
    border-radius: 16rpx;
    font-size: 28rpx;
    line-height: 1.5;
    word-break: break-all;
    position: relative;

    /* 璞嗗寘寮忔覆鏌撲紭鍖?*/
    contain: layout style paint;
    will-change: contents;

    /* 浼樺寲鏂囧瓧娓叉煋 */
    text-rendering: optimizeSpeed;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    /* 闃叉鎵撳瓧鏃剁殑閲嶆帓 */
    min-height: 1.5em;

    /* 浼樺寲鍔ㄧ敾鎬ц兘 */
    transform: translateZ(0);
    backface-visibility: hidden;
  }
}

/* 鐢ㄦ埛娑堟伅鏍峰紡 */
.user-message {
  flex-direction: row-reverse;
  
  .message-content {
    margin-right: 20rpx;
    background-color: #4B7BEC;
    color: #fff;
    
    &::after {
      content: '';
      position: absolute;
      right: -12rpx;
      top: 20rpx;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 8rpx 0 8rpx 14rpx;
      border-color: transparent transparent transparent #4B7BEC;
    }
  }
}

/* AI娑堟伅鏍峰紡 */
.ai-message {
  flex-direction: row;
  
  .message-content {
    margin-left: 20rpx;
    background-color: #f5f7fa;
    color: #333;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    
    &::after {
      content: '';
      position: absolute;
      left: -12rpx;
      top: 20rpx;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 8rpx 14rpx 8rpx 0;
      border-color: transparent #f5f7fa transparent transparent;
    }
  }
}

/* 鎵撳瓧鎸囩ず鍣ㄦ牱寮?*/
.typing-indicator {
  display: flex;
  align-items: center;
  margin-top: 8rpx;
  
  .typing-dot {
    width: 12rpx;
    height: 12rpx;
    border-radius: 50%;
    background-color: #999;
    margin-right: 6rpx;
    animation: typing-animation 1.5s infinite ease-in-out;
    
    &:nth-child(1) {
      animation-delay: 0s;
    }
    
    &:nth-child(2) {
      animation-delay: 0.2s;
    }
    
    &:nth-child(3) {
      animation-delay: 0.4s;
      margin-right: 0;
    }
  }
  
  @keyframes typing-animation {
    0%, 60%, 100% {
      transform: translateY(0);
      opacity: 0.6;
    }
    30% {
      transform: translateY(-8rpx);
      opacity: 1;
    }
  }
}

/* 搴曢儴鐣欑櫧 - 璞嗗寘寮忔粴鍔ㄩ敋鐐?*/
.bottom-space {
  height: 10rpx;
  width: 100%;
  /* 纭繚婊氬姩閿氱偣鍙 */
  min-height: 1px;
  opacity: 0;
  pointer-events: none;
}

.message-padding {
  height: 30rpx;
  width: 100%;
}

/* 杈撳叆鍖哄煙鏍峰紡 */
.input-container {
  padding: 20rpx 32rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
  display: flex;
  align-items: center;
  position: relative;
  
  .input-type-btn {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
  }
  
  .input-box {
    flex: 1;
    background-color: #F5F7FA;
    border-radius: 36rpx;
    padding: 16rpx 24rpx;
    margin: 0 20rpx;
    max-height: 200rpx;
    overflow-y: auto;
    
    .input-textarea {
      width: 100%;
      min-height: 40rpx;
      max-height: 160rpx;
      font-size: 28rpx;
      line-height: 1.5;
    }
  }
  
  .voice-btn {
    flex: 1;
    height: 72rpx;
    background-color: #F5F7FA;
    border-radius: 36rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 20rpx;
    
    text {
      font-size: 28rpx;
      color: #666;
    }
    
    &:active {
      background-color: #E0E3E8;
    }
  }
  
  .send-btn {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background-color: #CCCCCC;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    transition: all 0.3s;
  }
  
  .send-btn-active {
    background: linear-gradient(135deg, #4B7BEC, #3867D6);
    box-shadow: 0 4rpx 12rpx rgba(56, 103, 214, 0.3);
  }
}

/* 褰曢煶鎻愮ず钂欏眰 */
.recording-mask {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
  
  .recording-indicator {
    width: 300rpx;
    height: 300rpx;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 24rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    
    .recording-wave {
      display: flex;
      align-items: flex-end;
      height: 100rpx;
      margin-bottom: 30rpx;
      
      .wave-item {
        width: 8rpx;
        height: 20rpx;
        background-color: #4B7BEC;
        margin: 0 6rpx;
        border-radius: 4rpx;
        transition: height 0.2s ease-in-out;
      }
    }
    
    .recording-wave-active {
      .wave-item {
        animation: wave-animation 1s infinite ease-in-out;
        
        &:nth-child(1) {
          animation-delay: 0s;
        }
        
        &:nth-child(2) {
          animation-delay: 0.2s;
        }
        
        &:nth-child(3) {
          animation-delay: 0.4s;
        }
        
        &:nth-child(4) {
          animation-delay: 0.6s;
        }
        
        &:nth-child(5) {
          animation-delay: 0.8s;
        }
      }
    }
    
    @keyframes wave-animation {
      0%, 100% {
        height: 20rpx;
      }
      50% {
        height: 60rpx;
      }
    }
    
    text {
      color: #FFFFFF;
      font-size: 32rpx;
      margin-bottom: 20rpx;
    }
    
    .cancel-text {
      color: #CCCCCC;
      font-size: 24rpx;
    }
  }
}

/* ==================== 璞嗗寘寮忔粴鍔ㄤ笓鐢ㄥ姩鐢?==================== */

/* 娑堟伅鍑虹幇鍔ㄧ敾 */
@keyframes message-appear {
  0% {
    opacity: 0;
    transform: translateY(20rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 鎵撳瓧鍐呭鍙樺寲鏃剁殑寰姩鐢?*/
@keyframes content-update {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2rpx);
  }
  100% {
    transform: translateY(0);
  }
}

/* 婊氬姩鎸囩ず鍣ㄥ姩鐢?*/
@keyframes scroll-hint {
  0%, 100% {
    opacity: 0.3;
    transform: translateY(0);
  }
  50% {
    opacity: 0.8;
    transform: translateY(-10rpx);
  }
}

/* 鏂版秷鎭椂鐨勬秷鎭」鍔ㄧ敾 */
.message-item {
  animation: message-appear 0.3s ease-out;
}

/* 鎵撳瓧鏃跺唴瀹圭殑寰姩鐢?*/
.ai-message .message-content {
  transition: all 0.1s ease-out;
}

/* 婊氬姩瀹瑰櫒鐨勫钩婊戣繃娓?*/
.chat-container {
  transition: scroll-behavior 0.3s ease;
}

/* 浼樺寲婊氬姩鏃剁殑鎬ц兘 */
.chat-container::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
}

/* 纭繚鍦ㄤ笉鍚屽钩鍙颁笅婊氬姩鏉￠兘闅愯棌 */
.chat-container {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* 璞嗗寘寮忔粴鍔ㄧ殑鍏抽敭甯т紭鍖?*/
@media (prefers-reduced-motion: no-preference) {
  .chat-container {
    scroll-behavior: smooth;
  }

  .message-content {
    transition: transform 0.1s ease-out;
  }
}

/* 鍑忓皯鍔ㄧ敾鐨勭敤鎴峰亸濂借缃?*/
@media (prefers-reduced-motion: reduce) {
  .chat-container {
    scroll-behavior: auto;
  }

  .message-item {
    animation: none;
  }

  .message-content {
    transition: none;
  }
}
</style>
