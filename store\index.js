import Vue from "vue";
import Vuex from "vuex";
import storage from "@/utils/storage";

Vue.use(Vuex);

const store = new Vuex.Store({
  state: {
	messegeNum: [],
    isShowToast:false, // 是否在展示Toast中
    remark:[], //填写订单备注
    shareLink:"", //分享链接
    verificationKey: "", //获取key表示验证通过
    distributionId:"", //分销员Id 如果当前账户从未登录过时记录
    hasLogin: storage.getHasLogin(),
    userInfo: storage.getUserInfo(),
    uuid: storage.getUuid(),
    token: "",
	waitPay: 0, //未读状态下待支付订单数量
	waitShip: 0, //未读状态下待收货订单数量
	waitEv: 0, //未读状态下待评价订单数量
	waitAfter: 0, //未读状态下售后订单数量
	imMsg: 0, //未读状态下客服消息数量
	mailMsg: 0, //未读状态下站内信消息数量
  },
  mutations: {
    login(state, userInfo) {
      state.userInfo = userInfo || {};
      state.userName =
        userInfo.Name || userInfo.Nickname || userInfo.Username || "匿名用户";
      state.hasLogin = true;
    },
    logout(state) {
      state.userName = "";
      state.hasLogin = false;
    },

    // 设置填写订单中备注
    setRemark(state, remark) {
      state.remark = remark;
    }
  },
  getters: {
	messegeNum: state => {
		return state.messegeNum
	},  
  },
  actions: {},
});

export default store;
