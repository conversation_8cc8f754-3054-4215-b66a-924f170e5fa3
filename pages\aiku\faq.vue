<template>
  <view class="faq-page">
    <!-- 顶部背景图 -->
    <view class="header-bg">
      <image src="https://img.freepik.com/free-vector/gradient-network-connection-background_23-2148865392.jpg" mode="aspectFill"></image>
      <view class="header-title">
        <text>FAQ</text>
      </view>
    </view>

    <!-- FAQ内容区域 -->
    <view class="faq-content">
      <view class="faq-list">
        <!-- FAQ项目 -->
        <view class="faq-item" v-for="(item, index) in faqList" :key="index">
          <view class="question">{{ item.question }}</view>
          <view class="answer">{{ item.answer }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      faqList: [
        {
          question: '1. 什么是《AI库》？',
          answer: '《AI库》是一款集成了人工智能技术的全能助手应用，旨在帮助用户提高工作和生活效率。'
        },
        {
          question: '2.《AI库》有哪些功能？',
          answer: '《AI库》包含多项功能，包括人工智能助手、实时翻译、智能会议支持等。它可以帮助用户管理日程安排、进行语言翻译、提供会议支持等。'
        },
        {
          question: '3. 如何使用《AI库》？',
          answer: '使用《AI库》非常简单。您只需下载并安装应用程序，然后根据提示进行设置。一旦设置完成，您就可以开始使用各种功能了。'
        },
        {
          question: '4.《AI库》的实时翻译功能如何工作？',
          answer: '《AI库》的实时翻译功能利用先进的自然语言处理技术，能够即时将输入的文本或语音翻译成目标语言。用户只需输入要翻译的内容，应用程序将立即提供准确的翻译结果。'
        },
        {
          question: '5.《AI库》的智能会议支持功能有什么优势？',
          answer: '智能会议支持功能能够提供实时会议转录和摘要，帮助会议参与者更好地记录讨论内容。此外，它还可以根据上下文提供建议，帮助会议进行更有效的讨论和决策。'
        },
        {
          question: '6.《AI库》如何保护用户的隐私和数据安全？',
          answer: '《AI库》采取了多种措施来保护用户的隐私和数据安全，包括端到端加密、严格的数据访问控制等。我们始终将用户的隐私放在首位，并致力于确保其数据的安全性和保密性。'
        },
        {
          question: '7.《AI库》是否支持多平台？',
          answer: '是的，《AI库》支持多平台，包括iPhone系统、Android系统等智能手机平台。用户可以根据自己的喜好和需求，在不同设备上使用应用程序，无缝切换并保持一致的体验。'
        },
        {
          question: '8. 我可以在哪里下载《AI库》？',
          answer: '您可以在应用商店（如App Store、其它手机应用市场等）中搜索《AI库》，然后按照提示进行下载和安装。您也可以访问我们的官方网站以获取更多下载信息。'
        },
        {
          question: '9. 我如何联系《AI库》的客户支持团队？',
          answer: '如果您有任何问题或反馈，欢迎随时联系我们的客户支持团队。您可以通过应用内反馈功能、电子邮件或官方网站上的联系表格与我们取得联系。我们将竭诚为您提供帮助和支持。'
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.faq-page {
  min-height: 100vh;
  background-color: #f8f8f8;

  .header-bg {
    position: relative;
    height: 300rpx;
    overflow: hidden;

    image {
      width: 100%;
      height: 100%;
    }

    .header-title {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 40rpx;
      text-align: center;
      
      text {
        font-size: 48rpx;
        color: #fff;
        font-weight: bold;
        text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
      }
    }
  }

  .faq-content {
    margin-top: -50rpx;
    position: relative;
    z-index: 1;
    
    .faq-list {
      padding: 30rpx;
      
      .faq-item {
        background: #fff;
        border-radius: 20rpx;
        padding: 30rpx;
        margin-bottom: 30rpx;
        box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
        
        .question {
          font-size: 32rpx;
          color: #333;
          font-weight: bold;
          margin-bottom: 20rpx;
          line-height: 1.4;
        }
        
        .answer {
          font-size: 28rpx;
          color: #666;
          line-height: 1.6;
        }
        
        &:last-child {
          margin-bottom: 0;
        }
        
        &:hover {
          transform: translateY(-2rpx);
          box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08);
          transition: all 0.3s ease;
        }
      }
    }
  }
}
</style>