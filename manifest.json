{
    "name" : "华上在线",
    "appid" : "__UNI__EF83784",
    "description" : "",
    "versionName" : "4.5.11",
    "versionCode" : 4000514,
    "transformPx" : false,
    "app-plus" : {
        "compatible" : {
            "ignoreVersion" : true //true表示忽略版本检查提示框，HBuilderX1.9.0及以上版本支持  
        },
        /* 5+App特有相关 */
        "usingComponents" : true,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "modules" : {
            "Payment" : {},
            "Share" : {},
            "Geolocation" : {},
            "Maps" : {},
            "Bluetooth" : {},
            "Barcode" : {},
            "Camera" : {},
            "OAuth" : {},
            "Push" : {}
        },
        "error" : {
            /* 404错误页面*/
            "url" : "hybrid/html/error.html"
        },
        /* 模块配置 */
        "distribute" : {
            /* 应用发布信息 */
            "android" : {
                /* android打包配置 */
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_LOCATION_EXTRA_COMMANDS\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH_ADMIN\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH\"/>",
                    "<uses-permission android:name=\"android.permission.INSTALL_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.REQUEST_INSTALL_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.RECEIVE_BOOT_COMPLETED\" />",
                    "<uses-permission android:name=\"android.permission.GET_TASKS\" />"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a", "x86" ],
                "minSdkVersion" : 21,
                "targetSdkVersion" : 30,
                "schemes" : "huashangjishu",
                //安卓自添加 权限  应用市场 过度索取权限
                "permissionExternalStorage" : {
                    "request" : "none",
                    "prompt" : "应用保存运行状态等信息，需要获取读写手机存储（系统提示为访问设备上的照片、媒体内容和文件）权限，请允许。"
                },
                "permissionPhoneState" : {
                    "request" : "none",
                    "prompt" : "为保证您正常、安全地使用，需要获取设备识别码（部分手机提示为获取手机号码）使用权限，请允许"
                },
                "autoSdkPermissions" : false
            },
            //安卓自添加 权限  应用市场 过度索取权限
            "ios" : {
                "idfa" : false,
                "privacyDescription" : {
                    "NSPhotoLibraryUsageDescription" : "用作社区平台发布照片或视频或从相册选择图片作为头像",
                    "NSCameraUsageDescription" : "访问您的相机以提供视频拍摄服务",
                    "NSContactsUsageDescription" : "",
                    "NSBluetoothPeripheralUsageDescription" : "访问您的蓝牙权限用作蓝牙耳机链接",
                    "NSBluetoothAlwaysUsageDescription" : "访问您的蓝牙权限用作蓝牙耳机链接",
                    "NSLocationAlwaysAndWhenInUseUsageDescription" : "需要您提供精确坐标以获取周边蓝牙设备或社区发布定位",
                    "NSMicrophoneUsageDescription" : "使用您的麦克风用作视频拍摄录音用途",
                    "NSLocationWhenInUseUsageDescription" : "需要您提供精确坐标以获取周边蓝牙设备或社区发布定位"
                },
                "urltypes" : "huashangjishu",
                "dSYMs" : false,
                "capabilities" : {
                    "entitlements" : {
                        "com.apple.developer.associated-domains" : [ "applinks:mall-app.huashangjishu.com" ]
                    }
                }
            },
            /* ios打包配置 */
            "sdkConfigs" : {
                "payment" : {
                    "weixin" : {
                        "__platform__" : [ "ios", "android" ],
                        "appid" : "wxf6212b5645b41b1c",
                        // "UniversalLinks" : "https://mall.huashangjishu.com/uni-universallinks/__UNI__EF83784/",
                        "UniversalLinks" : "https://mall-app.huashangjishu.com/app/"
                    },
                    "alipay" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                },
                "ad" : {},
                "share" : {
                    "weixin" : {
                        "appid" : "wxf6212b5645b41b1c",
                        // "UniversalLinks" : "https://mall.huashangjishu.com/uni-universallinks/__UNI__EF83784/"
                        "UniversalLinks" : "https://mall-app.huashangjishu.com/app/"
                    }
                },
                "oauth" : {
                    "weixin" : {
                        "appid" : "wxf6212b5645b41b1c",
                        "UniversalLinks" : "https://mall-app.huashangjishu.com/app/"
                    },
                    "apple" : {}
                },
                "geolocation" : {
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    },
                    "amap" : {
                        "name" : "amap_15875557215ByL2sJUp0",
                        "__platform__" : [ "ios", "android" ],
                        "appkey_ios" : "9af85cb9427981de8e5073c156180345",
                        "appkey_android" : "cda42bd9d75a6136783f3d8e6d7082c3"
                    }
                },
                "maps" : {
                    "amap" : {
                        "appkey_ios" : "9af85cb9427981de8e5073c156180345",
                        "appkey_android" : "cda42bd9d75a6136783f3d8e6d7082c3",
                        "name" : "amap_15875557215ByL2sJUp0"
                    }
                },
                "push" : {}
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "iosStyle" : "common",
                "ios" : {
                    "storyboard" : "CustomStoryboard.zip"
                },
                "androidStyle" : "common",
                "android" : {
                    "hdpi" : "CustomStartPage/start-page.9.png",
                    "xhdpi" : "CustomStartPage/start-page.9.png",
                    "xxhdpi" : "CustomStartPage/start-page.9.png"
                },
                "useOriginalMsgbox" : true
            }
        },
        "nativePlugins" : {
            "JG-JCore" : {
                "JPUSH_APPKEY_ANDROID" : "a6f79ee81916f041dee03e92",
                "JPUSH_APPKEY_IOS" : "a6f79ee81916f041dee03e92",
                "JPUSH_CHANNEL_ANDROID" : "",
                "JPUSH_CHANNEL_IOS" : "",
                "__plugin_info__" : {
                    "name" : "极光推送 JCore 官方 SDK",
                    "description" : "极光推送 JCore 官方 SDK HBuilder 插件版本",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=4028",
                    "android_package_name" : "com.hskj.earphone.platform",
                    "ios_bundle_id" : "com.ios.hssky",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "4028",
                    "parameters" : {
                        "JPUSH_APPKEY_ANDROID" : {
                            "des" : "[Android]极光portal配置应用信息时分配的AppKey",
                            "key" : "JPUSH_APPKEY",
                            "value" : "a6f79ee81916f041dee03e92"
                        },
                        "JPUSH_APPKEY_IOS" : {
                            "des" : "[iOS]极光portal配置应用信息时分配的AppKey",
                            "key" : "JCore:APP_KEY",
                            "value" : "a6f79ee81916f041dee03e92"
                        },
                        "JPUSH_CHANNEL_ANDROID" : {
                            "des" : "[Android]用于统计分发渠道，不需要可填默认值developer-default",
                            "key" : "JPUSH_CHANNEL",
                            "value" : ""
                        },
                        "JPUSH_CHANNEL_IOS" : {
                            "des" : "[iOS]用于统计分发渠道，不需要可填默认值developer-default",
                            "key" : "JCore:CHANNEL",
                            "value" : ""
                        }
                    }
                }
            },
            "JG-JPush" : {
                "JPUSH_ADVERTISINGID_IOS" : "",
                "JPUSH_DEFAULTINITJPUSH_IOS" : "",
                "JPUSH_GOOGLE_API_KEY" : "",
                "JPUSH_GOOGLE_APP_ID" : "",
                "JPUSH_GOOGLE_PROJECT_ID" : "",
                "JPUSH_GOOGLE_PROJECT_NUMBER" : "",
                "JPUSH_GOOGLE_STORAGE_BUCKET" : "",
                "JPUSH_HONOR_APPID" : "",
                "JPUSH_HUAWEI_APPID" : "",
                "JPUSH_ISPRODUCTION_IOS" : "true",
                "JPUSH_MEIZU_APPID" : "",
                "JPUSH_MEIZU_APPKEY" : "",
                "JPUSH_NIO_APPID" : "",
                "JPUSH_OPPO_APPID" : "",
                "JPUSH_OPPO_APPKEY" : "",
                "JPUSH_OPPO_APPSECRET" : "",
                "JPUSH_VIVO_APPID" : "",
                "JPUSH_VIVO_APPKEY" : "",
                "JPUSH_XIAOMI_APPID" : "",
                "JPUSH_XIAOMI_APPKEY" : "",
                "__plugin_info__" : {
                    "name" : "极光推送 JPush 官方 SDK",
                    "description" : "极光推送JPush官方SDK HBuilder插件版本",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=4035",
                    "android_package_name" : "com.hskj.earphone.platform",
                    "ios_bundle_id" : "com.ios.hssky",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "4035",
                    "parameters" : {
                        "JPUSH_ADVERTISINGID_IOS" : {
                            "des" : "[iOS]广告标识符（IDFA）如果不需要使用IDFA，可不填",
                            "key" : "JPush:ADVERTISINGID",
                            "value" : ""
                        },
                        "JPUSH_DEFAULTINITJPUSH_IOS" : {
                            "des" : "[iOS]是否默认初始化，是填true，不是填false或者不填",
                            "key" : "JPush:DEFAULTINITJPUSH",
                            "value" : ""
                        },
                        "JPUSH_GOOGLE_API_KEY" : {
                            "des" : "厂商google api_key,示例:g-12346578",
                            "key" : "google_api_key",
                            "value" : ""
                        },
                        "JPUSH_GOOGLE_APP_ID" : {
                            "des" : "厂商google mobilesdk_app_id,示例：g-12346578",
                            "key" : "google_app_id",
                            "value" : ""
                        },
                        "JPUSH_GOOGLE_PROJECT_ID" : {
                            "des" : "厂商google project_id ,示例：g-12346578",
                            "key" : "project_id",
                            "value" : ""
                        },
                        "JPUSH_GOOGLE_PROJECT_NUMBER" : {
                            "des" : "厂商google project_number,示例：g-12346578",
                            "key" : "gcm_defaultSenderId",
                            "value" : ""
                        },
                        "JPUSH_GOOGLE_STORAGE_BUCKET" : {
                            "des" : "厂商google storage_bucket,示例：g-12346578",
                            "key" : "google_storage_bucket",
                            "value" : ""
                        },
                        "JPUSH_HONOR_APPID" : {
                            "des" : "厂商HONOR-appId,示例：12346578",
                            "key" : "com.hihonor.push.app_id",
                            "value" : ""
                        },
                        "JPUSH_HUAWEI_APPID" : {
                            "des" : "厂商HUAWEI-appId,示例：appid=12346578",
                            "key" : "com.huawei.hms.client.appid",
                            "value" : ""
                        },
                        "JPUSH_ISPRODUCTION_IOS" : {
                            "des" : "[iOS]是否是生产环境，是填true，不是填false或者不填",
                            "key" : "JPush:ISPRODUCTION",
                            "value" : ""
                        },
                        "JPUSH_MEIZU_APPID" : {
                            "des" : "厂商MEIZU-appId,示例：MZ-12345678",
                            "key" : "MEIZU_APPID",
                            "value" : ""
                        },
                        "JPUSH_MEIZU_APPKEY" : {
                            "des" : "厂商MEIZU-appKey,示例：MZ-12345678",
                            "key" : "MEIZU_APPKEY",
                            "value" : ""
                        },
                        "JPUSH_NIO_APPID" : {
                            "des" : "厂商nio-appId,示例：12346578",
                            "key" : "nio_push_app_id",
                            "value" : ""
                        },
                        "JPUSH_OPPO_APPID" : {
                            "des" : "厂商OPPO-appId,示例：OP-12345678",
                            "key" : "OPPO_APPID",
                            "value" : ""
                        },
                        "JPUSH_OPPO_APPKEY" : {
                            "des" : "厂商OPPO-appkey,示例：OP-12345678",
                            "key" : "OPPO_APPKEY",
                            "value" : ""
                        },
                        "JPUSH_OPPO_APPSECRET" : {
                            "des" : "厂商OPPO-appSecret,示例：OP-12345678",
                            "key" : "OPPO_APPSECRET",
                            "value" : ""
                        },
                        "JPUSH_VIVO_APPID" : {
                            "des" : "厂商VIVO-appId,示例：12345678",
                            "key" : "com.vivo.push.app_id",
                            "value" : ""
                        },
                        "JPUSH_VIVO_APPKEY" : {
                            "des" : "厂商VIVO-appkey,示例：12345678",
                            "key" : "com.vivo.push.api_key",
                            "value" : ""
                        },
                        "JPUSH_XIAOMI_APPID" : {
                            "des" : "厂商XIAOMI-appId,示例：MI-12345678",
                            "key" : "XIAOMI_APPID",
                            "value" : ""
                        },
                        "JPUSH_XIAOMI_APPKEY" : {
                            "des" : "厂商XIAOMI-appKey,示例：MI-12345678",
                            "key" : "XIAOMI_APPKEY",
                            "value" : ""
                        }
                    }
                }
            }
        }
    },
    "permission" : {
        "scope.userLocation" : {
            "desc" : "你的位置信息将用于高德地图的效果展示"
        }
    },
    /* SDK配置 */
    "quickapp" : {},
    /* 快应用特有相关 */
    "mp-weixin" : {
        /* 小程序特有相关 */
        "usingComponents" : true,
        "appid" : "wx6f10f29075dc1b0b",
        "optimization" : {
            "subPackages" : true
        },
        "setting" : {
            "urlCheck" : false,
            "minified" : true,
            "postcss" : false,
            "es6" : true
        },
        "permission" : {
            "scope.userLocation" : {
                "desc" : "位置信息将用于高德地图的效果展示"
            }
        },
        "plugins" : {},
        // 直播插件注释
        // "live-player-plugin" : {
        //     "version" : "1.3.0",
        //     "provider" : "wx2b03c6e691cd7370"
        // }
        "requiredPrivateInfos" : [ "chooseLocation", "getLocation" ]
    },
    "h5" : {
        "devServer" : {
            "disableHostCheck" : true
        },
        "router" : {
            "mode" : "history",
            "base" : ""
        },
        "sdkConfigs" : {
            "maps" : {}
        },
        "optimization" : {
            "treeShaking" : {
                "enable" : true
            }
        },
        "title" : "华上在线",
        "template" : ""
    },
    "locale" : "zh-Hans",
    "fallbackLocale" : "zh-Hans",
    "_spaceID" : "mp-a393a77a-6566-4f28-be1e-106cc1f41a70",
    "sassImplementationName" : "node-sass"
}
