<!DOCTYPE html>
<html>

	<head>
		<meta charset="utf-8">
		<title></title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">

		<!--标准mui.css-->
		<link rel="stylesheet" href="../css/mui.min.css">
		<!--App自定义的css-->
		<!--<link rel="stylesheet" type="text/css" href="../css/app.css"/>-->
		<style>
			.mui-row.mui-fullscreen>[class*="mui-col-"] {
				height: 100%;
			}

			.title {
				margin: 20px 15px 10px;
				color: #6d6d72;
				font-size: 15px;
			}

			.oa-contact-cell.mui-table .mui-table-cell {
				padding: 11px 0;
				vertical-align: middle;
			}

			.oa-contact-cell {
				position: relative;
				margin: -11px 0;
			}

			.mui-checkbox {

				width: 20px;
				height: 20px;
				margin: 10px;
			}

			#head {
				line-height: 20px;
			}

			.head-img {
				width: 32px;
				height: 32px;
				margin: 10px;
			}

			#head-img1 {
				position: absolute;
				bottom: 10px;
				right: 40px;
				width: 40px;
				height: 40px;
			}

			.oa-contact-avatar {
				width: 75px;
			}

			.oa-contact-avatar img {
				border-radius: 50%;
			}

			.oa-contact-content {
				width: 100%;
			}

			.oa-contact-name {
				margin-right: 20px;
			}

			.oa-contact-name,
			oa-contact-position {
				float: left;
			}
		</style>

	</head>
	<body>
		<header class="mui-bar mui-bar-nav">
			<a onClick="javascript:history.back(-1);" class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></a>
			<h1 id="name" class="mui-title">音效选择</h1>
		</header>
		<div class="mui-content">
			<div class="mui-card">
				<ul id="eqlist" class="mui-table-view mui-table-view-radio">
					<li class="mui-table-view-cell">
						<a class="mui-navigate-right">
							均衡
						</a>
					</li>
					<li class="mui-table-view-cell ">
						<a class="mui-navigate-right">
							低音增强
						</a>
					</li>

					<li class="mui-table-view-cell ">
						<a class="mui-navigate-right">
							柔和高音
						</a>
					</li>

					<li class="mui-table-view-cell ">
						<a class="mui-navigate-right">
							人声增强
						</a>
					</li>

					<li class="mui-table-view-cell ">
						<a class="mui-navigate-right">
							轻音乐
						</a>
					</li>

					<li class="mui-table-view-cell ">
						<a class="mui-navigate-right">
							高音&低音
						</a>
					</li>

				</ul>
			</div>
		</div>
		</div>
	</body>
	<script src="../js/mui.min.js"></script>
	<script src="../js/device.js"></script>
	<script type="text/javascript" src="https://unpkg.com/@dcloudio/uni-webview-js@0.0.3/index.js"></script>
	<script>
		// 在引用依赖的文件后，需要在 HTML 中监听 UniAppJSBridgeReady 事件触发后，才能安全调用 uni 的 API。  
		document.addEventListener('UniAppJSBridgeReady', function() {
		
			uni.getEnv(function(res) {
				console.log('获取当前环境：' + JSON.stringify(res));
			});
		});
		function SPP_sendAT(message) {
			console.log(message);
			// plus.webview.currentWebview().close();

			uni.postMessage({
				data: {
					instructions: message, // 这是传的参数
				},
			});
			
			
			uni.postMessage({
				data: {
					editStore: {
						idx: 7,
						idxName: 'CH',
						data: [localStorage.getItem('CH_modle')],
					}, // 这是传的参数
				},
			});
		}
		
		mui.init({
			swipeBack: false, //启用右滑关闭功能

		});
		document.getElementById('eqlist').addEventListener('selected', function(e) {
			console.log("当前选中的文本值为:" + e.detail.el.innerText);
			selectEQ(e.detail.el.innerText)

			localStorage.setItem('CH_modle_name', e.detail.el.innerText)


		});

		document.querySelector('.mui-table-view.mui-table-view-radio ' + "li:nth-child(" + (Number(localStorage.getItem('CH_modle')) + 1) +
			')').classList.add('mui-selected');

		function selectEQ(eq_str) {
			switch (eq_str) {
				case "均衡":
					localStorage.setItem('CH_modle', "0");
					SPP_sendAT("AT+CH=0\r\n");
					break;
				case "低音增强":
					localStorage.setItem('CH_modle', "1");
					SPP_sendAT("AT+CH=1\r\n");
					break;
				case "柔和高音":
					localStorage.setItem('CH_modle', "2");
					SPP_sendAT("AT+CH=2\r\n");
					break;
				case "人声增强":
					localStorage.setItem('CH_modle', "3");
					SPP_sendAT("AT+CH=3\r\n");
					break;
				case "轻音乐":
					localStorage.setItem('CH_modle', "4");
					SPP_sendAT("AT+CH=4\r\n");
					break;
				case "高音&低音":
					localStorage.setItem('CH_modle', "5");
					SPP_sendAT("AT+CH=5\r\n");
					break;
			}

		};
	</script>
</html>