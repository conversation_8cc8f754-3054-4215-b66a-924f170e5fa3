<template>
	<view class="content">
		<u-navbar title="设置" leftIcon="arrow-left" :autoBack="true" :placeholder="true" bgColor="#fff"></u-navbar>
		<view class="tab" style="background-color: #f9f9f9;">
			<view class="on" style="width: 650rpx;">基础设置</view>
		</view>
		<view class="tab">
			<image src="/static/aiku/sz1.svg" mode=""></image>
			<view class="on" style="width: 300rpx">音量调节</view>
			<view class="volume">
				<u-icon name="minus" @click="computedWidth('minus')"></u-icon>
				<view style="width: 200rpx;"> <u-slider v-model="volumevalue" min="0" max="150" activeColor="#3c9cff"step="30" @change="volumechange"></u-slider></view>
				<u-icon name="plus" @click="computedWidth('plus')"></u-icon>
			</view>
		</view>
		<view class="tab">
			<image src="/static/aiku/sz2.svg" mode=""></image>
			<view class="on" style="width: 340rpx">字体大小</view>
			<view class="volume" style="width: 180rpx">
				<u-number-box :min="20" :max="50" button-size="36" color="#fff" bgColor="#2979ff"
					iconStyle="color: #fff" @change="fontchange" v-model="fontvalue" buttonSize="45rpx"></u-number-box>

			</view>
		</view>
		<!-- <view class="tab">
			<image src="../../static/sz3.svg" mode=""></image>
			<view class="on" style="width: 460rpx">自动结束拾音</view>
			<view class="volume"  style="width: 60rpx">
				<u-switch v-model="syvaluesy" size="20" ></u-switch>
			</view>
		</view> -->
		<view class="tab" style="background-color: #f9f9f9;">
			<view class="on" style="width: 650rpx;">对话设置</view>
		</view>
		<view class="tab">
			<image src="/static/aiku/sz4.svg" mode=""></image>
			<view class="on" style="width: 460rpx">只翻译不播放</view>
			<view class="volume"  style="width: 60rpx">
				<u-switch v-model="bfvaluesy" size="20" ></u-switch>
			</view>
		</view>
		<view class="tab">
			<image src="/static/aiku/sz5.svg" mode=""></image>
			<view class="on" style="width: 570rpx">清空翻译对话记录</view>
<!-- 			<image src="../../static/right.svg" mode="" class="right"></image> -->
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				volumevalue: 0, //音量值
				fontvalue:"",//字体大小
				syvaluesy:true,//拾音开关
				bfvaluesy:false,//只翻译不播放开关
				ttsModule:"",
			}
		},
		onLoad() {
			// 获取当前系统音量并设置为滑动条的初始值
			    this.fontvalue = uni.getStorageSync('fontsizee');
				// plus.device.getVolume() // 获取当前音量
				this.ttsModule = uni.requireNativePlugin("hm-tts-TtsModule")
				this.ttsModule.init((ret) => {});
				var ret = this.ttsModule.getVolume();
				this.volumevalue = ret.currentVolume
				console.log(ret);
			    // 监听系统音量变化
			    // plus.device.addEventListener('volumechange', (e) => {
			    //   this.volumevalue = e.volume; // 更新滑动条的值
			    // });
				
				
				 var intervalId =setInterval(() => {
				var ret = this.ttsModule.getVolume();
				 const currentVolume = ret.currentVolume
				 if (currentVolume !== this.volumevalue) {
				    this.volumevalue = currentVolume;
				 }
				}, 100);
		},
		onHide() {
			clearInterval(intervalId);
		},
		methods: {
			//调节字体大小
			fontchange(e){
				console.log(this.fontvalue);
				uni.setStorageSync('fontsizee', this.fontvalue);
			},
			//调节音量大小
			computedWidth(type) {
				if (type === 'plus') {
					this.volumevalue = uni.$u.range(0, 150, this.volumevalue + 15)
					this.volumechange(this.volumevalue)
					
					// 调用原生插件的setVolume方法来设置音量

				} else {
					this.volumevalue = uni.$u.range(0, 150, this.volumevalue - 15)
					
					this.volumechange(this.volumevalue)
					// 调用原生插件的setVolume方法来设置音量

				}
			},
			
			volumechange(e){
				this.volumevalue = e; // 更新音量值
				// 调用原生API设置音量
				// plus.device.setVolume(50);
				
				this.ttsModule.setVolume({'volume': this.volumevalue});
			}
		}
	}
</script>

<style lang="scss">
	page {
		height: 100%;
		background-color: #fff;
	}

	.content {
		.tab {
			width: 100%;
			height: 90rpx;
			display: flex;
			align-items: center;
			justify-content: space-evenly;
			flex-direction: row;
			font-size: 26rpx;
			background-color: #fff;

			.on {
				width: 500rpx;
			}

			image {
				width: 50rpx;
				height: 50rpx;
			}

			.right {
				width: 30rpx;
				height: 30rpx;
			}

			.volume {
				width: 220rpx;
				display: flex;
			}
		}
	}
</style>