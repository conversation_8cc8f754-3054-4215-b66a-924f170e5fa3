function checkIsAppleDevice() {
	var u = navigator.userAgent,
		app = navigator.appVersion;
	var ios = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
	var iPad = u.indexOf("iPad") > -1;
	var iPhone = u.indexOf("iPhone") > -1 || u.indexOf("Mac") > -1;
	if (ios || iPad || iPhone) {
		return true;
	} else {
		return false;
	}
}
//js判断是否为Android设备
function checkIsAndroidDevice() {
	var u = navigator.userAgent;
	if (u.indexOf("Android") > -1 || u.indexOf("Adr") > -1) {
		return true;
	} else {
		return false;
	}
}
//js判断是否为鸿蒙系统 chos是鸿蒙webview的标识
function checkIsHarmonyOS() {
	var u = navigator.userAgent;
	if (u.indexOf("ohos") > -1) {
		return true;
	} else {
		return false;
	}
}