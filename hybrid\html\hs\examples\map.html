<!doctype html>
<html>

	<head>
		<meta charset="utf-8">
		<title></title>
		<meta name="viewport"
			content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
		<link href="css/mui.css" rel="stylesheet" />
		<!--App自定义的css-->
		<link rel="stylesheet" type="text/css" href="../css/app.css" />
		<script src="js/mui.js"></script>
		<script type="text/javascript">
			mui.init()
		</script>
		<style>
			body,
			html,
			#container {
				overflow: scroll;
				width: 100%;
				height: 100%;
				margin: 0;
				font-family: "微软雅黑";

			}

			#content {
				z-index: 999;
				position: absolute;
				background-color: transparent;
				margin-top: 28px;
			}
		</style>
		<script src="http://api.map.baidu.com/api?type=webgl&v=3.0&ak=ivyBKb8CFG5kRDmx8Nlkmo12mmk7QX9P"></script>
	</head>

	<body>
		<div class="mui-content" id="content">
			<div style="mui-input-row">
				<input type="input" placeholder="请输入城市名称" style="width:70%;" id="cityName" />
				<button class="mui-btn mui-btn-primary" style="margin-top: -4px;" id="search">查 询</button>
			</div>
			<div style="mui-input-row">
				<button class="mui-btn mui-btn-red" style="margin-buttom: 4px;" id="location">定 位</button>
			</div>

		</div>
		<div id="container"></div>

		<script>
			var map = new BMapGL.Map('container'); // 创建Map实例
			var gc = new BMapGL.Geocoder();
			//城市名称，需要以 市 结尾，地图级别最大21
			map.centerAndZoom('北京市', 12); // 初始化地图,设置中心点坐标和地图级别
			map.enableScrollWheelZoom(true); // 开启鼠标滚轮缩放
			map.addEventListener('click', function(e) {
				if (e.latlng.lng != "" && e.latlng.lat != "") {
					map.clearOverlays();
					var new_point = new BMapGL.Point(e.latlng.lng, e.latlng.lat);
					var marker = new BMapGL.Marker(new_point); // 创建标注
					map.addOverlay(marker); // 将标注添加到地图中
					map.panTo(new_point);
				}
			});
			document.getElementById('search').addEventListener('tap', function() {
				var cityName = document.getElementById('cityName').value;
				map.centerAndZoom(cityName + '市', 12);
			});

			function getNowLocation() {
				var geolocation = new BMapGL.Geolocation();
				geolocation.getCurrentPosition(function(r) {
					if (this.getStatus() == BMAP_STATUS_SUCCESS) {
						map.clearOverlays();
						var mk = new BMapGL.Marker(r.point);
						map.addOverlay(mk);
						map.panTo(r.point);
						//alert('您的位置：' + r.point.lng + ',' + r.point.lat);
						map.setCenter(r.point);
						gc.getLocation(r.point, function(rs) {
							var addComp = rs.addressComponents;
							mui.alert(addComp.province + addComp.city + addComp.district + addComp.street +
								addComp.streetNumber);
						});
					} else {
						alert('failed' + this.getStatus());
					}
				});
			}
			mui.plusReady(function() {
				getNowLocation();
			});
			document.getElementById("location").addEventListener("tap", function() {
				getNowLocation();
			});
		</script>

		<div class="mui-content" style="margin-height: 90%;">
			<div class="title">
				我的耳机
			</div>
			<ul class="mui-table-view">
				<li class="mui-table-view-cell">Item 1</li>
				<li class="mui-table-view-cell">Item 2</li>
				<li class="mui-table-view-cell">Item 3</li>
				<li class="mui-table-view-cell">Item 3</li>
			</ul>
		</div>
	</body>
</html>