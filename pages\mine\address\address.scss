.empty {
    margin-top: 200rpx !important;
  }
  .alifont {
    display: inline-block;
  }
  
  .region {
    span {
      margin: 0 4rpx !important;
    }
  }
  .address {
    .default {
      border: 1px solid #ff6262;
      color: #ff6262;
      font-size: 22rpx;
      border-radius: 6rpx;
      align-self: center;
      padding: 2rpx 20rpx;
    }
    .list {
      .item:hover {
        background: #ededed;
      }
  
      .item {
        margin-top: 20rpx;
        font-size: $font-base;
        color: #666;
  
        .basic {
          padding: 30rpx;
          line-height: 1.5em;
          border-bottom: 1px solid $border-color-light;
  
          :nth-child(2) {
            margin: 0 20rpx;
          }
  
          :nth-child(4) {
            color: $font-color-light;
            font-size: $font-sm;
  
            margin-top: 10rpx;
  
            text:nth-child(2) {
              margin: 0;
            }
  
            view {
              font-size: 28rpx;
            }
          }
        }
  
        .edit {
          display: flex;
          justify-content: space-between;
          align-items: center;
          vertical-align: middle;
          height: 80rpx;
          font-size: $font-sm;
          color: $font-color-light;
          padding: 0 30rpx;
  
          .unchecked {
            width: 28rpx;
            height: 28rpx;
            border-radius: 50%;
            border: 1px solid #e0e0e0;
            display: inline-block;
            vertical-align: middle;
            margin-right: 8rpx;
            position: relative;
            top: -2rpx;
            left: 0;
          }
  
          view:nth-child(1) {
            view:nth-child(1) {
              font-size: $font-base;
              color: $main-color;
              margin-right: 8rpx;
              vertical-align: middle;
            }
          }
  
          view:nth-child(2) {
            text {
              margin-left: 5rpx;
            }
  
            .alifont {
              font-size: 32rpx;
            }
  
            .icon-bianji-copy {
              font-size: 28rpx;
              position: relative;
              top: 2rpx;
              left: 0;
            }
  
            .icon-lajitong {
              position: relative;
              top: 4rpx;
            }
          }
  
          .mr-40 {
            margin-right: 40rpx;
          }
        }
      }
    }
  
    .btn {
      background: $light-color;
      position: fixed;
      width: 690rpx;
      bottom: 60rpx;
      height: 80rpx;
      left: 30rpx;
      font-size: 30rpx;
      line-height: 80rpx;
  
      .u-icon {
        margin-right: 10rpx;
      }
    }
  }