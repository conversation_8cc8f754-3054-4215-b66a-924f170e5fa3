/**
 * 星火认知大模型API对接
 * 版本：Spark Lite
 * 功能：提供与星火大模型的WebSocket通信接口
 */
import CryptoJS from 'crypto-js'; // 使用crypto-js库处理加密

// 星火大模型配置
const sparkConfig = {
  // API接口地址 - Spark Lite版本
  apiUrl: 'wss://spark-api.xf-yun.com/v1.1/chat',
  // 应用配置 - 请替换为实际的应用信息
  appId: '34d1587e', // 需要替换为实际的AppID
  apiKey: '5be6f357e6d8526a61b8541d8127f887', // 需要替换为实际的APIKey
  apiSecret: 'Y2NmNmM0MTUwM2FiYmExOGQ2YmY0YmRm', // 需要替换为实际的APISecret
  // 模型配置
  domain: 'lite', // lite版本
  temperature: 0.5, // 核采样阈值
  maxTokens: 4096, // 最大生成token数
  modelName: '小叶同学', // 模型名称
}

/**
 * 星火认知大模型API封装类
 */
class SparkAiApi {
  constructor() {
    this.ws = null;
    this.connected = false;
    this.callbacks = {
      onMessage: null,
      onError: null,
      onClose: null,
      onStream: null,
      onComplete: null,
    };
    this.messageHistory = []; // 消息历史
    this.maxHistoryLength = 10; // 保留的最大历史消息数量
  }
  
  /**
   * 设置回调函数
   * @param {Object} callbacks 回调函数集合
   */
  setCallbacks(callbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }
  
  /**
   * 生成RFC1123格式的日期
   * @returns {string} RFC1123格式的日期
   */
  getDate() {
    const date = new Date();
    const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    
    return days[date.getUTCDay()] + ", " +
      date.getUTCDate().toString().padStart(2, '0') + " " +
      months[date.getUTCMonth()] + " " +
      date.getUTCFullYear() + " " +
      date.getUTCHours().toString().padStart(2, '0') + ":" +
      date.getUTCMinutes().toString().padStart(2, '0') + ":" +
      date.getUTCSeconds().toString().padStart(2, '0') + " " +
      "GMT";
  }
  
  /**
   * 生成鉴权URL
   * @returns {string} 鉴权后的URL
   */
  genAuthUrl() {
    // 获取配置
    const { apiUrl, appId, apiKey, apiSecret } = sparkConfig;
    
    // 提取host和path
    const url = new URL(apiUrl);
    const host = url.hostname;
    const path = url.pathname;
    
    // 生成RFC1123格式的日期
    const date = this.getDate();
    
    // 拼接字符串
    let tmp = "host: " + host + "\n";
    tmp += "date: " + date + "\n";
    tmp += "GET " + path + " HTTP/1.1";
    
    // 使用hmacSHA256算法结合APISecret对tmp签名
    const signature = CryptoJS.HmacSHA256(tmp, apiSecret);
    const signatureBase64 = CryptoJS.enc.Base64.stringify(signature);
    
    // 拼接authorization_origin
    const authorizationOrigin = 
      `api_key="${apiKey}", algorithm="hmac-sha256", headers="host date request-line", signature="${signatureBase64}"`;
    
    // Base64编码生成authorization
    const authorization = btoa(authorizationOrigin);
    
    // 拼接url参数
    const params = {
      authorization,
      date,
      host
    };
    
    // 生成最终URL
    const authUrl = apiUrl + '?' + Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');
    
    return authUrl;
  }
  
  /**
   * 添加消息到历史记录
   * @param {Object} message 消息对象
   */
  addMessageToHistory(message) {
    // 添加新消息
    this.messageHistory.push(message);
    
    // 如果超过最大长度，移除最早的消息
    if (this.messageHistory.length > this.maxHistoryLength) {
      this.messageHistory.shift();
    }
  }
  
  /**
   * 清空历史消息
   */
  clearHistory() {
    this.messageHistory = [];
  }
  
  /**
   * 构建WebSocket请求数据
   * @param {string} content 用户输入内容
   * @returns {Object} WebSocket请求数据
   */
  buildRequestData(content) {
    const { appId, domain, temperature, maxTokens } = sparkConfig;
    
    // 构建请求消息数组
    const text = [];
    
    // 添加历史消息
    this.messageHistory.forEach(msg => {
      text.push({
        role: msg.role,
        content: msg.content
      });
    });
    
    // 添加当前用户消息
    text.push({
      role: 'user',
      content: content
    });
    
    // 构建完整请求体
    return {
      header: {
        app_id: appId,
        uid: `user_${Date.now()}`
      },
      parameter: {
        chat: {
          domain,
          temperature,
          max_tokens: maxTokens
        }
      },
      payload: {
        message: {
          text
        }
      }
    };
  }
  
  /**
   * 连接WebSocket
   * @returns {Promise} 连接结果Promise
   */
  connect() {
    return new Promise((resolve, reject) => {
      try {
        // 生成鉴权URL
        const authUrl = this.genAuthUrl();
        
        // 创建WebSocket连接
        this.ws = uni.connectSocket({
          url: authUrl,
          success: () => {
            console.log('WebSocket连接创建成功');
          },
          fail: (err) => {
            console.error('WebSocket连接创建失败', err);
            reject(err);
          }
        });
        
        // 监听WebSocket事件
        uni.onSocketOpen(() => {
          console.log('WebSocket连接已打开');
          this.connected = true;
          resolve(true);
        });
        
        uni.onSocketError((err) => {
          console.error('WebSocket错误', err);
          this.connected = false;
          if (this.callbacks.onError) {
            this.callbacks.onError(err);
          }
          reject(err);
        });
        
        uni.onSocketClose(() => {
          console.log('WebSocket连接已关闭');
          this.connected = false;
          if (this.callbacks.onClose) {
            this.callbacks.onClose();
          }
        });
        
        uni.onSocketMessage((res) => {
          try {
            // 解析返回的数据
            const data = JSON.parse(res.data);
            
            // 处理返回的数据
            this.handleResponse(data);
          } catch (error) {
            console.error('处理WebSocket消息时出错', error);
            if (this.callbacks.onError) {
              this.callbacks.onError(error);
            }
          }
        });
        
      } catch (err) {
        console.error('创建WebSocket连接时出错', err);
        reject(err);
      }
    });
  }
  
  /**
   * 关闭WebSocket连接
   */
  closeConnection() {
    if (this.ws && this.connected) {
      uni.closeSocket({
        success: () => {
          console.log('WebSocket连接已关闭');
          this.connected = false;
        },
        fail: (err) => {
          console.error('关闭WebSocket连接失败', err);
        }
      });
    }
  }
  
  /**
   * 发送消息到星火大模型
   * @param {string} content 用户消息内容
   * @returns {Promise} 发送结果Promise
   */
  sendMessage(content) {
    return new Promise(async (resolve, reject) => {
      try {
        // 如果未连接，先建立连接
        if (!this.connected) {
          await this.connect();
        }
        
        // 构建请求数据
        const requestData = this.buildRequestData(content);
        
        // 发送数据
        uni.sendSocketMessage({
          data: JSON.stringify(requestData),
          success: () => {
            console.log('WebSocket消息发送成功');
            resolve(true);
          },
          fail: (err) => {
            console.error('WebSocket消息发送失败', err);
            reject(err);
          }
        });
        
        // 将用户消息添加到历史记录
        this.addMessageToHistory({
          role: 'user',
          content: content
        });
        
      } catch (err) {
        console.error('发送消息时出错', err);
        reject(err);
      }
    });
  }
  
  // 用于记录星火大模型的流式响应内容
  currentResponseContent = '';
  
  /**
   * 处理WebSocket返回的响应
   * @param {Object} data 响应数据
   */
  handleResponse(data) {
    try {
      // 检查是否有错误
      if (data.header.code !== 0) {
        console.error('星火大模型返回错误', data.header);
        if (this.callbacks.onError) {
          this.callbacks.onError(new Error(`星火大模型返回错误: ${data.header.message}`));
        }
        return;
      }
      
      // 获取返回的状态
      const status = data.header.status;
      
      // 处理返回的内容
      if (data.payload.choices && data.payload.choices.text && data.payload.choices.text.length > 0) {
        const textResult = data.payload.choices.text[0];
        const currentChunk = textResult.content;
        
        // 根据状态处理内容
        if (status === 0) { // 首条消息，重置累积内容
          this.currentResponseContent = currentChunk;
        } else { // 后续消息，累积内容
          this.currentResponseContent += currentChunk;
        }
        
        // 记录流式响应内容（仅供调试）
        console.log('星火AI当前片段:', currentChunk);
        console.log('星火AI累积内容:', this.currentResponseContent);
        
        // 每收到一个片段就立即通知回调，传递当前片段内容，确保实时流式显示
        if (this.callbacks.onStream) {
          // 第一个参数为当前接收到的完整内容，用于显示
          // 第二个参数表示是否为最后一条消息
          this.callbacks.onStream(this.currentResponseContent, status === 2);
        }
        
        // 如果是最后一条消息，添加到历史记录并通知完成
        if (status === 2) {
          // 添加到历史记录
          this.addMessageToHistory({
            role: 'assistant',
            content: this.currentResponseContent
          });
          
          // 调用完成回调
          if (this.callbacks.onComplete) {
            this.callbacks.onComplete(this.currentResponseContent);
          }
          
          // 获取token使用情况
          if (data.payload.usage && this.callbacks.onMessage) {
            this.callbacks.onMessage({
              type: 'usage',
              data: data.payload.usage.text
            });
          }
        }
      }
      
    } catch (error) {
      console.error('处理响应时出错', error);
      if (this.callbacks.onError) {
        this.callbacks.onError(error);
      }
    }
  }
}

// 导出星火AI实例
const sparkAi = new SparkAiApi();
export default sparkAi;
