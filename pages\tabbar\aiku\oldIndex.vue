<template>
	<view class="content">
		<view class="top">
			<text class="title">
				一键翻译
			</text>
			<view class="img">
				<image src="/static/aiku/yi.webp" mode=""></image>
			</view>
		</view>
		
		<view class="bot">
			<view class="earphones" @click="toearphones">
				<image src="/static/aiku/earphones.svg" mode=""></image>
				翻译耳机
			</view>
			<view class="earphones">
				<image src="/static/aiku/stick.svg" mode=""></image>
				蓝牙翻译棒
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		onLoad() {
			// 尝试获取本地数据
			const myData = uni.getStorageSync('targetlanguagee');
			const fontData = uni.getStorageSync('fontsizee');
			 
			// 检查语种数据是否存在
			if (!myData) {
				//不存在
				uni.setStorageSync('targetlanguagee', "英语");
				uni.setStorageSync('sourcelanguagee', "中文");
			}
			// 检查字体大小数据是否存在
			if (!fontData) {
				//不存在
				uni.setStorageSync('fontsizee', 30);
			}
		},
		methods: {
			toearphones(){
				uni.navigateTo({
					url:"/pages/select/earphones"
				})
			}
		}
	}
</script>

<style lang="scss">
	page{
		height: 100%;
		background-color: #f8f8f8;
	}
	.content{
		width: 100%;
		.top{
			width: 100%;
			height: 400rpx;
			position: relative;
			background-color: #a9cdfa;
			.title{
				color: #fff;
				font-size: 50rpx;
				letter-spacing: 10rpx;
				padding-top: 100rpx;
				display: flex;
			    align-items: center;
				justify-content: center;
			}
			.img{
				width: 100%;
				height: 300rpx;
				display: flex;
			    align-items: center;
				justify-content: center;
			}
		}
		.bot{
			width: 100%;
			height: 100%;
			background-color: #f8f8f8;
			margin-top: -20rpx;
			border-radius: 30rpx 30rpx 0rpx 0rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			.earphones{
				width: 400rpx;
				height: 150rpx;
				margin-top: 100rpx;
				border-radius: 30rpx;
				background-color: #fff;
				border: 6rpx solid #a9cdfa;
				display: flex;
			    flex-direction: row;
			    align-items: center;
				justify-content: center;
				letter-spacing: 5rpx;
				image{
					width: 100rpx;
					height: 80rpx;
				}
			}
		}
	}
</style>
