<template>
	<view>

		<web-view v-if="isBle" :src="src" ref="webview" @message="onMessage"></web-view>
	</view>
</template>

<script>
	const app = getApp()
	export default {
		data() {
			return {
				src: "",
				stateObj: {},
				vw: null,
				isBle: false,
			};
		},
		onLoad(options) {
			let data = JSON.parse(options.appStartActivity)
			let that = this
			this.isBle = options.notBle
			console.log(options);
			if (!options.notBle) {
				let srcZero = data.appStartActivity.split('/')
				console.log(srcZero[1])
				if (srcZero[1] != 'hybrid') {
					this.src = data.appStartActivity
				} else {
					this.src = data.appStartActivity
					this.createWebview()

					uni.$on('deviceValue', (data) => {
						console.log('设备返回值为:' + JSON.stringify(data));
					});
				}
				console.log(JSON.stringify(srcZero))

				setTimeout(() => {
					uni.setNavigationBarTitle({
						title: data.appName,
					});
				}, 1000);



				plus.globalEvent.addEventListener('plusMessage', (event) => {

					// console.log('收到来自web-view的消息：', JSON.stringify(event))
					let keyData = event.data.args.data.arg
					if (keyData?.equipmentName) {
						uni.$emit('equipmentName', {
							equipmentName: keyData.equipmentName
						})
					}

					if (keyData?.instructions) {
						uni.$emit('messageFromH5', {
							instructions: keyData.instructions
						})
					}

					if (keyData?.editStore) {
						let editStore = keyData.editStore
						let stateObj = uni.getStorageSync('stateObj')
						stateObj[editStore.idx][editStore.idxName] = editStore.data
						uni.setStorageSync('stateObj', stateObj)
					}
					
					if (keyData?.backerPage) {
						uni.navigateBack({
							delta: 1
						})
						that.vw.close();
					}
				})
			} else {
				this.src = data.appStartActivity
				setTimeout(() => {
					uni.setNavigationBarTitle({
						title: data.appName,
					});
				}, 1000);
			}

		},

		onShow() {
			console.log('触发onshow')
		},


		onReady() {
			this.clearMuiBack();
		},


		onUnload() {
			console.log('退出webview页面')
			// plus.webview.currentWebview().close('none');
		},

		methods: {
			createWebview() {
				const systemInfo = uni.getSystemInfoSync();
				const statusBarHeight = systemInfo.statusBarHeight;
				const navigationBarHeight = systemInfo.platform === 'android' ? 48 : 44; // 根据实际情况调整导航栏高度
				let webViewHeight = 0;
				// 计算Webview的高度
				console.log(systemInfo.osName);
				if (systemInfo.osName == 'ios') {
					console.log("进入这个地方");
					webViewHeight = (systemInfo.screenHeight - statusBarHeight - navigationBarHeight) - 34;
				} else {
					webViewHeight = systemInfo.screenHeight - statusBarHeight - navigationBarHeight;
				}


				let that = this
				let stateObj = uni.getStorageSync('stateObj')
				that.vw = plus.webview.create(
					`${that.src}?t=` + new Date().getTime(),
					'plusready', 
					{
						'uni-app': 'none',
						top: statusBarHeight + navigationBarHeight, // 考虑状态栏和导航栏高度
						height: webViewHeight,
					}, 
					{
						data: {
							stateObj
						}
					}
				)
				let currentWebview = that.$mp.page.$getAppWebview()
				currentWebview.append(that.vw); //重要，否则会失效


				// that.vw.on('back', function() {
				// 	// webview.close();
				// });

				// // 监听标题栏返回按钮点击事件
				// that.vw.addEventListener('titleNViewButtonClick', function(e) {
				// 	if (e.index === 0) { // 0代表返回按钮
				// 		// webview.close();
				// 	}
				// });

				// 监听返回事件
				// that.vw.on('back', function() {
				// 	// 处理返回事件，例如关闭 WebView
				// 	console.log('关闭');
				// 	// webview.close();
				// });

			},

			throttle(fn, interval) {
				// last为上一次触发回调的时间
				let last = 0
				// 将throttle处理结果当作函数返回
				return function() {
					// 保留调用时的this上下文
					let context = this
					// 保留调用时传入的参数
					let args = arguments
					// 记录本次触发回调的时间
					let now = new Date()
					// 判断上次触发的时间和本次触发的时间差是否小于时间间隔的阈值
					if (now - last >= interval) {
						// 如果时间间隔大于我们设定的时间间隔阈值，则执行回调
						last = now;
						fn.apply(context, args);
					}
				}
			},

			onMessage(event) {
				// 对从web-view中接收到的消息进行处理


				// 将消息发送到uni-app中的事件总线
				// EventBus.$emit('messageFromH5', event.detail.data)
			},

			// 关闭mui返回
			clearMuiBack() {
				// #ifdef APP-PLUS
				var currentWebview = this.$scope.$getAppWebview().children()[0]; //监听注入的js
				currentWebview.addEventListener("loaded", function() {
					currentWebview.evalJS("mui.init({keyEventBind: {backbutton: false }});");
				});
				// #endif
			},
		}
	}
</script>

<style lang="scss">
	.custom-navbar {
		/* 添加自定义样式 */
		display: flex;
		align-items: center;
		padding: 10px;
		background-color: #fff;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 10;
	}

	.back-button,
	.title {
		/* 按钮和标题的样式 */
	}
</style>