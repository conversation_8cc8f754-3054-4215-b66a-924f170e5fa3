<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport"
			content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
		<title></title>
		<script src="js/mui.min.js"></script>
		<link href="css/mui.min.css" rel="stylesheet" />
		<link href="css/mui.picker.css" rel="stylesheet" />
		<link href="css/mui.poppicker.css" rel="stylesheet" />
		<script type="text/javascript" charset="utf-8">
			mui.init({
				keyEventBind: {
					backbutton: false //关闭back按键监听
				}
			});
		</script>
		<style>
			.mui-row.mui-fullscreen>[class*="mui-col-"] {
				height: 100%;
			}

			.title {
				margin: 20px 15px 10px;
				color: #6d6d72;
				font-size: 15px;
			}

			.oa-contact-cell.mui-table .mui-table-cell {
				padding: 11px 0;
				vertical-align: middle;
			}

			.oa-contact-cell {
				position: relative;
				margin: -11px 0;
			}

			#head {
				line-height: 20px;
			}

			.head-img {
				width: 32px;
				height: 32px;
				margin: 10px;
			}

			#head-img1 {
				position: absolute;
				bottom: 10px;
				right: 40px;
				width: 40px;
				height: 40px;
			}

			.oa-contact-avatar {
				width: 75px;
			}

			.oa-contact-avatar img {
				border-radius: 50%;
			}

			.oa-contact-content {
				width: 100%;
			}

			.oa-contact-name {
				margin-right: 20px;
			}

			.oa-contact-name,
			oa-contact-position {
				float: left;
			}

			.mui-button {
				background-color: #6d6d72;
				border-radius: 50px;
			}

			.mui-back {
				background-color: #6d6d72;
				border-radius: 50px;
			}

			.mui-table-view-cell {
				background-color: #000000;
			}

			/*点击变蓝色高亮*/
			.mui-table-view-cell.mui-active {
				background-color: #000000;
			}

			.mui-ellipsis {
				color: #FFFFFF;
			}

			.button-text {
				color: white;
				height: 30px;
				padding: 5px 10px;
				text-align: left;
				font-size: 18px;
			}

			.container {

				display: flex;
				/* 将容器设置为弹性盒子 */
				justify-content: center;

				color: #FFFFFF;
				/* 将弹性盒子内的内容水平居中 */
			}
		</style>
	</head>
	<body>


		<div id="header">
			<header class="mui-bar mui-bar-nav" style="height: 180px; background-color: #000000;">
				<div class="container">
					<img src="./image/rws_a1.png" style="width: 160px;height: 80px;" />
				</div>
				<div class="container">
					<span>
						<Label>Ⓛ</Label>
						<Label id="earlv">0</Label>%
						<img id="earlv_img" src="./image/mipmap/ic_battery_20.png" style="width: 30px;height: 16px;" />
						<Label>Ⓡ</Label>
						<Label id="earrv">0</Label>%
						<img id="earrv_img" src="./image/mipmap/ic_battery_20.png" style="width: 30px;height: 16px;" />
						<Label>Ⓒ</Label>
						<Label id="warehouselv">0</Label>%
						<img id="warehouselv_img" src="./image/mipmap/ic_battery_20.png"
							style="width: 30px;height: 16px;" />
					</span>
				</div>
				<div class="container">
					<span>
						<Label id="bt_name"></Label>
						<img id="changename" src="./image/ic_modify.png" style="width: 12px;height: 12px;">
					</span>
				</div>

				<div class="container">
					<span>
						<Label id="state">未连接</Label>
					</span>
				</div>
			</header>
		</div>
		<div style="position: absolute;top:180px;background-color: #000000;">

			<ul class="mui-card mui-table-view mui-table-view-striped mui-table-view-condensed">
				<li class="mui-table-view-cell">
					<div class="mui-table">
						<div class="mui-table-cell">
							<h4 class="mui-ellipsis">产品特性</h4>
							<h5 style="margin-bottom: 20px;margin-top: 20px;">快速了解此产品的特性及使用方法</h5>
							<button type="button"
								class="mui-btn-block mui-button mui-icon mui-icon-info mui-right button-text"
								onclick="window.location.href='./examples/productcharacteristics.html'">
								产品特性
							</button>
						</div>
					</div>
				</li>
				<li class="mui-table-view-cell">
					<div class="mui-table">
						<div class="mui-table-cell mui-col-xs-10">
							<h4 class="mui-ellipsis">空间音频</h4>
							<h5 style="margin-bottom: 20px;margin-top: 20px;">空间音频以体验更佳的音频</h5>
							<button type="button"
								class=" mui-btn-block mui-button mui-icon mui-icon-arrowright mui-right button-text"
								onclick="window.location.href='./examples/spatialaudio.html'">空间音频</button>
						</div>
					</div>
				</li>

				<li class="mui-table-view-cell">
					<div class="mui-table">
						<div class="mui-table-cell mui-col-xs-10">
							<h4 class="mui-ellipsis">运动计步</h4>
							<h5 style="margin-bottom: 20px;margin-top: 20px;">记录您的累计步数</h5>
							<button type="button"
								class=" mui-btn-block mui-button mui-icon mui-icon-arrowright mui-right button-text"
								onclick="window.location.href='./examples/motion.html'">运动计步</button>
						</div>
					</div>
				</li>
				<li class="mui-table-view-cell">
					<div class="mui-table">
						<div class="mui-table-cell mui-col-xs-10">
							<h4 class="mui-ellipsis">姿态控制</h4>
							<h5 style="margin-bottom: 20px;margin-top: 20px;">通过头部的动作姿态来控制产品的行为</h5>
							<button type="button"
								class=" mui-btn-block mui-button mui-icon mui-icon-arrowright mui-right button-text"
								onclick="window.location.href='./examples/attitude.html'">姿态控制</button>
						</div>
					</div>
				</li>

				<li class="mui-table-view-cell">
					<div class="mui-table">
						<div class="mui-table-cell mui-col-xs-10">
							<h4 class="mui-ellipsis">久坐提醒</h4>
							<h5 style="margin-bottom: 20px;margin-top: 20px;">久坐易引发健康问题，久坐时提醒您。</h5>
							<button type="button"
								class=" mui-btn-block mui-button mui-icon mui-icon-arrowright mui-right button-text"
								onclick="window.location.href='./examples/sedentary.html'">久坐提醒</button>
						</div>
					</div>
				</li>

				<li class="mui-table-view-cell">
					<div class="mui-table">
						<div class="mui-table-cell mui-col-xs-10">
							<h4 class="mui-ellipsis">跌落提醒</h4>
							<h5 style="margin-bottom: 20px;margin-top: 20px;">在使用耳机时发生耳机跌落会触发提醒并发呼叫紧急联系人电话</h5>
							<button type="button"
								class=" mui-btn-block mui-button mui-icon mui-icon-arrowright mui-right button-text"
								onclick="window.location.href='./examples/drop.html'">跌落提醒</button>
						</div>
					</div>
				</li>
				<li class="mui-table-view-cell">
					<div class="mui-table">
						<div class="mui-table-cell mui-col-xs-10">
							<h4 class="mui-ellipsis">敲击</h4>
							<h5 style="margin-bottom: 20px;margin-top: 20px;">当用手敲击耳机时，执行相应功能</h5>
							<button type="button"
								class=" mui-btn-block mui-button mui-icon mui-icon-arrowright mui-right button-text"
								onclick="window.location.href='./examples/knocking.html'">敲击</button>
						</div>
					</div>
				</li>
				<li class="mui-table-view-cell">
					<div class="mui-table">
						<div class="mui-table-cell mui-col-xs-10">
							<h4 class="mui-ellipsis">游戏模式</h4>
							<h5 style="margin-bottom: 20px;margin-top: 20px;">游戏模式能有效的降低声音传输的延时</h5>
							<button type="button"
								class=" mui-btn-block mui-button mui-icon mui-icon-arrowright mui-right button-text"
								onclick="window.location.href='./examples/gamemode.html'">游戏模式</button>
						</div>
					</div>
				</li>
				<li class="mui-table-view-cell">
					<div class="mui-table">
						<div class="mui-table-cell mui-col-xs-10">
							<h4 class="mui-ellipsis">固件版本</h4>
							<h5 style="margin-bottom: 20px;margin-top: 20px;">固件的版本表示该产品使用的软件及OTA升级</h5>
							<button type="button"
								class=" mui-btn-block mui-button mui-icon mui-icon-arrowright mui-right button-text"
								onclick="window.location.href='./examples/ota.html'">固件版本</button>
						</div>
					</div>
				</li>
				<li class="mui-table-view-cell">
					<div class="mui-table">
						<div class="mui-table-cell mui-col-xs-10">
							<h4 class="mui-ellipsis">厂商信息</h4>
							<h5 style="margin-bottom: 20px;margin-top: 20px;">查看耳机详细生产信息</h5>
							<button type="button"
								class=" mui-btn-block mui-button mui-icon mui-icon-arrowright mui-right button-text"
								onclick="window.location.href='./examples/manufacturerdetails.html'">厂商信息</button>
						</div>
					</div>
				</li>
			</ul>
		</div>


	</body>

	<script type="text/javascript" src="https://unpkg.com/@dcloudio/uni-webview-js@0.0.3/index.js" ></script>
	<script src="js/mui.picker.js"></script>
	<script src="js/mui.poppicker.js"></script>
	<script src="js/device.js"></script>
	<script>
		function SPP_sendAT(message) {
			console.log(message);
			// SPP.sendAT(message);

		}
		mui.init({
			swipeBack: false, //启用右滑关闭功能

		});
		SPP_sendAT("AT+CB\r\n");
		setInterval("clock()", 1000);

		function clock() {
			SPP_sendAT("AT+CC\r\n");
		}
		var bt_lv = "0";
		var bt_rv;
		var bt_washhousev;
		var ver = "Q4";
		var version;

		function getVimg(number) {
			if (number >= 128) {
				number -= 128;
				if (number >= 100) {
					return "./image/mipmap/ic_battery_charging_100.png"
				} else if (number >= 90) {
					return "./image/mipmap/ic_battery_charging_90.png"
				} else if (number >= 80) {
					return "./image/mipmap/ic_battery_charging_80.png"
				} else if (number >= 60) {
					return "./image/mipmap/ic_battery_charging_60.png"
				} else if (number >= 50) {
					return "./image/mipmap/ic_battery_charging_50.png"
				} else if (number > 20) {
					return "./image/mipmap/ic_battery_charging_30.png"
				} else {
					return "./image/mipmap/ic_battery_charging_20.png"
				}

			} else {
				if (number >= 100) {
					return "./image/mipmap/ic_battery_100.png"
				} else if (number >= 90) {
					return "./image/mipmap/ic_battery_90.png"
				} else if (number >= 80) {
					return "./image/mipmap/ic_battery_80.png"
				} else if (number >= 60) {
					return "./image/mipmap/ic_battery_60.png"
				} else if (number >= 50) {
					return "./image/mipmap/ic_battery_50.png"
				} else if (number > 20) {
					return "./image/mipmap/ic_battery_30.png"
				} else {
					return "./image/mipmap/ic_battery_20.png"
				}
			}
		};


		function SPPisconnect(status) {
			if (status == 1) {

				document.getElementById("state").textContent = "已连接";
			} else {

				document.getElementById("state").textContent = "未连接";
				window.location.reload();
			}
		}

		function SPPReceive(data) {
			console.log(data);
			document.getElementById("state").textContent = "已连接";
			var arry = data.split('=');
			console.log(arry[1]);
			switch (arry[0]) {
				case "CA":
					document.getElementById("bt_name").textContent = arry[1];
					break;
				case "CC":
					var v = arry[1].split(',');
					bt_lv = v[0];
					bt_rv = v[1];
					bt_washhousev = v[2];
					if (bt_lv >= 128) {
						document.getElementById("earlv").textContent = parseInt(bt_lv - 128);
						document.getElementById("earlv_img").src = getVimg(bt_lv);
					} else {
						document.getElementById("earlv").textContent = parseInt(bt_lv);
						document.getElementById("earlv_img").src = getVimg(bt_lv);
					}
					if (bt_rv >= 128) {
						document.getElementById("earrv").textContent = parseInt(bt_rv - 128);
						document.getElementById("earrv_img").src = getVimg(bt_rv);
					} else {
						document.getElementById("earrv").textContent = parseInt(bt_rv);
						document.getElementById("earrv_img").src = getVimg(bt_rv);
					}
					if (bt_washhousev >= 128) {
						document.getElementById("warehouselv").textContent = parseInt(bt_washhousev - 128);
						document.getElementById("warehouselv_img").src = getVimg(bt_washhousev);
					} else {
						document.getElementById("warehouselv").textContent = parseInt(bt_washhousev);
						document.getElementById("warehouselv_img").src = getVimg(bt_washhousev);
					}
					break;
				case "VER":
					//Q4_ZJ_A19_M01A_EQ0_156_multilink
					var v = arry[1].split('_');
					ver = arry[1];
					version = v[5];
					break;
			}

		};

		document.getElementById("changename").addEventListener('tap', function(e) {
			e.detail.gesture.preventDefault(); //修复iOS 8.x平台存在的bug，使用plus.nativeUI.prompt会造成输入法闪一下又没了
			var name = document.getElementById("bt_name");
			var btnArray = ['取消', '确定'];
			mui.prompt('耳机重连后生效', '请输入新的耳机名称', '修改耳机名称', btnArray, function(e) {
				if (e.index == 1) {
					name.textContent = e.value;
					SPP_sendAT("AT+CA=" + e.value + "\r\n")
				} else {
					// info.innerText = '你点了取消按钮';
				}
			})
		});
	</script>
</html>