<template>
	<view class="content">
		<u-icon name="arrow-left" size="28" color="#515151" @click="arroeleft"></u-icon>
		<view class="target" @click="tocountlist('true')">
			<view class="tit">{{sourcelanguage}}</view>
			 <u-icon name="arrow-down" size="20" color="#515151" ></u-icon>
		</view>
		<image src="/static/aiku/change.svg" mode=""></image>
		<view class="targett" @click="tocountlist('false')">
			<u-icon name="arrow-down" size="20" color="#fff" ></u-icon>
			<view class="tit">{{targetlanguage}}</view> 
			
		</view>
		<u-icon name="list-dot" size="28" color="#515151" @click="tolist"></u-icon>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				title: "Hello!"
			};
		},
		props:['sourcelanguage','targetlanguage'],
		methods: {
			arroeleft() {
				this.$router.back();
			},
			tolist() {
				uni.navigateTo({
					url: "/pages/menu/menu"
				})
			},
			tocountlist(num){
				uni.navigateTo({
					url: "/pages/translate/countlist?tab="+num
				})
			}
		},
	}
</script>

<style lang="scss">
	.content {
		width: 100%;
		height: 120rpx;
		background-color: #f3f4f6;
		display: flex;
		    align-items: center;
		    justify-content: space-around;
		    flex-direction: row;
		    flex-wrap: nowrap;
		.target{
			width: 200rpx;
			height: 80rpx;
			padding: 0rpx 10rpx;
			border-radius: 20rpx;
			font-size: 35rpx;
			background-color: #cfe9f9;
			display: flex;
			    flex-direction: row;
			    align-items: center;
			 box-shadow: 0rpx 0rpx 3px #cacaca;
			.tit{
				text-align: center;
				line-height: 80rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				width: 150rpx;
				height: 80rpx;
			}
  

		}
		.targett{
			width: 200rpx;
			height: 80rpx;
			padding: 0rpx 10rpx;
			border-radius: 20rpx;
			font-size: 35rpx;
			background-color: #5681ce;
			display: flex;
			    flex-direction: row;
			    align-items: center;
			box-shadow: 0rpx 0rpx 3px #cacaca;
			.tit{
				text-align: center;
				line-height: 80rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				width: 150rpx;
				height: 80rpx;
				color: #fff;
			}
		}
		image{
			width: 70rpx;
			height: 70rpx;
		}
	}
</style>