<template>
  <view class="comments-page">
    <!-- 顶部栏 -->
    <view class="header">
      <text class="title">大家说{{total ? '(' + total + ')' : ''}}</text>
    </view>

    <!-- 评论列表 -->
    <scroll-view scroll-y class="comments-list" @scrolltolower="loadMore">
      <!-- 使用 uView 图标替代图片 -->
      <view class="empty-state" v-if="!loading && (!commentList || commentList.length === 0)">
        <u-icon name="chat" size="80" color="#ddd"></u-icon>
        <text class="empty-text">还没人说话，成为第一个分享的人吧~</text>
      </view>
      
      <view class="comment-item" v-for="item in commentList" :key="item.id">
        <!-- 用户信息 -->
        <view class="user-info">
          <image class="avatar" :src="item.memberAvatar"></image>
          <view class="user-detail">
            <view class="name-wrap">
              <text class="nickname">{{item.memberName}}</text>
              <text class="bought-tag" v-if="item.hasBought">已购买</text>
              <text class="seller-tag" v-if="item.isSeller">商家</text>
            </view>
          </view>
          <!-- 删除按钮 -->
          <view class="delete-btn" v-if="isMyComment(item)" @tap="deleteComment(item.id)">
            <u-icon name="trash" size="28" color="#999"></u-icon>
          </view>
        </view>

        <!-- 图片视频 -->
        <view class="media-list" v-if="item.mediaList && item.mediaList.length">
          <view class="media-item" v-for="(media, index) in item.mediaList" :key="index" @tap="previewMedia(item.mediaList, index, media.type)">
            <image v-if="media.type === 'IMAGE'" :src="media.url" mode="aspectFill"></image>
            <view v-else-if="media.type === 'VIDEO'" class="video-thumb">
              <image :src="media.thumbUrl" mode="aspectFill"></image>
              <view class="play-icon">
                <u-icon name="play-right-fill" color="#fff" size="40"></u-icon>
              </view>
            </view>
          </view>
        </view>

        <!-- 评论内容 -->
        <view class="content">
          <view 
            :class="['text', item.isExpanded ? '' : 'collapsed']" 
            @tap="toggleExpand(item)"
            v-html="parseEmoji(item.content)"
          ></view>
          <text class="expand-btn" v-if="needExpand(item)" @tap="toggleExpand(item)">
            {{item.isExpanded ? '收起' : '展开'}}
          </text>
        </view>

        <!-- 底部信息栏 -->
        <view class="comment-footer">
          <view class="left-info">
            <text class="time">{{item.createTime}}</text>
          </view>
          <view class="right-actions">
            <view class="action-item" @tap="likeComment(item, 'COMMENT')">
              <u-icon :name="item.isLiked ? 'thumb-up-fill' : 'thumb-up'" size="28" :color="item.isLiked ? '#ff0000' : '#999'"></u-icon>
              <text>{{item.likeCount || '点赞'}}</text>
            </view>
            <view class="action-item" @tap="showReplyInput(item)">
              <u-icon name="chat" size="28" color="#999"></u-icon>
              <text>回复</text>
            </view>
          </view>
        </view>

        <!-- 回复列表 -->
        <view class="reply-list" v-if="item.replyList && item.replyList.length">
          <view class="reply-item" v-for="(reply, index) in showAllReplies(item) ? item.replyList : item.replyList.slice(0, 2)" :key="reply.id">
            <view class="reply-user-info">
              <image class="reply-avatar" :src="reply.memberAvatar"></image>
              <view class="reply-content">
                <view class="reply-header">
                  <view class="reply-header-left">
                    <text class="reply-name">{{reply.memberName}}</text>
                    <text class="seller-tag" v-if="reply.isSeller">商家</text>
                    <text class="reply-to" v-if="reply.replyTo && reply.replyTo != reply.commentId">回复 @{{reply.replyToName}}</text>
                  </view>
                  <view 
                    class="reply-delete"
                    v-if="isMyComment(reply)"
                    @tap.stop="deleteReply(reply.id)"
                  >
                    <u-icon name="trash" color="#999" size="32"></u-icon>
                  </view>
                </view>
                <view 
                  class="reply-text"
                  v-html="parseEmoji(reply.content)"
                ></view>
                <view class="reply-footer">
                  <text class="reply-time">{{reply.createTime}}</text>
                  <view class="reply-actions">
                    <view class="action-item" @tap="likeComment(reply, 'REPLY')">
                      <u-icon :name="reply.isLiked ? 'thumb-up-fill' : 'thumb-up'" size="24" :color="reply.isLiked ? '#ff0000' : '#999'"></u-icon>
                      <text>{{reply.likeCount || '点赞'}}</text>
                    </view>
                    <view class="action-item" @tap="showReplyInput(item, reply)">
                      <u-icon name="chat" size="24" color="#999"></u-icon>
                      <text>回复</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
          
          <view class="reply-expand" v-if="item.replyList.length > 2" @tap="toggleReplies(item)">
            <text>{{ item.showAllReplies ? '收起回复' : `展开${item.replyList.length - 2}条回复` }}</text>
            <u-icon :name="item.showAllReplies ? 'arrow-up' : 'arrow-down'" size="24" color="#666"></u-icon>
          </view>
        </view>
      </view>
      
      <!-- 没有更多评论提示 -->
      <view class="no-more" v-if="finished && commentList.length > 0">
        <u-divider :use-slot="true">
          <view class="no-more-content">
            <u-icon name="info-circle" color="#909399" size="28"></u-icon>
            <text>没有更多分享了</text>
          </view>
        </u-divider>
      </view>
    </scroll-view>

    <!-- 底部评论输入框 -->
    <view class="comment-input-footer">
      <input 
        class="input-box" 
        type="text"
        :placeholder="replyTo ? '回复 @' + replyTo.memberName : placeholder"
        v-model="rawContent"
        @tap="openCommentInput"
      />
    </view>

    <!-- 评论弹出框 -->
    <u-popup 
      v-model="isShowCommentInput" 
      mode="bottom"
    >
      <view class="comment-input-box">
        <view class="input-header">
          <text class="title">{{replyTo ? '回复 @' + replyTo.memberName : '分享您的体验'}}</text>
          <view class="close-btn" @tap="closeCommentInput">
            <u-icon name="close" size="32" color="#333"></u-icon>
          </view>
        </view>
        
        <scroll-view class="input-content" scroll-y scroll-anchoring>
		
		<!-- 上传预览区域 -->
		<view class="upload-preview" v-if="uploadList.length > 0">
		  <view class="preview-item" v-for="(item, index) in uploadList" :key="index">
		    <image 
		      :src="item.type == 'IMAGE' ? item.url : item.thumb" 
		      mode="aspectFill" 
		      @tap="previewMedia(uploadList, index)"
		    ></image>
		<view v-if="item.type === 'VIDEO'" class="play-icon" @tap="previewMedia(uploadList, index)">
		      <u-icon name="play-right-fill" color="#fff" size="40"></u-icon>
		</view>
		    <view class="delete-icon" @tap="deleteFile(index)">
		      <u-icon name="close" color="#fff" size="20"></u-icon>
		    </view>
		  </view>
		</view>
		
          <view class="textarea-wrapper">
            <textarea 
              class="comment-textarea"
			  :show-confirm-bar="false"
              v-model="rawContent"
              :placeholder="placeholder"
			  :maxlength="10000"
            ></textarea>
            
            <view class="textarea-tools">
              <view class="tool-item" @tap="toggleEmojiPicker">
                <image 
                  class="emoji-icon" 
                  :src="showEmojiPicker ? '/static/icons/emoji-active.png' : '/static/icons/emoji.png'"
                ></image>
              </view>
              <view class="tool-item" v-if="!replyTo" @tap="showMediaSelect">
				<u-icon name="photo" size="48" color="#666"></u-icon>
              </view>
            </view>
          </view>
          
          <!-- 表情选择器 -->
          <view class="emoji-picker" v-show="showEmojiPicker">
            <view class="emoji-grid">
              <view 
                class="emoji-item"
                v-for="(emoji, index) in qqEmojis"
                :key="index"
                @tap="insertEmoji(emoji)"
              >
                <image :src="emoji.url" mode="aspectFit"></image>
              </view>
            </view>
          </view>
		  
        </scroll-view>
        
        <view class="input-footer">
          <u-button 
            type="primary" 
            :disabled="!commentContent.trim() && uploadList.length === 0" 
            @tap="submitComment"
          >发送</u-button>
        </view>
      </view>
    </u-popup>

    <!-- 媒体预览弹窗 -->
    <u-popup v-model="showMediaPreview" mode="bottom" height="90%">
      <view class="media-preview">
		<view v-html="videoHtml" style="width:100%; height:100%;"></view>
		<view class="preview-close" @tap="closeMediaPreview">
		    <u-icon name="close" color="#fff" size="40"></u-icon>
		</view>
      </view>
    </u-popup>
	
	<u-action-sheet :list="mediaSelectList" v-model="mediaSelect" :tips="mediaSelectTips" 
	:safe-area-inset-bottom="true" @click="selectMediaUpload"></u-action-sheet>
	
	<u-toast ref="uToast" />
	<u-modal v-model="showUViewModal" :content="uViewModalContent" :title="uViewModalTitle"></u-modal>
  </view>
</template>

<script>
import { getCommentPage, addComment, replyComment, deleteComment, toggleLike, deleteReply } from '@/api/comments.js'
import storage from '@/utils/storage.js'
import { upload } from '@/api/common.js'

export default {
  name: 'Comments',
  props: {
    goodsId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      storage,
      action: upload,
      // 评论列表数据
      commentList: [],
      total: 0,
      // 分页参数
      pageParams: {
        pageNumber: 1,
        pageSize: 10,
        goodsId: ''
      },
      // 评论内容
      rawContent: '',
      // 回复对象
      replyTo: null,
      // 上传配置
      uploadList: [],
      // 预览相关
      showMediaPreview: false,
      currentMediaIndex: 0,
      previewMediaList: [],
      currentVideo: '', // 添加当前播放视频的 URL
      // 弹窗控制
      isShowCommentInput: false,
      // 加载状态
      loading: false,
      finished: false,
      // 表情相关
      showEmojiPicker: false,
      qqEmojis: [
        { text: '[微笑]', url: '/static/emoji/0.gif' },
        { text: '[撇嘴]', url: '/static/emoji/1.gif' },
        { text: '[色]', url: '/static/emoji/2.gif' },
        { text: '[发呆]', url: '/static/emoji/3.gif' },
        { text: '[得意]', url: '/static/emoji/4.gif' },
        { text: '[流泪]', url: '/static/emoji/5.gif' },
        { text: '[害羞]', url: '/static/emoji/6.gif' },
        { text: '[闭嘴]', url: '/static/emoji/7.gif' },
        { text: '[睡]', url: '/static/emoji/8.gif' },
        { text: '[大哭]', url: '/static/emoji/9.gif' },
        { text: '[尴尬]', url: '/static/emoji/10.gif' },
        { text: '[发怒]', url: '/static/emoji/11.gif' },
        { text: '[调皮]', url: '/static/emoji/12.gif' },
        { text: '[呲牙]', url: '/static/emoji/13.gif' },
        { text: '[惊讶]', url: '/static/emoji/14.gif' },
        { text: '[难过]', url: '/static/emoji/15.gif' },
        { text: '[酷]', url: '/static/emoji/16.gif' },
        { text: '[冷汗]', url: '/static/emoji/17.gif' },
        { text: '[抓狂]', url: '/static/emoji/18.gif' },
        { text: '[吐]', url: '/static/emoji/19.gif' },
        { text: '[偷笑]', url: '/static/emoji/20.gif' },
        { text: '[可爱]', url: '/static/emoji/21.gif' },
        { text: '[白眼]', url: '/static/emoji/22.gif' },
        { text: '[傲慢]', url: '/static/emoji/23.gif' },
        { text: '[饥饿]', url: '/static/emoji/24.gif' },
        { text: '[困]', url: '/static/emoji/25.gif' },
        { text: '[惊恐]', url: '/static/emoji/26.gif' },
        { text: '[流汗]', url: '/static/emoji/27.gif' },
        { text: '[憨笑]', url: '/static/emoji/28.gif' },
        { text: '[大兵]', url: '/static/emoji/29.gif' },
        { text: '[奋斗]', url: '/static/emoji/30.gif' },
        { text: '[咒骂]', url: '/static/emoji/31.gif' },
        { text: '[疑问]', url: '/static/emoji/32.gif' },
        { text: '[嘘]', url: '/static/emoji/33.gif' },
        { text: '[晕]', url: '/static/emoji/34.gif' },
        { text: '[折磨]', url: '/static/emoji/35.gif' },
        { text: '[衰]', url: '/static/emoji/36.gif' },
        { text: '[骷髅]', url: '/static/emoji/37.gif' },
        { text: '[敲打]', url: '/static/emoji/38.gif' },
        { text: '[再见]', url: '/static/emoji/39.gif' },
        { text: '[擦汗]', url: '/static/emoji/40.gif' }
      ],
      // 滚动控制
      scrollToView: '',
      // 占位符文本
      placeholder: '说点什么...',
      maxImageCount: 2, // 最大图片上传数量
      maxVideoCount: 1, // 最大视频上传数量
	  maxImageSize: 50,
	  maxVideoSize: 160,
      curFileType: '',
	  mediaSelect: false,
	  mediaSelectList: [{text: '图片'}, {text: '视频'}],
	  mediaSelectTips: {text: '请选择文件类型'},
	  showUViewModal: false,
	  uViewModalContent: '',
	  uViewModalTitle: '',
	  videoHtml: '',
    }
  },

  computed: {
    commentContent() {
		console.log(this.rawContent);
      return (this.rawContent || '').trim()
    },
    displayContent() {
      if (!this.rawContent) return this.placeholder
      return this.parseEmoji(this.rawContent)
    }
  },

  created() {
    // 获取当前用户ID
    const userInfo = storage.getUserInfo()
    this.currentUserId = userInfo ? userInfo.id : ''
	// console.log(this.$options.filters.getPages());
  },

  watch: {
    goodsId: {
      handler(val) {
        if (val) {
          this.pageParams.goodsId = val
          this.loadComments(true)
        }
      },
      immediate: true
    },
	currentVideo(val) {
	  this.videoHtml = '<video   ref="videoPlay" style="width:100%; height:100%;" src="' + val + '" autoplay loop show-mute-btn  webkit-playsinline="" playsinline="" ></video>';
	}
  },

  methods: {
	  selectMediaUpload(index) {
		  let that = this;
		  let text = that.mediaSelectList[index].text;
		  // 获取当前已上传的图片和视频数量
		  const currentImages = that.uploadList.filter(item => item.type === 'IMAGE').length;
		  const currentVideos = that.uploadList.filter(item => item.type === 'VIDEO').length;
		  if (text == '图片') {
			  uni.chooseImage({
				  success(chooseImageRes) {
					  console.log('chooseImageRes', chooseImageRes);
					  let tempFiles = chooseImageRes.tempFiles;
					  if (!tempFiles || tempFiles.length == 0) { return; }
					  if (currentImages + tempFiles.length > that.maxImageCount) {
						  that.uViewModalTitle = "温馨提示";
						  that.uViewModalContent = `最多只能上传${that.maxImageCount}张图片`;
						  that.showUViewModal = true;
						  return;
					  }
					  
					  for (let i = 0; i < tempFiles.length; i++) {
						  let file = tempFiles[i];
						  if (file.size > (that.maxImageSize * 1024 * 1024)) {
							  that.uViewModalTitle = "温馨提示";
							  that.uViewModalContent = `图片大小不能超过${that.maxImageSize}MB`;
							  that.showUViewModal = true;
							  break;
						  }
						  
						  that.uploadMediaFile(file.path, 'IMAGE', {"directoryPath": "goodsComments/image"});
					  }
					  
				  }
			  });
		  } else if (text == '视频') {
			  uni.chooseVideo({
				  maxDuration: 60,
				  success(res) {
					  if (!res.tempFilePath)  { return; }
					  if (currentVideos >= 1) {
						  that.uViewModalTitle = "温馨提示";
						  that.uViewModalContent = `最多只能上传${that.maxVideoCount}个视频`;
						  that.showUViewModal = true;
						  return;
					  }
					  if (res.size > (that.maxVideoSize * 1024 * 1024)) {
						  that.uViewModalTitle = "温馨提示";
						  that.uViewModalContent = `视频大小不能超过${that.maxVideoSize}MB`;
						  that.showUViewModal = true;
						  return;
					  }
					  
					  that.uploadMediaFile(res.tempFilePath, 'VIDEO', {"directoryPath": "goodsComments/video"});
				  }
			  });
		  }
	  },
	  uploadMediaFile(filePath, type, params) {
		  uni.showLoading({
		    title: '上传中...',
		    mask: true
		  });
		  let that = this;
		  uni.uploadFile({
			  url: that.action,
			  filePath,
			  name: 'file',
			  header: { accessToken: that.storage.getAccessToken() },
			  formData: params,
			  success(res) {
				  console.log("res", res);
				  let data = JSON.parse(res.data);
				  that.uploadList.push({
				      url: data.result,
                      type: type,
					  thumb: type == 'IMAGE' ? data.result : data.result + '?x-oss-process=video/snapshot,t_0,f_jpg,w_600,h_600'
                  });
			  },
			  fail(err) {
				  that.$refs.uToast.show({
				  	title: '上传失败，请重试',
				  	icon: false,
				  });
			  },
			  complete(info) {
				  uni.hideLoading();
			  }
		  });
	  },
	  showMediaSelect() {
		  this.mediaSelect = true;
	  },
    // 判断是否是当前用户的评论
    isMyComment(item) {
      return item.memberId === this.currentUserId
    },

    // 加载评论列表
    async loadComments(reset = false) {
      if (this.loading || (this.finished && !reset)) return

      if (reset) {
        this.pageParams.pageNumber = 1
        this.commentList = []
        this.finished = false
      }

      this.loading = true
      try {
        const res = await getCommentPage(this.pageParams)
        if (res.data.success) {
          const { records, total } = res.data.result

          // 处理评论数据
          const newComments = records.map(item => ({
            ...item,
            isExpanded: false,
            showAllReplies: false,
            mediaList: Array.isArray(item.mediaList) ? item.mediaList.map(media => ({
              ...media,
              type: media.type ? media.type.toUpperCase() : 'IMAGE'
            })) : []
          }))

          if (reset) {
            this.commentList = newComments
          } else {
            this.commentList = [...this.commentList, ...newComments]
          }
          this.total = total

          // 判断是否加载完成
          this.finished = this.commentList.length >= total
        }
      } catch (error) {
        console.error('大家说加载失败:', error)
        uni.showToast({
          title: '大家说加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 提交评论
    async submitComment() {
      // 检查用户是否登录
      const userInfo = storage.getUserInfo()
      if (!userInfo) {
        uni.showToast({
          title: '请先登录',
          icon: 'none'
        })
        return
      }

      if (!this.commentContent && this.uploadList.length === 0) {
        uni.showToast({
          title: '请输入分享内容或上传图片/视频',
          icon: 'none'
        })
        return
      }

      try {
        const mediaList = this.uploadList.map(item => ({
          url: item.url,
          type: item.type || 'IMAGE',
          thumbUrl: item.thumb || item.url
        }))

        const params = {
          goodsId: this.goodsId,
          content: this.commentContent,
          mediaList
        }

        if (this.replyTo) {
          // 回复评论
          params.commentId = this.replyTo.commentId || this.replyTo.id;
          params.replyTo = this.replyTo.id
          await replyComment(params)
        } else {
          // 发表新评论
          await addComment(params)
        }

        uni.showToast({
          title: '发表成功',
          icon: 'success'
        })

        // 重置表单
        this.rawContent = ''
        this.uploadList = []
        this.replyTo = null
        this.isShowCommentInput = false
        this.showEmojiPicker = false

        // 重新加载评论列表
        await this.loadComments(true)
      } catch (error) {
        console.error('发表分享失败:', error)
        uni.showToast({
          title: '发表失败',
          icon: 'none'
        })
      }
    },

    // 删除评论
    async deleteComment(id) {
      uni.showModal({
        title: '提示',
        content: '确定要删除这条分享吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              await deleteComment(id)
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })
              await this.loadComments(true)
            } catch (error) {
              uni.showToast({
                title: '删除失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },
	
	// 删除回复
	async deleteReply(id) {
	  uni.showModal({
	    title: '提示',
	    content: '确定要删除这条回复吗？',
	    success: async (res) => {
	      if (res.confirm) {
	        try {
	          await deleteReply(id)
	          uni.showToast({
	            title: '删除成功',
	            icon: 'success'
	          })
	          await this.loadComments(true)
	        } catch (error) {
	          uni.showToast({
	            title: '删除失败',
	            icon: 'none'
	          })
	        }
	      }
	    }
	  })
	},

    // 点赞/取消点赞
    async likeComment(comment, type) {
      try {
        await toggleLike(comment.id, type)
        comment.isLiked = !comment.isLiked
        comment.likeCount = comment.isLiked ? (comment.likeCount + 1) : (comment.likeCount - 1)
      } catch (error) {
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        })
      }
    },
    // 删除上传文件
    deleteFile(index) {
      uni.showModal({
        title: '提示',
        content: '确定要删除这个文件吗？',
        success: (res) => {
          if (res.confirm) {
            this.uploadList.splice(index, 1);
          }
        }
      });
    },

    // 预览媒体
    previewMedia(mediaList, index) {
      const currentMedia = mediaList[index]
      if (currentMedia.type.toUpperCase() === 'IMAGE') {
        const images = mediaList
          .filter(media => media.type.toUpperCase() === 'IMAGE')
          .map(media => media.url)
        const current = images.indexOf(currentMedia.url)

        uni.previewImage({
          urls: images,
          current
        })
      } else if (currentMedia.type === 'VIDEO') {
        this.currentVideo = currentMedia.url
        this.showMediaPreview = true
        this.currentMediaIndex = index
        this.previewMediaList = mediaList
      }
    },

    // 关闭预览
    closeMediaPreview() {
      this.showMediaPreview = false
      this.previewMediaList = []
      this.currentMediaIndex = 0
    },

    // 修改打开评论输入框方法
    openCommentInput() {
      const userInfo = storage.getUserInfo()
      if (!userInfo) {
        uni.showToast({
          title: '请先登录',
          icon: 'none'
        })
        return
      }

      this.isShowCommentInput = true
      this.replyTo = null
      this.rawContent = ''
      this.uploadList = []
      this.showEmojiPicker = false
	  
	  // 先让键盘收起
	  uni.hideKeyboard();
    },

    // 修改显示回复输入框方法
    showReplyInput(comment, reply = null) {
      const userInfo = storage.getUserInfo()
      if (!userInfo) {
        uni.showToast({
          title: '请先登录',
          icon: 'none'
        })
        return
      }

      this.replyTo = reply || comment
      this.isShowCommentInput = true
      this.rawContent = ''
      this.uploadList = [] // 回复时清空上传列表
      this.showEmojiPicker = false
    },

    // 修改关闭评论输入框方法
    closeCommentInput() {
      this.isShowCommentInput = false
      this.rawContent = ''
      this.uploadList = []
      this.replyTo = null
      this.showEmojiPicker = false
      // 重置上传组件
      if (this.$refs.uUpload) {
        this.$refs.uUpload.clear()
      }
    },

    // 加载更多
    loadMore() {
      if (!this.loading && !this.finished) {
        this.pageParams.pageNumber++
        this.loadComments()
      }
    },

    // 展开/收起评论
    toggleExpand(item) {
      this.$set(item, 'isExpanded', !item.isExpanded)
    },

    // 是否需要展开按钮
    needExpand(item) {
      return item.content.length > 100
    },

    // 展开/收起回复
    toggleReplies(item) {
      this.$set(item, 'showAllReplies', !item.showAllReplies)
    },

    // 是否显示所有回复
    showAllReplies(item) {
      return item.showAllReplies || (item.replyList && item.replyList.length <= 2)
    },

    // 表情相关方法
    toggleEmojiPicker() {
      this.showEmojiPicker = !this.showEmojiPicker
    },

    insertEmoji(emoji) {
      this.rawContent = (this.rawContent || '') + emoji.text
    },

    parseEmoji(content) {
      if (!content) return ''
      
      const emojiMap = {}
      this.qqEmojis.forEach(emoji => {
		// #ifdef APP-PLUS
        emojiMap[emoji.text] = emoji.url.replace("/static", "static");
		// #endif
		// #ifdef H5
		emojiMap[emoji.text] = emoji.url;
		// #endif
      })
      
      // 只在显示时转换为图片，输入框保持文字形式
      return content.replace(/\[[^\]]+\]/g, match => {
        const url = emojiMap[match]
        return url ? `<img class="emoji-img" src="${url}" alt="${match}">` : match
      })
    },

    // 处理输入框内容变化
    handleInput(event) {
      // 直接使用 v-model 绑定，不需要特殊处理
    },

    // 处理输入框获取焦点
    handleFocus() {
      // 不需要特殊处理
    },

    // 处理输入框失去焦点
    handleBlur() {
      // 不需要特殊处理
    }
  }
}
</script>

<style lang="scss" scoped>
.comments-page {
  // min-height: 100vh;
  height: 100%;
  overflow: hidden;
  background: #fff;
  
  .header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #eee;
    
    .title {
      font-size: 32rpx;
      font-weight: bold;
    }
  }
  
  .comments-list {
    height: calc(100% - 86rpx - 92rpx - env(safe-area-inset-bottom));
    
    // 添加空状态样式
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 100rpx 0;
      
      .empty-icon {
        width: 200rpx;
        height: 200rpx;
        margin-bottom: 20rpx;
      }
      
      .empty-text {
        font-size: 28rpx;
        color: #999;
      }
    }
    
    .comment-item {
      padding: 30rpx;
      border-bottom: 1rpx solid #eee;
      
      .user-info {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
        
        .avatar {
          width: 60rpx;
          height: 60rpx;
          border-radius: 50%;
          margin-right: 20rpx;
        }
        
        .user-detail {
          flex: 1;
          
          .name-wrap {
            display: flex;
            align-items: center;
            
            .nickname {
              font-size: 28rpx;
              color: #333;
            }
            
            .bought-tag {
                font-size: 20rpx;
                color: #ff6b00;
                background: #fff5f0;
                padding: 2rpx 8rpx;
                border-radius: 4rpx;
                margin-left: 10rpx;
            }
            
            .seller-tag {
              font-size: 22rpx;
              color: #fff;
              background: #ff0036;
              padding: 2rpx 10rpx;
              border-radius: 4rpx;
              margin-left: 10rpx;
            }
          }
          
          .time {
            font-size: 26rpx;
            color: #999;
          }
        }
        
        .delete-btn {
          padding: 10rpx;
        }
      }
      
      .content {
        margin-bottom: 20rpx;
        
        .text {
          font-size: 28rpx;
          color: #333;
          line-height: 1.6;
          
          &.collapsed {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            overflow: hidden;
          }
        }
        
        .expand-btn {
          font-size: 26rpx;
          color: #666;
          margin-left: 10rpx;
        }
      }
      
      .media-list {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 20rpx;
        
        .media-item {
          width: 200rpx;
          height: 200rpx;
          margin: 10rpx;
          border-radius: 8rpx;
          overflow: hidden;
          
          image {
            width: 100%;
            height: 100%;
          }
          
          .video-thumb {
            position: relative;
            width: 100%;
            height: 100%;
            
            image, video {
              width: 100%;
              height: 100%;
            }
            
            .play-icon {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              background: rgba(0, 0, 0, 0.5);
              border-radius: 50%;
              padding: 10rpx;
            }
          }
        }
      }
      
      .comment-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20rpx;
        
        .left-info {
          .time {
            font-size: 24rpx;
            color: #999;
            margin-right: 20rpx;
          }
        }
        
        .right-actions {
          display: flex;
          align-items: center;
          
          .action-item {
            display: flex;
            align-items: center;
            margin-left: 30rpx;
            
            text {
              font-size: 26rpx;
              color: #999;
              margin-left: 6rpx;
            }
          }
        }
      }
      
      .reply-list {
        margin-left: 80rpx;
        margin-top: 20rpx;
        padding: 20rpx;
        background: #f8f8f8;
        border-radius: 8rpx;
        
        .reply-item {
          margin-top: 20rpx;
          
          .reply-user-info {
            display: flex;
            
            .reply-avatar {
              width: 50rpx;
              height: 50rpx;
              border-radius: 50%;
              margin-right: 16rpx;
            }
            
            .reply-content {
              flex: 1;
              
              .reply-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 8rpx;
                
                .reply-header-left {
                  display: flex;
                  align-items: center;
                  flex: 1;
                  
                  .reply-name {
                    font-size: 26rpx;
                    color: #333;
                  }
                  
                  .seller-tag {
                    font-size: 20rpx;
                    color: #fff;
					background: #ff0036;
                    padding: 0 8rpx;
                    border-radius: 4rpx;
                    margin-left: 8rpx;
                  }
                  
                  .reply-to {
                    font-size: 26rpx;
                    color: #999;
                    margin-left: 8rpx;
                  }
                }
                
                .reply-delete {
                  padding-left: 20rpx;
                  display: flex;
                  align-items: center;
                }
              }
              
              .reply-text {
                font-size: 28rpx;
                color: #333;
                line-height: 1.5;
              }
              
              .reply-footer {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 12rpx;
                
                .reply-time {
                  font-size: 24rpx;
                  color: #999;
                }
                
                .reply-actions {
                  display: flex;
                  align-items: center;
                  
                  .action-item {
                    display: flex;
                    align-items: center;
                    margin-left: 20rpx;
                    
                    text {
                      font-size: 24rpx;
                      color: #999;
                      margin-left: 4rpx;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  
  .comment-input-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20rpx 30rpx;
    background: #fff;
    border-top: 1rpx solid #eee;
    padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
    
    .input-box {
      background: #f5f5f5;
      border-radius: 36rpx;
      padding: 16rpx 30rpx;
      
      .placeholder {
        font-size: 28rpx;
        color: #999;
      }
    }
  }
  
  .media-preview {
    width: 100%;
    height: 100%;
    background: #000;
    position: relative;
    
    .preview-swiper {
      width: 100%;
      height: 100%;
      
      .preview-image {
        width: 100%;
        height: 100%;
      }
      
      .preview-video {
        width: 100%;
        height: 100%;
      }
    }
    
    .preview-close {
      position: absolute;
      top: 32rpx;
      right: 32rpx;
    }
  }
}

.comment-input-box {
  display: flex;
  flex-direction: column;
  
  .input-header {
    padding: 30rpx;
    border-bottom: 1rpx solid #eee;
    background: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .title {
      font-size: 32rpx;
      font-weight: bold;
    }
    
    .close-btn {
      padding: 10rpx;
    }
  }
  
  .input-content {
    flex: 1;
    max-height: 728rpx;
    background: #fff;
	position: relative;
    
    .textarea-wrapper {
      position: relative;
      margin: 0 auto;
      width: 94%;
	  height: 344rpx;
      border: 1rpx solid #eee;
      border-radius: 12rpx;
      background: #fff;
      
      .comment-textarea {
        width: 100%;
        height: 320rpx;
        padding: 20rpx 20rpx 68rpx 20rpx;
        font-size: 28rpx;
        line-height: 1.5;
        box-sizing: border-box;
      }
      
      .textarea-tools {
        position: absolute;
        right: 20rpx;
        bottom: 0;
        display: flex;
        align-items: center;
        z-index: 1;
        
        .tool-item {
          padding: 10rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          
          .emoji-icon {
            width: 48rpx;
            height: 48rpx;
            display: block;
          }
          
          &:active {
            opacity: 0.7;
          }
        }
      }
    }
    
    .emoji-picker {
      margin: 20rpx auto;
      width: 94%;
      background: #f8f8f8;
      border-radius: 12rpx;
      padding: 20rpx;
      
      .emoji-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 12rpx;
        
        .emoji-item {
          width: 70rpx;
          height: 70rpx;
          padding: 10rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          
          image {
            width: 100%;
            height: 100%;
          }
          
          &:active {
            background: #eee;
            border-radius: 8rpx;
          }
        }
      }
    }
  }
  
  .input-footer {
    padding: 20rpx 30rpx;
    border-top: 1rpx solid #eee;
    background: #fff;
  }
}

.upload-preview {
  margin: 20rpx auto;
  width: 94%;
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  
  .preview-item {
    position: relative;
    width: 160rpx;
    height: 160rpx;
    border-radius: 8rpx;
    overflow: hidden;

    .play-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 0, 0.5);
      border-radius: 50%;
      padding: 10rpx;
    }
    
    image, video {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .delete-icon {
      position: absolute;
      top: 6rpx;
      right: 6rpx;
      width: 40rpx;
      height: 40rpx;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.emoji-img {
  display: inline-block;
  width: 36rpx;
  height: 36rpx;
  vertical-align: middle;
  margin: 0 2rpx;
}

.content {
  .text {
    /deep/ img {
      display: inline-block;
      width: 36rpx;
      height: 36rpx;
      vertical-align: middle;
      margin: 0 2rpx;
    }
  }
}

.reply-text {
  /deep/ img {
    display: inline-block;
    width: 32rpx;
    height: 32rpx;
    vertical-align: middle;
    margin: 0 2rpx;
  }
}

.toolbar {
  display: flex;
  padding: 20rpx 0;
  border-top: 1rpx solid #eee;
  
  .tool-item {
    padding: 10rpx 30rpx;
    
    &:active {
      opacity: 0.7;
    }
  }
}

// 修复iOS滚动问题
.emoji-list {
  -webkit-overflow-scrolling: touch;
}

.comment-textarea {
  /deep/ .emoji-img {
    display: inline-block;
    width: 36rpx;
    height: 36rpx;
    vertical-align: middle;
    margin: 0 2rpx;
  }
}

// 添加回复展开/收起按钮样式
.reply-expand {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  color: #666;
  font-size: 26rpx;
  
  text {
    margin-right: 10rpx;
  }
  
  &:active {
    opacity: 0.7;
  }
}

.preview-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-more {
  padding: 30rpx 0;
  
  &-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10rpx;
    
    text {
      font-size: 26rpx;
      color: #909399;
    }
  }
}
</style> 