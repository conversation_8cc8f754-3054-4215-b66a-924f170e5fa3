<!DOCTYPE html>
<html>

	<head>
		<meta charset="utf-8">
		<title>Hello MUI</title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">

		<!--标准mui.css-->
		<link rel="stylesheet" href="../css/mui.min.css">
		<!--App自定义的css-->
		<!--<link rel="stylesheet" type="text/css" href="../css/app.css"/>-->
		<style>
			.mui-row.mui-fullscreen>[class*="mui-col-"] {
				height: 100%;
			}

			.title {
				margin: 20px 15px 10px;
				color: #6d6d72;
				font-size: 15px;
			}

			.oa-contact-cell.mui-table .mui-table-cell {
				padding: 11px 0;
				vertical-align: middle;
			}

			.oa-contact-cell {
				position: relative;
				margin: -11px 0;
			}

			.mui-checkbox {

				width: 20px;
				height: 20px;
				margin: 10px;
			}

			#head {
				line-height: 20px;
			}

			.head-img {
				width: 32px;
				height: 32px;
				margin: 10px;
			}

			#head-img1 {
				position: absolute;
				bottom: 10px;
				right: 40px;
				width: 40px;
				height: 40px;
			}

			.oa-contact-avatar {
				width: 75px;
			}

			.oa-contact-avatar img {
				border-radius: 50%;
			}

			.oa-contact-content {
				width: 100%;
			}

			.oa-contact-name {
				margin-right: 20px;
			}

			.oa-contact-name,
			oa-contact-position {
				float: left;
			}

			.mui-backdrop {
				position: fixed;
				top: 0;
				right: 0;
				bottom: 0;
				left: 0;
				z-index: 998;
				background-color: rgba(0, 0, 0, .3);
			}

			.container {

				display: flex;
				/* 将容器设置为弹性盒子 */
				justify-content: center;
				/* 将弹性盒子内的内容水平居中 */
				color: white;

			}
		</style>

	</head>
	<body style=" background-color: black;">
		<header class="mui-bar mui-bar-nav" style=" background-color: black;">
			<a class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></a>
			<h1 class="mui-title " style="color: white;">空间音频</h1>
		</header>
		<div class="mui-content mui-content-padded" style=" background-color: black;">
			<div>
				<ul class="mui-table-view mui-grid-view" style=" background-color: black;">
					<li class="mui-table-view-cell mui-media mui-col-xs-0">
						<img src="../image/line_left.png" />
					</li>
					<li id="head_close" class="mui-table-view-cell mui-media mui-col-xs-2">
						<img id="head_close_img" style="max-width:100%;overflow:hidden;"
							src="../image/mipmap-xxhdpi/head_close_p.png" />
						<div style="color: aliceblue;">关闭</div>
					</li>
					<li class="mui-table-view-cell mui-media mui-col-xs-4">
						<img src="../image/line_middle.png" />
					</li>
					<li id="head_doing" class="mui-table-view-cell mui-media mui-col-xs-2">
						<img id="head_doing_img" style="max-width:100%;overflow:hidden;"
							src="../image/mipmap-xxhdpi/head_doing_n.png" style=" width: 44px;height: 44px;" />
						<div style="color: aliceblue;">开启</div>
					</li>
					<li class="mui-table-view-cell mui-media mui-col-xs-2">
						<img src="../image/line_right.png"" />
					</li>
				</ul>
			</div>
			<div class=" container">
						<img src=" ../image/img_spatial_audio.png" />
			</div>

			<div class=" container">
				<h4>聆听移动的沉浸式三维音乐</h4>
			</div>
		</div>

	</body>
	<script src="../js/mui.min.js"></script>
	<script>
		function SPP_sendAT(message) {
			console.log(message);
			SPP.sendAT(message);
		}
		mui.init({
			swipeBack: false, //启用右滑关闭功能

		});
		SPP_sendAT("AT+CO\r\n");

		function SPPReceive(data) {
			console.log(data);
			var arry = data.split('=');
			console.log(arry[1]);
			switch (arry[0]) {

				case "CO":
					if (arry[1] == "0") {
						document.getElementById("head_close_img").src = "../image/mipmap-xxhdpi/head_close_p.png"
						document.getElementById("head_doing_img").src = "../image/mipmap-xxhdpi/head_doing_n.png"

					} else {
						document.getElementById("head_close_img").src = "../image/mipmap-xxhdpi/head_close_n.png"
						document.getElementById("head_doing_img").src = "../image/mipmap-xxhdpi/head_doing_p.png"

					}
					break;
			}

		};
		var mask = mui.createMask(callback);
		document.getElementById("head_close").addEventListener('tap', function(e) {
			e.detail.gesture.preventDefault(); //修复iOS 8.x平台存在的bug，使用plus.nativeUI.prompt会造成输入法闪一下又没了
			document.getElementById("head_close_img").src = "../image/mipmap-xxhdpi/head_close_p.png"
			document.getElementById("head_doing_img").src = "../image/mipmap-xxhdpi/head_doing_n.png"
			// mask.show();
			SPP_sendAT("AT+CO=0\r\n");
		});

		document.getElementById("head_doing").addEventListener('tap', function(e) {
			e.detail.gesture.preventDefault(); //修复iOS 8.x平台存在的bug，使用plus.nativeUI.prompt会造成输入法闪一下又没了
			document.getElementById("head_close_img").src = "../image/mipmap-xxhdpi/head_close_n.png"
			document.getElementById("head_doing_img").src = "../image/mipmap-xxhdpi/head_doing_p.png"
			mask.close();
			SPP_sendAT("AT+CO=2\r\n");

		});

		function callback() {
			// alert('触发遮罩');
		}
	</script>
</html>