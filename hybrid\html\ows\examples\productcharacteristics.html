<!doctype html>
<html>

	<head>
		<meta charset="utf-8">
		<title></title>
		<meta name="viewport"
			content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
		<link href="css/mui.css" rel="stylesheet" />
	</head>

	<body>
		<script src="js/mui.js"></script>
		<script type="text/javascript">
			mui.init()
		</script>
	</body>

</html>
<!DOCTYPE html>
<html>

	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">

		<!--标准mui.css-->
		<link rel="stylesheet" href="../css/mui.min.css">
		<!--App自定义的css-->
		<!--<link rel="stylesheet" type="text/css" href="../css/app.css"/>-->
		<style>
			.mui-row.mui-fullscreen>[class*="mui-col-"] {
				height: 100%;
			}

			.title {
				margin: 20px 15px 10px;
				color: #6d6d72;
				font-size: 15px;
			}

			oa-contact-position {
				float: left;
			}

			.container {

				display: flex;
				/* 将容器设置为弹性盒子 */
				justify-content: center;
				/* 将弹性盒子内的内容水平居中 */
				/* margin: 5px; */
				margin-top: 10px;
			}

			.texttittle {
				width: 180px;
				text-align: center;
				border: 2px solid #6d6d72;
				border-radius: 50px;
			}
		</style>

	</head>
	<body style="background-color: #000000;">
		<header class="mui-bar mui-bar-nav" style="background-color: black;">
			<a class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></a>
			<h1 class="mui-title" style="color: white;">产品特性</h1>
		</header>
		<div class="mui-card mui-content" style="background-color: #000000; color: white;;">
			<div class="">
				<div class="mui-row mui-text-center">
					<h4>● 快速了解产品特性 ●</h4>
				</div>
				<div class="mui-row  myrow container">
					<img src="../image/line2_right.png" />
					<label class="texttittle">空间音频</label>
					<img src="../image/line2_left.png" />
				</div>
				<div class="mui-row  myrow container">
					<div class=" mui-text-center">
						<img src="../image/img_spatial_features.png" />
					</div>
				</div>
				<div class="mui-row  myrow container">
					<div class=" mui-text-center">
						<h4>聆听移动的沉浸式三维音频</h4>
					</div>
				</div>

				<div class="mui-row  myrow container">
					<img src="../image/line2_right.png" />
					<label class="texttittle">播放/控制</label>
					<img src="../image/line2_left.png" />
				</div>
				<div class="mui-row  myrow container">
					<div class=" mui-text-center">
						<img src="../image/img_music_paly_control.png" />
					</div>
				</div>
				<div class="mui-row  myrow ">
					<h4>通过左右耳机的多功能按键，来播放/控制/暂停/上一曲/下一曲，以及定义长按键功能</h4>
				</div>
				<div class="mui-row  myrow container">
					<img src="../image/line2_right.png" />
					<label class="texttittle">姿态控制</label>
					<img src="../image/line2_left.png" />
				</div>
				<div class="mui-row  myrow container">
					<div class=" mui-text-center">
						<img src="../image/img_gyroscope.png" />
					</div>
				</div>
				<div class="mui-row  myrow ">
					<h4>通过左右耳机内置的陀螺仪强大算法，分析出身体的各种姿态实现相应的控制功能</h4>
				</div>
			</div>



		</div>


	</body>
	<script src="../js/mui.min.js"></script>
	<script>
		mui.init({
			swipeBack: false //启用右滑关闭功能
		});
	</script>
</html>