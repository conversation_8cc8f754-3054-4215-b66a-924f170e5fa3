<template>
	<view class="content">
		<u-navbar title="翻译耳机" leftIcon="arrow-left" :autoBack="true" @rightClick="rightClick" :placeholder="true" bgColor="#f3f4f6"  rightIcon="list-dot"></u-navbar>
		<view class="notconnected">
			<image src="/static/aiku/bearphones.svg" mode=""></image>
			<view class="notconnected-font">设备未连接</view>
			<image src="/static/aiku/attion.svg" mode=""></image>
		</view>
		<view class="connected notconnected">
			<image src="/static/aiku/bearphones.svg" mode=""></image>
			<view class="notconnected-font">
				<view class="name">name</view>
				<view class="empower">设备有效期:已授权</view>
			</view>
			<view class="connected">
				已连接
			</view>
		</view>
		<view class="tap">
			<view class="taptit1">
				轻松将你的耳机变成翻译耳机
			</view>
			<view class="taptit2">
				你可以将自己的蓝牙耳机，通过蓝牙连上手机，再返回本界面，就可以变成翻译耳机！
			</view>
		</view>
		<view class="bot">
			<view class="earphones" @click="toearphones">
				<view class="fontsion">
					<image src="/static/aiku/zysms.svg" mode=""></image>
				</view>
				<view class="font">
					<view class="font1">自由说模式</view>
					<view class="font2">两人各带一个耳机，实时翻译，释放双手，自由沟通</view>
				</view>
			</view>
			<view class="earphones" @click="toearphoness">
				<view class="fontsion">
					<image src="/static/aiku/ckms.svg" mode=""></image>
				</view>
				<view class="font">
					<view class="font1">触控模式</view>
					<view class="font2">两人各带一个耳机，单击或者双击耳机实时翻译，适合熟人愿意佩戴您的耳机</view>
				</view>
			</view>
			<view class="earphones" @click="toearphonesss">
				<view class="fontsion">
					<image src="/static/aiku/wfms.svg" mode=""></image>
				</view>
				<view class="font">
					<view class="font1">外放模式</view>
					<view class="font2">一人佩戴耳机，一人拿手机，适合陌生人不方便佩戴您的耳机</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		onLoad() {
			this.hqair()
		},
		methods: {
			rightClick(){
				uni.navigateTo({
					url:'/pages/menu/menu'
				})
			},
			//跳转自由说
			toearphones(){
				uni.navigateTo({
					url:'/pages/translate/free'
				})
			},
			//跳转触控
			toearphoness(){
				uni.navigateTo({
					url:'/pages/translate/touch'
				})
			},
			//跳转外放
			toearphonesss(){
				uni.navigateTo({
					url:'/pages/translate/exocytosis'
				})
			},
			hqair(){
				const BluetoothAdapter = plus.android.importClass('android.bluetooth.BluetoothAdapter'); // 引入Java 蓝牙类
				
				    const blueadapter = BluetoothAdapter.getDefaultAdapter(); //拿到默认蓝牙适配器方法
				
				    if (blueadapter) {
				        // 判断蓝牙是否开启
				        if (blueadapter.isEnabled()) {
				            // 已开启
				            uni.showToast({
				                title: '蓝牙已打开',
				            })
							
							//初始化蓝牙模块
							uni.openBluetoothAdapter({
							  success(res) {
							    console.log(res)
								//查找已连接蓝牙的uuid
								uni.getBluetoothDevices({
								 success: function(res) {
								    console.log('所有已发现的蓝牙设备:', res.devices);
								    // 示例：打印第一个设备的名字和UUID
								    if (res.devices.length > 0) {
								      console.log('第一个设备的名字:', res.devices[0].name);
								      console.log('第一个设备的UUID:', res.devices[0].deviceId);
								    }
								 },
								 fail: function(err) {
								    console.log('获取蓝牙设备失败:', err);
								 }
								});
							  }
							})
				        } else {
				            // 未开启弹出提示框
				            uni.showModal({
				                title: '提示',
				                content: '蓝牙尚未打开，是否打开蓝牙',
				                showCancel: true,
				                cancelText: '取消',
				                confirmText: '确定',
				                success(res) {
				                    // 点击确定后通过系统打开蓝牙
				                    if (res.confirm) {
				                        const blueadapter = BluetoothAdapter.getDefaultAdapter();
				                        if (blueadapter != null) {
				                            return blueadapter.enable();
				                        }
				                    } else {
				                        // 点击取消什么也不做
				                        console.log("点击了取消");
				                    }
				                }
				            })
				        }
				    }
				// uni.openBluetoothAdapter({
				//  success: function (res) {
				//     console.log('蓝牙适配器打开成功');
				//     // 获取已连接的蓝牙设备
				//     uni.getConnectedBluetoothDevices({
				//      services: [], // 替换为你的服务UUID
				//      success: function (res) {
				//         console.log('已连接的蓝牙设备:', res);
				//         if (res.devices.length > 0) {
				//           // 获取第一个已连接设备的名字
				//           const deviceName = res.devices[0].name;
				//           console.log('已连接的蓝牙设备名字:', deviceName);
				//         } else {
				//           console.log('没有找到已连接的蓝牙设备');
				//         }
				//      },
				//      fail: function (err) {
				//         console.log('获取已连接的蓝牙设备失败:', err);
				//      }
				//     });
				//  },
				//  fail: function (err) {
				//     console.log('蓝牙适配器打开失败:', err);
				//  }
				// });
			}
		}
	}
</script>

<style lang="scss">
	page{
		height: 100%;
		background-color: #f8f8f8;
	}
	.content{
		width: 100%;
		.notconnected{
			width: 100%;
			height: 80rpx;
			display: flex;
			    flex-direction: row;
			    justify-content: space-around;
			    align-items: center;
			background-color: #ffebed;
			image{
				width: 60rpx;
				height: 60rpx;
			}
			.notconnected-font{
				width: 500rpx;
				color: #ec6f71;
			}
		}
		.connected{
			background-color: #fff;
			.notconnected-font{
				display: flex;
				flex-direction: column;
			}
			.name{
				color: #030502;
				font-size: 34rpx;
			}
			.empower{
				color: #42b97d;
				font-size: 22rpx;
			}
			.connected{
				font-size: 24rpx;
				width: 120rpx;
				height: 50rpx;
				background-color: #efefef;
				color: #707070;
				text-align: center;
				line-height: 50rpx;
			}
		}
		.tap{
			width: 700rpx;
			height: 200rpx;
			border-radius: 30rpx;
			display: flex;
			flex-direction: column;
		    align-items: center;
			justify-content: space-evenly;
			margin: 0 auto;
			margin-top: 40rpx;
			background-image: linear-gradient(to bottom, #bbe0f6, #fff);
			.taptit1{
				width: 500rpx;
				font-size: 38rpx;
				font-weight: 600;
				color: #030502;
			}
			.taptit2{
				width: 550rpx;
				font-size: 25rpx;
				color: #adadad;
			}
		}
		.top{
			width: 100%;
			height: 400rpx;
			position: relative;
			background-color: #a9cdfa;
			.title{
				color: #fff;
				font-size: 50rpx;
				letter-spacing: 10rpx;
				padding-top: 100rpx;
				display: flex;
			    align-items: center;
				justify-content: center;
			}
			.img{
				width: 100%;
				height: 300rpx;
				display: flex;
			    align-items: center;
				justify-content: center;
			}
		}
		.bot{
			width: 100%;
			height: 100%;
			// background-color: #f8f8f8;
			margin-top: -20rpx;
			border-radius: 30rpx 30rpx 0rpx 0rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			.earphones{
				width: 600rpx;
				height: 150rpx;
				margin-top: 70rpx;
				border-radius: 30rpx;
				// background-color: #fff;
				border: 2rpx solid #cacaca;
				display: flex;
			    flex-direction: row;
			    align-items: center;
				justify-content: space-around;
				letter-spacing: 5rpx;
				.fontsion{
					width: 90rpx;
					height: 90rpx;
					border-radius: 50%;
					display: flex;
					justify-content: center;
					align-items: center;
					background-color: #89c9f1;
					image{
						width: 70rpx;
						height: 70rpx;
					}
				}
				.font{
					width: 450rpx;
					display: flex;
					flex-direction: column;
					align-items: flex-start;
					justify-content: space-evenly;
					.font1{
						
					}
					.font2{
						color: #adadad;
						font-size: 24rpx;
					}
				}
				
			}
		}
	}
</style>
