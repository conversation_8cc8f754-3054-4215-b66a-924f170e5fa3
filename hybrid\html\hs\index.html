<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<meta name="viewport"
			content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
		<title></title>
		<script src="js/mui.min.js"></script>
		<link href="css/mui.min.css" rel="stylesheet" />
		<link href="css/mui.picker.css" rel="stylesheet" />
		<link href="css/mui.poppicker.css" rel="stylesheet" />
		<!--require dsbridge init js-->
		<script src="./js/dsbridge.js"> </script>
		<script type="text/javascript" charset="utf-8">
			mui.init();
		</script>

		<style>
			/* .mui-row.mui-fullscreen>[class*="mui-col-"] {
				height: 100%;
			} */
			.battery {
				width: 30px;
				height: 16px;
				background: "./image/mipmap/ic_airpods_display.png";

			}

			.title {
				margin: 20px 15px 10px;
				color: #6d6d72;
				font-size: 15px;
			}

			.oa-contact-cell.mui-table .mui-table-cell {
				padding: 11px 0;
				vertical-align: middle;
			}

			.oa-contact-cell {
				position: relative;
				margin: -11px 0;
			}

			#head {
				line-height: 20px;
			}

			.head-img {
				width: 32px;
				height: 32px;
				margin: 10px;
			}

			#head-img1 {
				position: absolute;
				bottom: 10px;
				right: 40px;
				width: 40px;
				height: 40px;
			}

			.oa-contact-avatar {
				width: 75px;
			}

			.oa-contact-avatar img {
				border-radius: 50%;
			}

			.oa-contact-content {
				width: 100%;
			}

			.oa-contact-name {
				margin-right: 20px;
			}

			.oa-contact-name,
			oa-contact-position {
				float: left;
			}

			.container {
				display: flex;
				/* 将容器设置为弹性盒子 */
				justify-content: center;
				/* 将弹性盒子内的内容水平居中 */
			}
			
			
			.slider-container {
				width: 80%;
				margin-left: 52px;
				margin-top: 14px;
			}
			.slider {
			        -webkit-appearance: none;
			        width: 100%;
			        height: 10px;
			        background: #ddd;
			        outline: none;
			        opacity: 0.7;
			        transition: opacity 0.15s ease-in-out;
			        border-radius: 5px;
			    }
			
			    .slider:hover {
			        opacity: 1;
			    }
			
			    .slider::-webkit-slider-thumb {
			        -webkit-appearance: none;
			        appearance: none;
			        width: 20px;
			        height: 20px;
			        background: #4CAF50;
			        cursor: pointer;
			        border-radius: 50%;
			    }
			
			    .slider::-moz-range-thumb {
			        width: 20px;
			        height: 20px;
			        background: #4CAF50;
			        cursor: pointer;
			        border-radius: 50%;
			    }
		</style>
	</head>
	<body >
		<div id="box">
			
			<div id="tabbar-with-deivce" class="mui-control-content mui-active">
				
				<header class="mui-bar mui-bar-nav" style="height: 150px;top: 60px;display:none;">
					<div class="container">
						<img src="./image/mipmap/ic_airpods_display.png" style="width: 160px;height: 80px;" />
					</div>
					<div class="container">
						<span>
							<Label>Ⓛ</Label>
							<Label id="earleft">0</Label>%
							<img id="earleft_img" src="./image/mipmap/ic_battery_20.png" style="width: 30px;height: 16px;" />
							<Label>Ⓡ</Label>
							<Label id="earright">0</Label>%
							<img id="earright_img" src="./image/mipmap/ic_battery_20.png" style="width: 30px;height: 16px;" />
							<Label>Ⓒ</Label>
							<Label id="warehouselv">0</Label>%
							<img id="warehouselv_img" src="./image/mipmap/ic_battery_20.png"
								style="width: 30px;height: 16px;" />
						</span>
					</div>
					<div class="container">
						<span>
							<Label id="bt_name">H104</Label>
							<img id="changename" src="./image/mipmap-xxhdpi/change_name_icon.png"
								style="width: 12px;height: 12px;">
						</span>
					</div>
					<div class="container">
						<span>
							<Label id="state">未连接</Label>
						</span>
					</div>
				</header>

				<div style="margin-top:16px;margin-bottom:80px;">
					<div class="mui-content">
						<div class="mui-card">
							<h5 class="mui-content-padded">噪声控制</h5>
							<div class="nav">
								<ul class="mui-table-view mui-grid-view ">
									<li id="noise_close" class="mui-table-view-cell mui-media mui-col-xs-4">
										<img id="noise_close_img" src="./image/mipmap-xxhdpi/noise_close_p.png"
											style="width: 44px;height: 44px;" />
										<div class="mui-media-body">关闭</div>
									</li>
									<li id="noise_down" class="mui-table-view-cell mui-media mui-col-xs-4">
										<img id="noise_down_img" src="./image/mipmap-xxhdpi/noise_down_n.png"
											style="width: 44px;height: 44px;" />
										<div class="mui-media-body" >降噪</div>
									</li>
									<li id="noise_well" class="mui-table-view-cell mui-media mui-col-xs-4">
										<img id="noise_well_img" src="./image/mipmap-xxhdpi/noise_well_n.png"
											style=" width: 44px;height: 44px;" />
										<div class="mui-media-body">环境感知</div>
									</li>
								</ul>
							</div>
							<div id="controlUI" style="margin: 10px;">
								<h5>强度设定：<span id='rangeValue'>50</span></h5>
								<div class="mui-input-row mui-input-range mui-card-content">
									<input type="range" min="0" max="100" id="myRange">
								</div>
							</div>
						</div>
						<!-- <header class="mui-bar mui-bar-nav">
							<div onclick="backerPage()" class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></div>
						</header> -->
						<h5 class="mui-content-padded">空间音频</h5>
						<div class="mui-card">
							<h5 class="mui-content-padded">请佩戴双耳，播放音频时体验</h5>
							<div class="nav">
								<ul class="mui-table-view mui-grid-view">
									<li id="head_close" class="mui-table-view-cell mui-media mui-col-xs-4">
										<img id="head_close_img" src="./image/mipmap-xxhdpi/head_close_p.png"
											style=" width: 44px;height: 44px;" />
										<div class="mui-media-body">关闭</div>
									</li>
									<li id="head_hold" class="mui-table-view-cell mui-media mui-col-xs-4">
										<img id="head_hold_img" src="./image/mipmap-xxhdpi/head_hold_n.png"
											style="width: 44px;height: 44px;" />
										<div class="mui-media-body">固定</div>
									</li>
									<li id="head_doing" class="mui-table-view-cell mui-media mui-col-xs-4">
										<img id="head_doing_img" src="./image/mipmap-xxhdpi/head_doing_n.png"
											style=" width: 44px;height: 44px;" />
										<div class="mui-media-body">头部追踪</div>
									</li>
								</ul>
							</div>
						</div>
					</div>
					<h5 class="mui-content-padded">压感按压调节</h5>
					<div class="mui-card">
						<ul class="mui-table-view mui-table-view-chevron">
							<li class="mui-table-view-cell mui-media">
								<a id="pressLsetting" class="mui-navigate-right">
									<img class="mui-pull-left head-img" id="head-img"
										src="./image/mipmap-xxhdpi/select_left_icon.png">
									<div class="mui-media-body">
										左耳按键
										<p id="L_setting_detiles">操作控制:通透 降噪 关闭</p>
										<p id="L_setting_press">按压力度：<span id='LrangeValue'>50</span></p>
										
										<p class='mui-ellipsis'></p>
									</div>
								</a>
								<div class="slider-container">
									<input type="range" min="0" max="100" value="50" class="slider" style="width: 100%;" id="LmySlider">
								</div>
								
							</li>
							<li class="mui-table-view-cell mui-media">
								<a id="pressRsetting" class="mui-navigate-right">
									<img class="mui-pull-left head-img" src="./image/mipmap-xxhdpi/select_right_icon.png">
									<div class="mui-media-body">
										右耳按键
										<p id="R_setting_detiles">操作控制:通透 降噪 关闭</p>
										<p id="R_setting_press">按压力度：<span id='RrangeValue'>50</span></p>
										<p class='mui-ellipsis'></p>
									</div>
								</a>
								<div class="slider-container">
									<input type="range" min="0" max="100" value="50" class="slider" style="width: 100%;" id="RmySlider">
								</div>
							</li>
							<li id="pressspeed" class="mui-table-view-cell mui-media">
								<a class="mui-navigate-right">
									<img class="mui-pull-left head-img" src="./image/mipmap-xxhdpi/press_speed_icon.png">
									<div class="mui-media-body">
										按压速度
										<p id="pressspeed_text">较慢</p>
										<p class='mui-ellipsis'></p>
									</div>
								</a>
							</li>
							<li id="presstime" class="mui-table-view-cell mui-media">
								<a class="mui-navigate-right">
									<img class="mui-pull-left head-img" src="./image/mipmap-xxhdpi/press_time_icon.png">
									<div class="mui-media-body">
										按压时长
										<p id="presstime_text">较慢</p>
										<p class='mui-ellipsis'></p>
									</div>
								</a>
							</li>
						</ul>
					</div>
			
					<h5 class="mui-content-padded">音效设置</h5>
					<div class="mui-card">
						<ul class="mui-table-view">
							<!-- <li class="mui-table-view-cell" style="display: flex;">
								<img class="mui-pull-left head-img" src="./image/mipmap-xxhdpi/sleep_out_icon.png">
								<span style="display: flex; align-items: center;">耳机语音唤醒</span>
								<div id="rouse" class="mui-switch mui-switch-blue mui-switch-mini ">
									<div class="mui-switch-handle"></div>
								</div>
							</li> -->
			
							<li class="mui-table-view-cell" style="display: flex;">
								<img class="mui-pull-left head-img" src="./image/mipmap-xxhdpi/vol_increase_icon.png">
								<span style="display: flex; align-items: center;">音量增强</span>
								<div id="volumeenhancement" class="mui-switch mui-switch-blue mui-switch-mini ">
									<div class="mui-switch-handle"></div>
								</div>
							</li>
			
							<li class="mui-table-view-cell" style="display: flex;">
								<img class="mui-pull-left head-img" src="./image/mipmap-xxhdpi/send_word_increase_icon.png">
								<span style="display: flex; align-items: center;">送话增强</span>
								<div id="speakenhancement" class="mui-switch mui-switch-blue mui-switch-mini ">
									<div class="mui-switch-handle"></div>
								</div>
							</li>
			
							<li class="mui-table-view-cell mui-media">
								<a id="eqselect" class="mui-navigate-right">
									<img class=" mui-pull-left head-img"
										src="./image/mipmap-xxhdpi/select_sound_effect.png">
									<div class="mui-media-body">
										音效
										<p id="eqselcet">均衡</p>
										<p class='mui-ellipsis'></p>
									</div>
								</a>
							</li>
						</ul>
					</div>
					<div class="mui-card">
						<ul class="mui-table-view">
							<li id="multidevicetable"  class="mui-table-view-cell mui-media">
								<a class="mui-navigate-right" id="multidevice">
									<img class="mui-pull-left head-img" src="./image/mipmap-xxhdpi/more_device_icon.png">
									<div class="mui-media-body">
										多设备智能连接
										<p id="multidevice_text">在线静模式</p>
									</div>
								</a>
			
							</li>
							<li class="mui-table-view-cell" style="display: flex;">
								<img class="mui-pull-left head-img" src="./image/mipmap-xxhdpi/select_auto_test_icon.png">
								<span style="display: flex; align-items: center;">入耳检测</span>
								<div id="earcheck" class="mui-switch mui-switch-blue mui-switch-mini ">
									<div class="mui-switch-handle"></div>
								</div>
							</li>
							<!-- <li id="restoredefault" class="mui-table-view-cell mui-media">
								<a class=" mui-navigate-right">
									<div style="display: flex;">
										<img class="mui-pull-left head-img" src="./image/mipmap-xxhdpi/recovery_icon.png">
										<span style="display: flex; align-items: center;">恢复默认设置</span>
									</div>
								</a>
							</li> -->
							<li id="gamemode" class="mui-table-view-cell mui-media">
								<a class="mui-navigate-right">
									<img class="mui-pull-left head-img" src="./image/mipmap-xxhdpi/game_model_icon.png">
									<div class="mui-media-body">
										游戏模式
										<p id="gamemode_text">关闭</p>
									</div>
								</a>
							</li>
						</ul>
			
					</div>
			
				</div>
			</div>
			
			<!-- 		<div id="tabbar-with-find" class="mui-control-content">
				<iframe src="examples/map.html" width="100%"></iframe>
			</div> -->
			<div id="tabbar-with-update" class="mui-control-content">
				<header class="mui-bar mui-bar-nav">
					<h1 class="mui-title">版本更新</h1>
				</header>
				<div class="mui-content">
					<div class="mui-card">
						<ul class="mui-table-view">
							<li class="mui-table-view-cell mui-media">
								<a class="mui-navigate-right" href="examples/otadetialed.html">
									<img class="mui-pull-left head-img" src="./image/mipmap-xxhdpi/ota_version_icon.png">
									<div class="mui-media-body">
										耳机版本
										<p class='mui-ellipsis'></p>
									</div>
								</a>
							</li>
							<li class="mui-table-view-cell" style="display: flex;">
								<img class="mui-pull-left head-img" src="./image/mipmap-xxhdpi/ota_app_version_icon.png">
								<span style="display: flex; align-items: center;">APP自动检测更新</span>
								<div class="mui-switch mui-switch-blue mui-switch-mini mui-active">
									<div class="mui-switch-handle"></div>
								</div>
							</li>
			
							<li class="mui-table-view-cell mui-media">
								<a class="mui-navigate-right" href="examples/manufacturerdetails.html">
									<div class="mui-navigate-right mui-icon mui-icon-help">
										耳机详情
										<p class='mui-ellipsis'></p>
									</div>
								</a>
							</li>
			
						</ul>
					</div>
					<div class="mui-card">
						<div class="mui-card-content">
							<div class="mui-card-content-inner">
								<h4>连接手机</h4>
								<p>耳机打开充电仓会自动连接上次连接过的设备。按住充电仓按键约3秒，
									白灯闪烁时进入可搜索状态，此时可以在手机蓝牙菜单搜索连接耳机。</p>
							</div>
							<div class="mui-card-content-inner">
								<h4>音乐控制</h4>
								<p>按下按键一次播放&#47;暂停音乐，快速按下按键两次切换到下一曲，快速按下按键三次切换到上一曲</p>
							</div>
							<div class="mui-card-content-inner">
								<h4>电话控制</h4>
								<p>按下按键一次接听&#47;挂断电话，快速按下按键两次拒绝接听电话</p>
							</div>
						</div>
			
					</div>
			
				</div>
			
			</div>
			
			<div id="tabbar-with-help" class="mui-control-content">
			
				<header class="mui-bar mui-bar-nav">
					<h1 class="mui-title">操作说明</h1>
				</header>
				<div class="mui-content" style="margin-bottom:80px;">
					<div class="mui-card">
						<div class="mui-card-content">
							<div class="mui-card-content-inner">
								<h4>连接手机</h4>
								<p>耳机打开充电仓会自动连接上次连接过的设备。按住充电仓按键约3秒，
									白灯闪烁时进入可搜索状态，此时可以在手机蓝牙菜单搜索连接耳机。</p>
							</div>
							<div class="mui-card-content-inner">
								<h4>音乐控制</h4>
								<p>按下按键一次播放&#47;暂停音乐，快速按下按键两次切换到下一曲，快速按下按键三次切换到上一曲</p>
							</div>
							<div class="mui-card-content-inner">
								<h4>电话控制</h4>
								<p>按下按键一次接听&#47;挂断电话，快速按下按键两次拒绝接听电话</p>
							</div>
							<div class="mui-card-content-inner">
								<h4>噪声控制</h4>
								<p>按住按键3秒，耳机会在“降噪”“关闭”“通透”三种模式中切换</p>
							</div>
							<div class="mui-card-content-inner">
								<h4>语音助手</h4>
								<p>按住按键3秒，耳机会启动手机语音助手功能</p>
							</div>
							<div class="mui-card-content-inner">
								<h4>游戏模式</h4>
								<p>游戏中快速按下按键四次，开启&#47;关闭游戏模式(注：为保证非游戏模式下耳机播放的流畅性，退出游戏时请同时关闭耳机游戏模式)</p>
							</div>
							<div class="mui-card-content-inner">
								<h4>恢复出厂设置</h4>
								<p>耳机放入充电仓内，按住充电仓按键15秒，橙灯闪烁时耳机恢复出厂设置</p>
							</div>
						</div>
					</div>
			
				</div>
			</div>
			
			
			
			
			
			<nav class="mui-bar mui-bar-tab">
				<a class="mui-tab-item mui-active" href="#tabbar-with-deivce">
					<span class="mui-icon mui-icon-home"></span>
					<span class="mui-tab-label">我的设备</span>
				</a>
				<!-- 			<a class="mui-tab-item" href="#tabbar-with-find">
					<span class="mui-icon mui-icon-search"></span>
					<span class="mui-tab-label">查找</span>
				</a> -->
				<!-- <a class="mui-tab-item" href="#tabbar-with-update">
					<span class="mui-icon mui-icon-loop"></span>
					<span class="mui-tab-label">检测更新</sage); } mui.init({ swipeBack: false //启用右滑关闭功能 }); //
							SPP_sendAT("AT+CB\r\n"); var span>
				</a> -->
				<a class="mui-tab-item" href="#tabbar-with-help">
					<span class="mui-icon mui-icon-help"></span>
					<span class="mui-tab-label">帮助</span>
				</a>
			</nav>
		</div>
		
	</body>

    <script type="text/javascript" src="https://unpkg.com/@dcloudio/uni-webview-js@0.0.3/index.js" ></script>
	<!-- <script src="https://cdn.jsdelivr.net/npm/vue/dist/vue.js"></script> -->
	<script src="js/mui.picker.js"></script>
	<script src="js/mui.poppicker.js"></script>
	<script src="js/device.js"></script>
	<script type="text/javascript">
		// 将vue挂载到id为home的根元素上
		// var vm = new Vue({
		// 	el: "#box",
		// 	data() {
		// 	  return {};
		// 	},
		// 	methods: {},
		// 	mounted() {
		// 		document.addEventListener('visibilitychange', () => {
		// 			if (document.visibilityState === 'visible') {
		// 				console.log('浏览器的当前页签onShow时，do something')
		// 			}
		// 		})
		// 	},
		// 	created() {},
		// })
		// console.log(111)
		// console.log(localStorage.getItem('routerUrl'))
		// checkPostMessage();
		// 从首页跳转过来携带的参数
		
		
		
		var bt_rv;
		var bt_washhousev;
		var ver = "Q4";
		var version;
		var CE = 0;
		var CP_speed = 0;
		var CP_time = 0;
		var CQ_spk = 0;
		var CQ_mic = 0;
		var CI_L_modle = 0;
		var CI_R_modle = 0;
		var CI_modle_detial = 0;
		var CJ_L_press = 0;
		var CJ_R_press = 0;
		
		var routerUrl = '';
		
		var noiseState = null;
		var timeIng = null;
		var isEditStore = false;
		var timeNum = 0;
		
		if(window.plus){  
            plusReady();  
        }else{   
            document.addEventListener( "plusready", plusReady, false );  
        }  
        // 扩展API准备完成后要执行的操作  
        function plusReady(data){
			console.log(data )
			if (plus.webview.getWebviewById("plusready")) {

				// const appData = JSON.parse(JSON.stringify(plus.webview.getWebviewById("plusready").data));
				const appData = JSON.parse(JSON.stringify(plus.webview.getWebviewById("plusready").data));
                console.log('appData: ', JSON.stringify(appData));
				analysis(appData.stateObj, () => {
					setTimeout(() => {
						getData()
						
					}, 500)
				})
			}
        }  
		
		function customEvent(data) {
			console.log(data)
		}

		function SPP_sendAT(message) {
			console.log(message);                         
			//异步调用
			uni.postMessage({
				data: {
					instructions: message, // 这是传的参数
				},
			});
		}
		/* mui.init({
			swipeBack: false, //启用右滑关闭功能
			// keyEventBind: {
			// 	backbutton: false //关闭back按键监听
			// }
		}); */
		
		
		function LsetSilder() {
			var slider = document.getElementById("LmySlider");
			// var output = document.getElementById("sliderValue");
			slider.value = localStorage.getItem('CJ_L_press')
			// output.innerHTML = slider.value; // 初始化输出当前值
			
			slider.oninput = dedounce(function() {
				// output.innerHTML = this.value; // 滑动时更新值
				console.log(this.value);
				document.getElementById('L_setting_press').innerText = '按压力度：' + this.value;
				localStorage.setItem('CJ_L_press', this.value);
				SPP_sendAT("AT+CJ=" + localStorage.getItem('CJ_L_press') + "," + localStorage.getItem('CJ_R_press') +
					"\r\n");
			}, 500);
		}
		
		
		function RsetSilder() {
			var slider = document.getElementById("RmySlider");
			// var output = document.getElementById("sliderValue");
			slider.value = localStorage.getItem('CJ_R_press')
			// output.innerHTML = slider.value; // 初始化输出当前值
			
			slider.oninput = dedounce(function() {
				// output.innerHTML = this.value; // 滑动时更新值
				console.log(this.value);
				document.getElementById('R_setting_press').innerText = '按压力度：' + this.value;
				localStorage.setItem('CJ_R_press', this.value);
				SPP_sendAT("AT+CJ=" + localStorage.getItem('CJ_L_press') + "," + localStorage.getItem('CJ_R_press') +
					"\r\n");
			}, 500);
		}
		
		
		
		// onshowFunction() {
		// 	console.log('onshowFunction')
		// }
		
		
		
		// var 
		
		
		


		//间隔性定时器
		// 每过1秒，向文本框中显示系统时间。
		// setInterval("clock()", 50000);

		// function clock() {
		// 	SPP_sendAT("AT+CB\r\n");
		// 	SPP_sendAT("AT+CC\r\n");
		// }

		function getVimg(number) {
			console.log(number)
			console.log('进来了这里----------------------')
			if (number >= 128) {
				number -= 128;
				if (number >= 100) {
					return "./image/mipmap/ic_battery_charging_100.png"
				} else if (number >= 90) {
					return "./image/mipmap/ic_battery_charging_90.png"
				} else if (number >= 80) {
					return "./image/mipmap/ic_battery_charging_80.png"
				} else if (number >= 60) {
					return "./image/mipmap/ic_battery_charging_60.png"
				} else if (number >= 50) {
					return "./image/mipmap/ic_battery_charging_50.png"
				} else if (number >= 21) {
					return "./image/mipmap/ic_battery_charging_30.png"
				} else {
					return "./image/mipmap/ic_battery_charging_20.png"
				}

			} else {
				if (number >= 100) {
					return "./image/mipmap/ic_battery_100.png"
				} else if (number >= 90) {
					return "./image/mipmap/ic_battery_90.png"
				} else if (number >= 80) {
					return "./image/mipmap/ic_battery_80.png"
				} else if (number >= 60) {
					return "./image/mipmap/ic_battery_60.png"
				} else if (number >= 50) {
					return "./image/mipmap/ic_battery_50.png"
				} else if (number >= 21) {
					return "./image/mipmap/ic_battery_30.png"
				} else {
					return "./image/mipmap/ic_battery_20.png"
				}
			}
		};
		//注册 javascript API 
		// dsBridge.register('SPPisconnect', function(status) {
		// 	if (status == 1) {

		// 		document.getElementById("state").textContent = "已连接";
		// 	} else {

		// 		document.getElementById("state").textContent = "未连接";
		// 		window.location.reload();
		// 	}
		// 	return status;
		// })
		
		function backerPage() {
			console.log(886);
			uni.postMessage({
				data: {
					backerPage: true, // 这是传的参数
				},
			});
		}

		function SPPisconnect(status) {
			if (status == 1) {

				document.getElementById("state").textContent = "已连接";
			} else {

				document.getElementById("state").textContent = "未连接";
				window.location.reload();
			}
		}

		function noise_modle(ch) {
			switch (ch) {
				case "1":
					return "关闭";
				case "2":
					return "降噪";
				case "3":
					return "关闭 降噪";
				case "4":
					return "通透";
				case "5":
					return "关闭 通透";
				case "6":
					return "降噪 通透";
				case "7":
					return "通透 降噪 关闭";
			}
		}

		function eqselectstr(ch) {
			switch (ch) {
				case "0":
					return "均衡";
				case "1":
					return "低音增强";
				case "2":
					return "柔和高音";
				case "3":
					return "人声增强";
				case "4":
					return "轻音乐";
				case "5":
					return "高音&低音";
			}
		}



		function Ear_V_Show(bt_lv, bt_rv, bt_washhousev) {
			if (bt_lv >= 128) {
				document.getElementById("earleft").textContent = bt_lv - 128;
				document.getElementById("earleft_img").src = getVimg(bt_lv);
			} else {
				document.getElementById("earleft").textContent = bt_lv;
				document.getElementById("earleft_img").src = getVimg(bt_lv);
			}
			if (bt_rv >= 128) {
				document.getElementById("earright").textContent = bt_rv - 128;
				document.getElementById("earright_img").src = getVimg(bt_rv);
			} else {
				document.getElementById("earright").textContent = bt_rv;
				document.getElementById("earright_img").src = getVimg(bt_rv);
			}
			if (bt_washhousev >= 128) {
				document.getElementById("warehouselv").textContent = bt_washhousev - 128;
				document.getElementById("warehouselv_img").src = getVimg(bt_washhousev);
			} else {
				document.getElementById("warehouselv").textContent = bt_washhousev;
				document.getElementById("warehouselv_img").src = getVimg(bt_washhousev);
			}
		}

		function head_ui_init(CO) {
			switch (CO) {
				case "0":
					localStorage.setItem('CO_modle', CO)
					document.getElementById("head_close_img").src = "./image/mipmap-xxhdpi/head_close_p.png"
					document.getElementById("head_hold_img").src = "./image/mipmap-xxhdpi/head_hold_n.png"
					document.getElementById("head_doing_img").src = "./image/mipmap-xxhdpi/head_doing_n.png"
					break;
				case "1":
					localStorage.setItem('CO_modle', CO)
					document.getElementById("head_close_img").src = "./image/mipmap-xxhdpi/head_close_n.png"
					document.getElementById("head_hold_img").src = "./image/mipmap-xxhdpi/head_hold_p.png"
					document.getElementById("head_doing_img").src = "./image/mipmap-xxhdpi/head_doing_n.png"
					break;
				case "2":
					localStorage.setItem('CO_modle', CO)
					document.getElementById("head_close_img").src = "./image/mipmap-xxhdpi/head_close_n.png"
					document.getElementById("head_hold_img").src = "./image/mipmap-xxhdpi/head_hold_n.png"
					document.getElementById("head_doing_img").src = "./image/mipmap-xxhdpi/head_doing_p.png"
					break;
			}
		}

		function noise_ui_init(CE) {
			switch (CE) {
				case "0":
					localStorage.setItem('CE_modle', CE)
					document.getElementById("controlUI").style.display = "none";
					document.getElementById("noise_close_img").src = "./image/mipmap-xxhdpi/noise_close_p.png"
					document.getElementById("noise_down_img").src = "./image/mipmap-xxhdpi/noise_down_n.png"
					document.getElementById("noise_well_img").src = "./image/mipmap-xxhdpi/noise_well_n.png"
					break;
				case "1":
					localStorage.setItem('CE_modle', CE)
					document.getElementById("controlUI").style.display = "";
					document.getElementById('rangeValue').innerText = localStorage.getItem('CF_number')
					document.getElementById('myRange').value = localStorage.getItem('CF_number')
					document.getElementById("noise_close_img").src = "./image/mipmap-xxhdpi/noise_close_n.png"
					document.getElementById("noise_down_img").src = "./image/mipmap-xxhdpi/noise_down_p.png"
					document.getElementById("noise_well_img").src = "./image/mipmap-xxhdpi/noise_well_n.png"
					break;
				case "2":
					localStorage.setItem('CE_modle', CE)
					document.getElementById("controlUI").style.display = "";
					document.getElementById('rangeValue').innerText = localStorage.getItem('CG_number')
					document.getElementById('myRange').value = localStorage.getItem('CG_number')
					document.getElementById("noise_close_img").src = "./image/mipmap-xxhdpi/noise_close_n.png"
					document.getElementById("noise_down_img").src = "./image/mipmap-xxhdpi/noise_down_n.png"
					document.getElementById("noise_well_img").src = "./image/mipmap-xxhdpi/noise_well_p.png"
					break;
			}
		}
		
		function pressing_init(arr) {
			switch(arr[0]) {
				case '0': 
					document.getElementById("pressspeed_text").textContent = '较慢'
					break;
				case '1':
					document.getElementById("pressspeed_text").textContent = '适中'
					break;
				case '2':
					document.getElementById("pressspeed_text").textContent = '较快'
					break;
			}
			switch(arr[1]) {
				case '0': 
					document.getElementById("presstime_text").textContent = '较慢'
					break;
				case '1':
					document.getElementById("presstime_text").textContent = '适中'
					break;
				case '2':
					document.getElementById("presstime_text").textContent = '较快'
					break;
			}
			
		}
		
		function multidevice_init(num) {
			switch(num) {
				case '0': 
					document.getElementById("multidevice_text").textContent = '在线静模式'
					break;
				case '1':
					document.getElementById("multidevice_text").textContent = '智能抢停式'
					break;
			}
		}

		function noise_number_ui_init(CE) {
			switch (CE) {
				case "1":
					document.getElementById('rangeValue').innerText = localStorage.getItem('CF_number')
					document.getElementById('myRange').value = localStorage.getItem('CF_number')
					break;
				case "2":
					document.getElementById('rangeValue').innerText = localStorage.getItem('CG_number')
					document.getElementById('myRange').value = localStorage.getItem('CG_number')
					break;
			}
		}

		function send_noise_number(CE, number) {
			switch (CE) {
				case "1":
					localStorage.setItem('CF_number', number)
					document.getElementById('rangeValue').innerText = number
					document.getElementById('myRange').value = number
					break;
				case "2":
					localStorage.setItem('CG_number', number)
					document.getElementById('rangeValue').innerText = number
					document.getElementById('myRange').value = number
					break;
			}
		}

		function UI_init() {
			Ear_V_Show(localStorage.getItem('CC_L_V'), localStorage.getItem('CC_R_V'), localStorage.getItem(
				'CC_C_V'))
			noise_ui_init()
		}
		
		
		function getData() {
			document.getElementById("eqselcet").textContent = eqselectstr(localStorage.getItem('CH_modle'))
			
			if (localStorage.getItem('CI_L_modle') == "1") {
				document.getElementById("L_setting_detiles").textContent = "语音助手"
			}else {
				document.getElementById("L_setting_detiles").textContent = "操作控制：" + noise_modle(localStorage.getItem('CI_modle_detial'))
			}
			
			if (localStorage.getItem('CI_R_modle') == "1") {
				document.getElementById("R_setting_detiles").textContent = "语音助手"
			}else {
				document.getElementById("R_setting_detiles").textContent = "操作控制：" + noise_modle(localStorage.getItem('CI_modle_detial'))
			}
			
			document.getElementById("L_setting_press").textContent = "按压力度：" + localStorage.getItem('CJ_L_press')
			document.getElementById("R_setting_press").textContent = "按压力度：" + localStorage.getItem('CJ_R_press')
			
			console.log(noise_modle(localStorage.getItem('CI_modle_detial')), localStorage.getItem('CJ_L_press'), localStorage.getItem('CJ_R_press'))
		}
		

		function analysis(data, fun) {
			console.log(JSON.stringify(data));
			document.getElementById("state").textContent = "已连接";
			// var arry = data.split('=');
			// console.log(arry[1]);
			document.getElementById("bt_name").textContent = data[0].CA;
			
			localStorage.setItem('CC_L_V', data[2].CC[0]);
			localStorage.setItem('CC_R_V', data[2].CC[1]);
			localStorage.setItem('CC_C_V', data[2].CC[2]);
			Ear_V_Show(data[2].CC[0], data[2].CC[1], data[2].CC[2]);
			
			if (data[3].CD == "1") {
				document.getElementById("earcheck").classList.add('mui-active');
			}
			
			noise_ui_init(data[4].CE)
			
			head_ui_init(data[14].CO)
			
			localStorage.setItem('CF_number', data[5].CF);
			noise_number_ui_init(localStorage.getItem('CE_modle'))
			
			
			localStorage.setItem('CG_number', data[6].CG);
			noise_number_ui_init(localStorage.getItem('CE_modle'))
			CI_L_modle = data[8].CI[0];
			CI_R_modle = data[8].CI[1];
			CI_modle_detial = data[8].CI[2];
			if(!localStorage.getItem("isEditStore")) {
				document.getElementById("eqselcet").textContent = eqselectstr(data[7].CH)

				
				if (CI_L_modle == "1") {
					document.getElementById("L_setting_detiles").textContent = "语音助手"
				} else {
					document.getElementById("L_setting_detiles").textContent = "操作控制：" + noise_modle(CI_modle_detial)
					
				}
				if (CI_R_modle == "1") {
					document.getElementById("R_setting_detiles").textContent = "语音助手"
				} else {
					document.getElementById("R_setting_detiles").textContent = "操作控制：" + noise_modle(CI_modle_detial)
				}
				localStorage.setItem('CI_L_modle', data[8].CI[0]);
				localStorage.setItem('CI_R_modle', data[8].CI[1]);
				localStorage.setItem('CI_modle_detial', data[8].CI[2]);
				
				CJ_L_press = data[9].CJ[0];
				CJ_R_press = data[9].CJ[1];
				document.getElementById("L_setting_press").textContent = "按压力度：" + CJ_L_press
				document.getElementById("R_setting_press").textContent = "按压力度：" + CJ_R_press
				// document.getElementById('LmyRange').value = CJ_L_press
				
				localStorage.setItem('CJ_L_press', data[9].CJ[0]);
				localStorage.setItem('CJ_R_press', data[9].CJ[1]);
			
			}else {
				
				typeof fun == 'function' && fun();
				setTimeout(() => {
					localStorage.setItem("isEditStore", false)
				}, 1000)
			}
			// if (data[10].CK == "1") {
			// 	document.getElementById("rouse").classList.add('mui-active');
			// }
			
			CP_speed = data[15].CP[0];
			CP_time = data[15].CP[1];
			pressing_init(data[15].CP)
			
			if (data[16].CQ[0] == "1") {
				CQ_spk = data[16].CQ[0]
				console.log('音量增强')
				document.getElementById("volumeenhancement").classList.add('mui-active');
			}
			if (data[16].CQ[1] == "1") {
				CQ_mic = data[16].CQ[1]
				console.log('送话增强')
				document.getElementById("speakenhancement").classList.add('mui-active');
			}
			
			multidevice_init(data[17].CR)
			
			
			
			LsetSilder()
			RsetSilder()
			// setTimeout(() => {
			// 	sessionStorage.setItem("plusreadyData", "")
			// }, 1000);
			// switch (arry[0]) {
			// 	case "CA":
			// 		document.getElementById("bt_name").textContent = arry[1];
			// 		break;
			// 	case "CC":
			// 		var v = arry[1].split(',');
			// 		localStorage.setItem('CC_L_V', v[0]);
			// 		localStorage.setItem('CC_R_V', v[1]);
			// 		localStorage.setItem('CC_C_V', v[2]);
			// 		Ear_V_Show(v[0], v[1], v[2]);
			// 		break;
			// 	case "CD":
			// 		if (arry[1] == "1") {
			// 			document.getElementById("earcheck").classList.add('mui-active');
			// 		}
			// 		break;
			// 	case "CE":
			// 		noise_ui_init(arry[1])
			// 		break;
			// 	case "CO":
			// 		head_ui_init(arry[1])
			// 		break;
			// 	case "CF":
			// 		localStorage.setItem('CF_number', arry[1]);
			// 		noise_number_ui_init(localStorage.getItem('CE_modle'))
			// 		break;
			// 	case "CG":
			// 		localStorage.setItem('CG_number', arry[1]);
			// 		noise_number_ui_init(localStorage.getItem('CE_modle'))
			// 		break;
			// 	case "CH":
			// 		// localStorage.setItem('CH_modle', arry[1]);
			// 		document.getElementById("eqselcet").textContent = eqselectstr(arry[1])
			// 		break;
			// 	case "CK":
			// 		if (arry[1] == "1") {
			// 			document.getElementById("rouse").classList.add('mui-active');
			// 		}
			// 		break;
			// 	case "CQ":
			// 		var v = arry[1].split(',');
			// 		if (v[1] == "1") {
			// 			document.getElementById("speakenhancement").classList.add('mui-active');
			// 		}
			// 		if (v[0] == "1") {
			// 			document.getElementdById("volumeenhancement").classList.add('mui-active');
			// 		}
			// 		break;
			// 	case "CI":
			// 		var v = arry[1].split(',');
			// 		console.log(v[0]);
			// 		CI_L_modle = v[0];
			// 		CI_R_modle = v[1];
			// 		CI_modle_detial = v[2];
			// 		if (CI_L_modle == "1") {
			// 			document.getElementById("L_setting_detiles").textContent = "语音助手"
			// 		} else {
			// 			document.getElementById("L_setting_detiles").textContent = "操作控制：" + noise_modle(CI_modle_detial)
			// 		}
			// 		if (CI_R_modle == "1") {
			// 			document.getElementById("R_setting_detiles").textContent = "语音助手"
			// 		} else {
			// 			document.getElementById("R_setting_detiles").textContent = "操作控制：" + noise_modle(CI_modle_detial)
			// 		}
			// 		localStorage.setItem('CI_L_modle', v[0]);
			// 		localStorage.setItem('CI_R_modle', v[1]);
			// 		localStorage.setItem('CI_modle_detial', v[2]);
			// 		break;
			// 	case "CJ":
			// 		var v = arry[1].split(',');
			// 		CJ_L_press = v[0];
			// 		CJ_R_press = v[1];
			// 		document.getElementById("L_setting_press").textContent = "按压力度：" + CJ_L_press
			// 		document.getElementById("R_setting_press").textContent = "按压力度：" + CJ_R_press
			// 		localStorage.setItem('CJ_L_press', v[0]);
			// 		localStorage.setItem('CJ_R_press', v[1]);
			// 		break;
			// 	case "CP":
			// 		var v = arry[1].split(',');
			// 		CP_speed = v[0];
			// 		CP_time = v[1];
			// 		break;
			// 	case "CQ":
			// 		var v = arry[1].split(',');
			// 		CQ_spk = v[0];
			// 		CQ_mic = v[1];
			// 		break;
			// 	case "CE":
			// 		CE = arry[1];
			// 		localStorage.setItem('CE_modle', CE);

			// 		break;
			// 	case "VER":
			// 		//Q4_ZJ_A19_M01A_EQ0_156_multilink
			// 		var v = arry[1].split('_');
			// 		ver = arry[1];
			// 		version = v[5];
			// 		break;
			// }
		}
		dsBridge.register("syn", {
			tag: "syn",
			addValue: function(r, l) {
				return r + l;
			},
			getInfo: function() {
				return {
					tag: this.tag,
					value: 8
				}
			},
			SPPReceive: function(data) {
				analysis(data);
			}

		})

		function SPPReceive(data) {
			analysis(data);
		};

		document.getElementById('pressLsetting').addEventListener('tap', function() {
			var picker = new mui.PopPicker();
			picker.setData([{
				value: '1',
				text: '语音助手'
			}, {
				value: '5',
				text: '噪声控制'
			}]);
			picker.show(function(items) {
				// userResult.innerText = JSON.stringify(items[0]);
				if(items[0].value == '1') {
					localStorage.setItem('CI_L_modle', 1);
					document.getElementById("L_setting_detiles").textContent = items[0].text
				}else {
					localStorage.setItem('CI_L_modle', 5);
					document.getElementById("L_setting_detiles").textContent = noise_modle(localStorage.getItem('CI_modle_detial'))
				}
				// CI_L_modle = items[0].value
				SPP_sendAT("AT+CI=" + localStorage.getItem('CI_L_modle') + "," + localStorage.getItem('CI_R_modle') +
					"," + localStorage.getItem('CI_modle_detial') +
					"\r\n");
				// SPP_sendAT("AT+CP=" + CP_speed + "," + CP_time + "\r\n")
			});

			// document.getElementById('LmyRange').addEventListener('change', function() {
			// 	console.log('123')
			// 	var rangeValue = document.getElementById('LmyRange').value;
			// 	console.log(rangeValue)
			// 	document.getElementById('LrangeValue').innerText = rangeValue;
			// 	localStorage.setItem('CJ_L_press', rangeValue);
			// 	SPP_sendAT("AT+CJ=" + localStorage.getItem('CJ_L_press') + "," + localStorage.getItem('CJ_R_press') +
			// 		"\r\n");
			// });
			

			// routerUrl = 'examples/presssetingL.html'
			// window.location.href = routerUrl; 
			// localStorage.setItem("isEditStore", true)

			// localStorage.setItem('routerUrl', 'examples/presssetingL.html')
		});
		document.getElementById('pressRsetting').addEventListener('tap', function() {
			// mui.openWindow({
			// 	url: 'examples/presssetingR.html',
			// 	id: 'openWindow_recieve_page',
			// 	extras: {
			// 		name: '右耳按键'
			// 	}
			// });

			// routerUrl = 'examples/presssetingR.html'
			// window.location.href = routerUrl; 
			// localStorage.setItem("isEditStore", true)
			var picker = new mui.PopPicker();
			picker.setData([{
				value: '1',
				text: '语音助手'
			}, {
				value: '5',
				text: '噪声控制'
			}]);
			picker.show(function(items) {
				// userResult.innerText = JSON.stringify(items[0]);
				if(items[0].value == '1') {
					document.getElementById("R_setting_detiles").textContent = items[0].text
					localStorage.setItem('CI_R_modle', 1);
				}else {
					document.getElementById("R_setting_detiles").textContent = noise_modle(localStorage.getItem('CI_modle_detial'))
					localStorage.setItem('CI_R_modle', 5);
				}
				// CI_L_modle = items[0].value
				SPP_sendAT("AT+CI=" + localStorage.getItem('CI_L_modle') + "," + localStorage.getItem('CI_R_modle') +
					"," + localStorage.getItem('CI_modle_detial') +
					"\r\n");
				// SPP_sendAT("AT+CP=" + CP_speed + "," + CP_time + "\r\n")
			});


			// localStorage.setItem('routerUrl', 'examples/presssetingR.html')
		});
		document.getElementById('eqselect').addEventListener('tap', function() {
			// routerUrl = 'examples/eqselect.html'
			// localStorage.setItem('routerUrl', 'examples/eqselect.html')
			// console.log(routerUrl)
			// return
			// mui.openWindow({
			// 	url: 'examples/eqselect.html',
			// 	id: 'openWindow_recieve_page',
			// 	extras: {
			// 		name: '音效'
			// 	}
			// });
			
			var picker = new mui.PopPicker();
			picker.setData([{
				value: '0',
				text: '均衡'
			},{
				value: '1',
				text: '低音增强'
			},{
				value: '2',
				text: '柔和高音'
			},{
				value: '3',
				text: '人声增强'
			},{
				value: '4',
				text: '轻音乐'
			}, {
				value: '5',
				text: '高音&低音'
			}]);
			picker.show(function(items) {
				// userResult.innerText = JSON.stringify(items[0]);
				// CI_L_modle = items[0].value
				SPP_sendAT(`AT+CH=${items[0].value}\r\n`);
				document.getElementById("eqselcet").textContent = eqselectstr(items[0].value)
				// SPP_sendAT("AT+CP=" + CP_speed + "," + CP_time + "\r\n")
			});
			
			// window.location.href = routerUrl; 
			// localStorage.setItem("isEditStore", true)
			
		});
		// document.getElementById("rouse").addEventListener("toggle", function(event) {
		// 	if (!event.detail.isActive) {
		// 		// console.log("AT+CK=0\r\n");
		// 		// SPP_sendAT("AT+CK=0\r\n");
		// 		uni.postMessage({
		// 		    data: {
		// 		        instructions: "AT+CK=0\r\n", // 这是传的参数
		// 		    },
		// 		});
		// 	} else {
		// 		// console.log("AT+CK=1\r\n");
		// 		// SPP_sendAT("AT+CK=1\r\n");
		// 		uni.postMessage({
		// 		    data: {
		// 		        instructions: "AT+CK=1\r\n", // 这是传的参数
		// 		    },
		// 		});
		// 	}
		// });
		document.getElementById("volumeenhancement").addEventListener("toggle", function(event) {
			if (!event.detail.isActive) {
				// console.log("AT+CD=0\r\n");
				CQ_spk = 0;
				SPP_sendAT("AT+CQ=" + CQ_spk + "," + CQ_mic + "\r\n");
				
			} else {
				// console.log("AT+CD=1\r\n");
				CQ_spk = 1;
				SPP_sendAT("AT+CQ=" + CQ_spk + "," + CQ_mic + "\r\n");
			}

		});
		document.getElementById("speakenhancement").addEventListener("toggle", function(event) {
			if (!event.detail.isActive) {
				// console.log("AT+CQ=0\r\n");
				CQ_mic = 0;
				SPP_sendAT("AT+CQ=" + CQ_spk + "," + CQ_mic + "\r\n");
			} else {
				// console.log("AT+CQ=1\r\n");
				CQ_mic = 1;
				SPP_sendAT("AT+CQ=" + CQ_spk + "," + CQ_mic + "\r\n");
			}
		});
		document.getElementById("earcheck").addEventListener("toggle", function(event) {
			if (!event.detail.isActive) {
				// console.log("AT+CD=0\r\n");
				SPP_sendAT("AT+CD=0\r\n");
			} else {
				// console.log("AT+CD=1\r\n");
				SPP_sendAT("AT+CD=1\r\n");
			}
		});
		// document.getElementById("restoredefault").addEventListener('tap', function() {
		// 	var btnArray = ['确定', '取消'];
		// 	mui.confirm('点击确定后耳机所有功能回复默认，请确认是否恢复', '恢复默认设置', btnArray, function(e) {
		// 		if (e.index == 1) {
		// 			// console.log("AT+CZ\r\n");
		// 			SPP_sendAT("AT+CZ\r\n");
		// 		} else {

		// 		}
		// 	})
		// });

		document.getElementById("changename").addEventListener('tap', function(e) {
			e.detail.gesture.preventDefault(); //修复iOS 8.x平台存在的bug，使用plus.nativeUI.prompt会造成输入法闪一下又没了
			var name = document.getElementById("bt_name")
			var btnArray = ['取消', '确定'];
			mui.prompt('耳机重连后生效', '请输入新的蓝牙名称', '修改耳机名称', btnArray, function(e) {
				if (e.index == 1) {
					var patrn=/[\u4E00-\u9FA5]|[\uFE30-\uFFA0]/gi;
					
					if(!patrn.exec(e.value)) {
						SPP_sendAT("AT+CA=" + e.value + "\r\n")
						uni.postMessage({
						    data: {
						        equipmentName: e.value, // 这是传的参数
						    },
						});
						name.textContent = e.value
					}else {
						alert("暂不支持中文")
						return
					}
					
					
					
				} else {
					// info.innerText = '你点了取消按钮';
				}
			})
		});
		// 关闭降噪
		document.getElementById("noise_close").addEventListener('tap', function(e) {
			e.detail.gesture.preventDefault(); //修复iOS 8.x平台存在的bug，使用plus.nativeUI.prompt会造成输入法闪一下又没了
			SPP_sendAT("AT+CE=0\r\n")
			noise_ui_init("0")
			
		});
		document.getElementById("noise_down").addEventListener('tap', function(e) {
			e.detail.gesture.preventDefault(); //修复iOS 8.x平台存在的bug，使用plus.nativeUI.prompt会造成输入法闪一下又没了
			noiseState = 1
			SPP_sendAT("AT+CE=1\r\n")
			// uni.postMessage({
			//     data: {
			//         instructions: "AT+CE=<1>\r\n", // 这是传的参数
			//     },
			// });
			noise_ui_init("1")
		});
		document.getElementById("noise_well").addEventListener('tap', function(e) {
			e.detail.gesture.preventDefault(); //修复iOS 8.x平台存在的bug，使用plus.nativeUI.prompt会造成输入法闪一下又没了
			noiseState = 2
			SPP_sendAT("AT+CE=2\r\n")
	
			setTimeout(() => {
				readNoiseState()
			}, 500)
			noise_ui_init("2")
		});

		// 读取噪声状态
		function readNoiseState() {
			uni.postMessage({
			    data: {
			        instructions: "AT+CE\r\n", // 这是传的参数
			    },
			});
		}



		document.getElementById("head_close").addEventListener('tap', function(e) {
			e.detail.gesture.preventDefault(); //修复iOS 8.x平台存在的bug，使用plus.nativeUI.prompt会造成输入法闪一下又没了
			uni.postMessage({
			    data: {
			        instructions: "AT+CO=0\r\n", // 这是传的参数
			    },
			});
			// SPP_sendAT("AT+CO=0\r\n")
			head_ui_init("0")
		});
		document.getElementById("head_hold").addEventListener('tap', function(e) {
			e.detail.gesture.preventDefault(); //修复iOS 8.x平台存在的bug，使用plus.nativeUI.prompt会造成输入法闪一下又没了
			uni.postMessage({
			    data: {
			        instructions: "AT+CO=1\r\n", // 这是传的参数
			    },
			});
			// SPP_sendAT("AT+CO=1\r\n")
			head_ui_init("1")
		});
		document.getElementById("head_doing").addEventListener('tap', function(e) {
			e.detail.gesture.preventDefault(); //修复iOS 8.x平台存在的bug，使用plus.nativeUI.prompt会造成输入法闪一下又没了
			SPP_sendAT("AT+CO=2\r\n")
			head_ui_init("2")
		});

		document.getElementById('pressspeed').addEventListener('tap', function(event) {
			var picker = new mui.PopPicker();
			picker.setData([{
				value: '0',
				text: '较慢'
			}, {
				value: '1',
				text: '适中'
			}, {
				value: '2',
				text: '较快'
			}]);
			picker.show(function(items) {
				// userResult.innerText = JSON.stringify(items[0]);
				document.getElementById("pressspeed_text").textContent = items[0].text
				CP_speed = items[0].value
				SPP_sendAT("AT+CP=" + CP_speed + "," + CP_time + "\r\n")
			});
		}, false);
		document.getElementById('presstime').addEventListener('tap', function(event) {
			var picker = new mui.PopPicker();
			picker.setData([{
				value: '0',
				text: '较慢'
			}, {
				value: '1',
				text: '适中'
			}, {
				value: '2',
				text: '较快'
			}]);

			picker.show(function(items) {
				document.getElementById("presstime_text").textContent = items[0].text
				CP_time = items[0].value
				SPP_sendAT("AT+CP=" + CP_speed + "," + CP_time + "\r\n")
			});
		}, false);
		
		document.getElementById('multidevicetable').addEventListener('tap', function(event) {
			var picker = new mui.PopPicker();
			picker.setData([{
				value: '0',
				text: '在线静模式'
			}, {
				value: '1',
				text: '智能抢停式'
			}]);
			picker.show(function(items) {
				document.getElementById("multidevice_text").textContent = items[0].text
				SPP_sendAT("AT+CR=" + items[0].value + "\r\n")
			});
		}, false);

		document.getElementById('gamemode').addEventListener('tap', function(event) {
			var picker = new mui.PopPicker();
			picker.setData([{
				value: '0',
				text: '关闭'
			}, {
				value: '1',
				text: '延时低'
			}, {
				value: '2',
				text: '延时中'
			}, {
				value: '3',
				text: '延时高'
			}]);
			picker.show(function(items) {
				document.getElementById("gamemode_text").textContent = items[0].text
				SPP_sendAT("AT+CL=" + items[0].value + "\r\n")
			});
		}, false);
		

		document.getElementById('myRange').addEventListener('change', dedounce(function() {
			var rangeValue = document.getElementById('myRange').value;
			document.getElementById('rangeValue').innerText = rangeValue;
			
			if(noiseState == 1) {
				SPP_sendAT("AT+CF=" + rangeValue + "\r\n")
			}else {
				SPP_sendAT("AT+CG=" + rangeValue + "\r\n")
			}
			send_noise_number(localStorage.getItem('CE_modle'), rangeValue)
		}, 500));
		
		 
		
		function dedounce(func, delay) {
			let timer = null;
			return function () {
				clearTimeout(timer);
				timer = setTimeout(() => {
					func.apply(this, arguments);
				}, delay);
			}
		}
	</script>
</html>