<template>
	<view class="content">
		<view class="top">
			<!-- <view class="share"> -->
				<!-- <button class="copycode" open-type="share" @click="">分享给好友</button> -->
				<!-- <u-icon name="share" @click="share" size="50" style="margin-right: 20rpx"></u-icon> -->
				<!-- <u-icon name="scan" @click="scanCode" size="50"></u-icon> -->
			<!-- </view> -->
			<view class="devicebox" v-if="maskShow">
				<image src="../../../static/img/leida.gif" mode=""></image>
			</view>
			<!-- <view class="devicebox" @click="init()" v-if="maskShow == false && raebox == false">
				<view class="deviceimg">
					<image src="../../../static/ear1.png" mode=""></image>
				</view>
				<view class="devicetitle">点击添加设备</view>
			</view> -->
			<view class="raebox" v-if="raebox">
				<view class="raebox_left">
					<image src="../../../static/img/ic_huashang_logo.png" mode="aspectFit"></image>
				</view>
				<view class="raebox_right">
						<view class="name">{{ earname }}</view>
					<view class="container">
						<view class="container_1">
							<view class="container_2">
								<text>Ⓛ</text>
								<image :src="earlv_img" style="width: 30px; height: 16px"></image>
							</view>
							<view class="">
								<text>{{ earleft }}</text>
								%
							</view>
						</view>
						<view class="container_1">
							<view class="container_2">
								<text>Ⓡ</text>
								<image :src="earrv_img" style="width: 30px; height: 16px"></image>
							</view>
							<view class="">
								<text>{{ earright }}</text>
								%
							</view>
						</view>
						<view class="container_1">
							<view class="container_2">
								<text>Ⓒ</text>
								<image :src="warehouselv_img" style="width: 30px; height: 16px"></image>
							</view>
							<view class="">
								<text>{{ warehouselv }}</text>
								%
							</view>
						</view>
					</view>
					<view>
						<view class="set" @click="tourl">{{ isset }}</view>
						<view class="ear">{{ isconnected }}</view>
					</view>
				</view>
			</view>
			<view class="select" v-if="false">
				<view :class="index == selindex ? 'selbox sel' : ''" v-for="(item, index) in sellist" :key="index" @click="selcli(item.app, index)">
					{{ item.name }}
				</view>
			</view>
			<view class="whole" v-if="selindex == 0&&false" @tap="showDropdown" :class="border == true ? 'borderradio' : ''">
				<!-- <image src="../../static/whole.svg" mode=""></image> -->
				{{ selectedItem }}
				<image src="../../../static/down.svg" mode=""></image>
			</view>
			<!-- <view class="dropdown-list borderradio" v-if="showList">
				<view class="dropdown-item" v-for="(item, index) in listData" @tap="selectItem(item)" :key="index" :class="{ selected: selectedItem === item }">
					{{ item.dictLabel }}
				</view>
			</view> -->
			<view class="boslist" v-if="false">
				<view class="box" @click="go(item)" v-for="(item, index) in tabindexlist" :key="index" v-if="item.status == 0">
					<view class="title">{{ item.appName }}</view>
					<image :src="item.appIcon" mode=""></image>
				</view>
			</view>
		</view>
		<ai-ku>
			<template v-slot:func>
				<u-icon @click="hrefTo" name="grid-fill" style="width: 36rpx;height: 36rpx;margin-right:8rpx" color="#4095e5">
				</u-icon>
			</template>
		</ai-ku>
		<view v-if="maskShow" class="uni-mask">
			<scroll-view class="uni-scroll_box" scroll-y >
				<view class="uni-title">蓝牙列表</view>
				<view class="icon" @click="maskclose()">
					<u-icon name="close" color="#000" size="28"></u-icon>
				</view>
				<view class="uni-list-box" v-for="(item, index) in bluetoothList" :key="index" @click="confirmConn(item)" v-if="item.name">
					<!-- <image src="../../static/images/earlist.png" mode="" ></image> -->
					<view class="uni-list_name" style="font-size: 35rpx">{{ item.name || '未知设备' }}</view>
					<view class="uni-list_name">{{ item.deviceId || '无' }}</view>
				</view>
			</scroll-view>
		</view>
		<popup v-if="showNotice" :title="popTitle" :content="popContent" @closePop="closePop"></popup>
		<!-- <lf-tabbar :active="0" :count="messegeNum"></lf-tabbar> -->
	</view>
</template>

<script>
import { getDictList, getAppsByRemarkAndHeadphoneType, 
getAppsByRemark, getPop, miniConfig } from "@/api/device.js";
import aiKu from '@/pages/tabbar/aiku/index.vue'
import { mapGetters } from 'vuex';
const bt = uni.requireNativePlugin('Common-BT');
const bluetooth = uni.requireNativePlugin('LY-Bluetooth');
export default {
	data() {
		return {
			earname: 'BT-CC', //蓝牙名字
			isconnected: '未连接', //ble蓝牙是否连接
			earleft: '0', //左耳电池电量
			earlv_img: '../../../static/img/ic_battery_20.png', //左耳电池图片
			earright: '0', //右耳电池电量
			earrv_img: '../../../static/img/ic_battery_20.png', //右耳电池图片
			warehouselv: '0', //电池盒电量
			warehouselv_img: '../../../static/img/ic_battery_20.png', //电池盒电量图片
			val: '', //蓝牙返回的数据
			openear: false,
			showNotice: false,
			popTitle: '',
			popContent: '',
			urltit: '', //扫码后跳转地址
			raebox: true, //设置耳机的盒子
			bluetoothList: [], //搜索列表
			maskShow: false, //搜索列表遮盖层
			imgshow: false, //搜索雷达gif图
			sellist: [
				{
					name: '蓝牙设备',
					app: 'app_ble'
				},
				{
					name: '智汇之城',
					app: 'app_zhcc'
				},
				{
					name: '天空之城',
					app: 'app_bbs'
				}
			],
			selindex: '0',
			selectedItem: '所有设备',
			tabindexlist: [],
			listData: ['所有设备', '蓝牙耳机', '挂脖耳机', '夹耳耳机'], // 下拉列表的数据
			border: true,
			showList: false, // 控制下拉列表的显示和隐藏
			bluetoothObj: {},
			services: [],
			serviceId: 0,
			writeCharacter: false,
			readCharacter: false,
			notifyCharacter: false,
			earconnectivity: false, //监听蓝牙是否连接
			firstvalue: '', //上一次连接的耳机
			isset: '点击添加', //设置

			newServiceId: '',
			devicesList: [],

			connectNum: 0, // 链接次数

			intervalTime: null,

			reconnectionDevicesId: '', // 重连服务id
			reconnectionNum: 0, // 重连次数
			intervalId: null,
			
			queryInstructionsArr: ['AT+CA\r\n', 'AT+CC\r\n', 'AT+CD\r\n', 'AT+CE\r\n', 'AT+CF\r\n', 'AT+CG\r\n', 'AT+CH\r\n', 'AT+CI\r\n', 'AT+CJ\r\n', 'AT+CK\r\n',
			'AT+CL\r\n', 'AT+CM\r\n', 'AT+CN\r\n', 'AT+CO\r\n', 'AT+CP\r\n', 'AT+CQ\r\n', 'AT+CR\r\n', 'AT+CS\r\n', 'AT+CT=\r\n'],
			instructions16hexStr: '',
			isConnecting: false,
			isClickSearch: false,
			connStatus: 1, // 连接状态；1-未连接，2-连接中，3-已连接
			shakeNum: 0, // 握手次数
		};
	},
	watch: {},
	components: {aiKu},
	computed: {
		...mapGetters(['messegeNum'])
	},
	onLoad() {
		let that = this

		// uni.removeStorageSync('bluetoothObj');
		console.log(uni.getStorageSync('bluetoothObj'));

		// 初始化ble适配器
		// this.startBluetooth()

		uni.$on('messageFromH5', this.throttle((data) => {
			console.log('A页面传的值为:' + JSON.stringify(data));
			this.writeBLE(data.instructions);
		}, 300));
		uni.$on('equipmentName', (data) => {
			console.log('A页面传的设备名称为:' + JSON.stringify(data));
			that.earname = data.equipmentName
			that.bluetoothObj.name = data.equipmentName
			uni.setStorageSync('bluetoothObj', that.bluetoothObj)
			// this.writeBLE(data.);
		});
	},
	onShow() {
		//判断是否可重连
		if (this.connStatus === 1) {
			this.firstlink();
		}
		this.getPop();
		this.applist(this.sellist[this.selindex].app);
		// this.tablist(4);
	},
	onHide: function () {
		// 页面隐藏时关闭加载提示框
		uni.hideLoading();
		this.maskclose();
	},
	onUnload: function () {
		// 页面卸载时关闭加载提示框
		uni.hideLoading();
		this.isConnecting = false;
		this.bluetoothList = [];
		this.connStatus = 1;
	},
	onReachBottom() {
	},
	onPullDownRefresh() {
		this.getPop();
		this.applist(this.sellist[this.selindex].app);
		// this.tablist(4);
		uni.stopPullDownRefresh();
	},
	methods: {
		throttle(fn, interval) {
		  // last为上一次触发回调的时间
		  let last = 0
		  // 将throttle处理结果当作函数返回
		  return function () {
		      // 保留调用时的this上下文
		      let context = this
		      // 保留调用时传入的参数
		      let args = arguments
		      // 记录本次触发回调的时间
		      let now = new Date()
		      // 判断上次触发的时间和本次触发的时间差是否小于时间间隔的阈值
			  console.log('last', last);
			  console.log('now', now);
			  console.log('now - last', (now - last));
		      if (now - last >= interval) {
		      // 如果时间间隔大于我们设定的时间间隔阈值，则执行回调
		          last = now;
		          fn.apply(context, args);
		      }
		    }
		},
		// 重构蓝牙模块
		startBluetooth(fun) {
			let that = this;
			console.log('重构蓝牙模块        startBluetooth');
			// 初始化蓝牙适配器
			uni.openBluetoothAdapter({
				success: (res) => {
					typeof fun == 'function' && fun();
				},
				fail: (err) => {
					if (err.errCode == 10001) {
					  uni.showModal({
						  title: '温馨提示',
						  content: '请检查手机蓝牙是否开启',
						  showCancel: false
					  });
					}
					console.error('init bluetooth adapter failed:', err);
					
					that.isClickSearch = false;
				},
				complete: (res) => {
					console.log('openBluetoothAdapter的complete-------------------------------------------------', JSON.stringify(res));
					uni.getBluetoothAdapterState({
						complete: (res) => {
							console.log('getBluetoothAdapterState的complete-------------------------------------------------', JSON.stringify(res));
						}
					});
				}
			});
		},

		// 开始搜寻附近蓝牙设备
		startBluetoothDevicesDiscovery(fun) {
			let that = this;
			uni.startBluetoothDevicesDiscovery({
				allowDuplicatesKey: true,
				success: (res) => {
					// 开启搜索界面
					that.maskShow = true;
					that.raebox = false;
					
					that.isClickSearch = false;
					console.log(res);
					fun();
				},
				fail: (err) => {
					that.isClickSearch = false;
					uni.showToast({
						title: '搜索开启失败，请重试',
						icon: 'none'
					});
					console.log(err);
				}
			});
		},

		// 监听寻找到新设备的事件
		// onBluetoothDeviceFound(fun) {
		// 	uni.onBluetoothDeviceFound((res) => {
		// 		fun(res)
		// 	})
		// },

		getBluetoothAdapterState() {
			let that = this;

			uni.getBluetoothAdapterState({
				success: (res) => {
					console.log(res);
					if (res.available) {
						that.connect();
					}
				},
				fail: (err) => {
					console.log(err);
					that.startBluetooth(that.connect());
				}
			});
		},

		// 搜索蓝牙设备
		searchBle() {
			let that = this;
			
			let bluetoothObj = uni.getStorageSync('bluetoothObj');
			
			that.startBluetoothDevicesDiscovery(() => {
				let isFirst = true;
				uni.onBluetoothDeviceFound((res) => {
					// console.log('发现新设备', JSON.stringify(res));
					let pairedDevice = res.devices[0];
					// console.log('bluetoothList', JSON.stringify(that.bluetoothList));
					let checkBle = that.bluetoothList.findIndex((item) => item.deviceId == res.devices[0].deviceId);
					if (checkBle == -1) {
						that.bluetoothList.unshift(pairedDevice);
					}
					
					if(isFirst && bluetoothObj && bluetoothObj.deviceId && res.devices[0].deviceId == bluetoothObj.deviceId) {
						isFirst = false;
						that.connect(res.devices[0]);
					}
				});
			});
		},
		confirmConn(item) {
			let that = this;
			uni.showModal({
				title: '温馨提示',
				content: '您确认连接【' + (item.name ? item.name : '未知设备') + '】设备吗？',
				success(res) {
					if (res.confirm) {
						that.connect(item);
					}
				}
			});
		},
		connect(item) {
			let that = this;
			if (that.isConnecting) {
				console.log('有设备正在连接中，请稍后');
				console.log('当前连接中的设备：', that.bluetoothObj.deviceId);
				return;
			}
			that.isConnecting = true;
			// 连接蓝牙设备
			uni.showLoading({
				title: '连接中...',
				mask: true
			});
			that.connStatus = 2;
			console.log(item);
			that.bluetoothObj = item;
			console.log(that.bluetoothObj);
			// uni.setStorageSync('bluetoothObj', item);
			uni.createBLEConnection({
				deviceId: that.bluetoothObj.deviceId,
				timeout: 8000,
				success: (res) => {
					console.log(res);
					//蓝牙连接方法体闭包
					function connectFun() {
						// uni.stopBluetoothDevicesDiscovery();
							
						uni.getBLEDeviceServices({
							deviceId: that.bluetoothObj.deviceId, // 设备ID
							success(res) {
								
								for (let i = 0; i < res.services.length; i++) {
									console.log('serviceId：' + res.services[i].uuid);
									if (res.services[i].uuid == '0177F5DA-0000-1000-8000-00805F9B34FB' || res.services[i].uuid == '5052494D-2DAB-0341-6972-6F6861424C45') {
										that.newServiceId = res.services[i].uuid; //获取到特定的服务
										// that.deviceId=deviceId;
										setTimeout(() => {
											uni.getBLEDeviceCharacteristics({
												deviceId: that.bluetoothObj.deviceId, // 设备ID，在前面已经获取到
												serviceId: that.newServiceId, // 服务UUID，在前面已经获取到
												success(res) {
													uni.hideLoading();
													that.connStatus = 3;
													uni.showToast({
														title: 'BLE连接成功'
													}); 
													if (!that.intervalTime) {
														clearInterval(that.intervalTime);
													}
													// console.log(res);
													// uni.hideLoading()
													// return
													for (let i = 0; i < res.characteristics.length; i++) {
														let item = res.characteristics[i];
														console.log('characteristics：' + item.uuid);
														if (item.uuid == '00008888-0000-1000-8000-00805F9B34FB') {
															that.readcharacteristicId = item.uuid;
															that.startNotify(item);
														}
														
														if (item.uuid == '43484152-2DAB-3141-6972-6F6861424C45') {
															that.readcharacteristicId = item.uuid;
															that.startNotifyTwo(item);
														}
					
														if (item.uuid == '00007777-0000-1000-8000-00805F9B34FB') {
															that.writecharacteristicId = item.uuid;
														}
													}
													that.isConnecting = false;
												},
												fail(err) {
													that.isConnecting = false;
													console.error(err);
													uni.hideLoading();
													that.connStatus = 1;
												}
											});
										});
									}
								}
							},
							fail(err) {
								that.isConnecting = false;
								console.error(err);
								uni.hideLoading();
								that.connStatus = 1;
							}
						});
					}
					
					let systemInfo = uni.getSystemInfoSync();
					console.log('systemInfo', systemInfo);
					console.log('systemInfo.osName', systemInfo.osName);
					
					if(systemInfo.osName != 'ios') {
						setTimeout(() => {
							uni.setBLEMTU({
								deviceId: that.bluetoothObj.deviceId,
								mtu: 364,
								success: res => {
									console.log("mtu设置成功");
									setTimeout(() => {
										connectFun();
									}, 3000)
								}
							})
						}, 1000)
					}else {
						setTimeout(() => {
							connectFun();
						}, 3000)
					}
					
					
				},
				fail: (err) => {
					that.isConnecting = false;
					uni.hideLoading();
					that.connStatus = 1;
					// that.reconnectionNum++;
					uni.removeStorageSync('bluetoothObj');
					console.log(err);
					uni.showToast({
						title: '连接失败',
						icon: 'none'
					})
					/* if (that.reconnectionNum <= 3) {
						that.connect(item);
					} else {
						uni.removeStorageSync('bluetoothObj');
					} */
				},
				// complete: (res) => {
				// 	// that.connectNum++
				// 	uni.hideLoading();
				// }
			});
		},

		writeBLE(data) {
			let that = this;
			if (!that.readcharacteristicId) {
				return;
			}
			// const buffer = [0x41, 0x54, 0x2b, 0x43, 0x45, 0x3d, 0x3c, 0x32, 0x32, 0x0d, 0x0a];
			console.log('that.bluetoothObj.deviceId', that.bluetoothObj.deviceId);
			console.log('that.newServiceId', that.newServiceId);
			console.log('that.readcharacteristicId', that.readcharacteristicId);
			console.log('that.generate_command(data)', that.generate_command(data));
			uni.writeBLECharacteristicValue({
				deviceId: that.bluetoothObj.deviceId,
				serviceId: that.newServiceId,
				characteristicId: that.readcharacteristicId,
				value: that.generate_command(data),
				writeType: 'write',
				success: function (res) {
					console.log('已发送指令', JSON.stringify(res));
				},
				fail: function (res) {
					console.log('发送指令时报错：', JSON.stringify(res));
					uni.showToast({
						title: '发送失败，可能蓝牙目前没有连接',
						icon: 'none'
					});
				}
			});
		},
		
		writeBLETwo(data) {
			let that = this;
			if (!that.readcharacteristicId) {
				return;
			}
			// const buffer = [0x41, 0x54, 0x2b, 0x43, 0x45, 0x3d, 0x3c, 0x32, 0x32, 0x0d, 0x0a];
			console.log('that.bluetoothObj.deviceId', that.bluetoothObj.deviceId);
			console.log('that.newServiceId', that.newServiceId);
			console.log('that.readcharacteristicId', that.readcharacteristicId);
			// console.log('that.hex2ab(data)', that.hex2ab(data));
			uni.writeBLECharacteristicValue({
				deviceId: that.bluetoothObj.deviceId,
				serviceId: that.newServiceId,
				characteristicId: '43484152-2DAB-3241-6972-6F6861424C45',
				value: that.hex2ab(data),
				writeType: 'write',
				success: function (res) {
					console.log('已发送指令', JSON.stringify(res));
				},
				fail: function (res) {
					console.log('发送指令时报错：', JSON.stringify(res));
					uni.showToast({
						title: '发送失败，可能蓝牙目前没有连接',
						icon: 'none'
					});
				}
			});
		},
		
		writeBLEThree(data) {
			let that = this;
			if (!that.readcharacteristicId) {
				return;
			}
			// const buffer = [0x41, 0x54, 0x2b, 0x43, 0x45, 0x3d, 0x3c, 0x32, 0x32, 0x0d, 0x0a];
			let hexStrData = that.stringToHex(data);
			let bitNum = hexStrData.split(' ').length + 2;
			if (bitNum < 10) {
				bitNum = '0' + bitNum;
			}
			let result = '05 5A ' + bitNum + ' 00 01 38 ' + hexStrData;
			console.log('that.bluetoothObj.deviceId', that.bluetoothObj.deviceId);
			console.log('that.newServiceId', that.newServiceId);
			console.log('that.readcharacteristicId', that.readcharacteristicId);
			// console.log('that.hex2ab(data)', that.hex2ab(result));
			uni.writeBLECharacteristicValue({
				deviceId: that.bluetoothObj.deviceId,
				serviceId: that.newServiceId,
				characteristicId: '43484152-2DAB-3241-6972-6F6861424C45',
				value: that.hex2ab(result),
				writeType: 'write',
				success: function (res) {
					console.log('已发送指令', JSON.stringify(res));
				},
				fail: function (res) {
					console.log('发送指令时报错：', JSON.stringify(res));
					uni.showToast({
						title: '发送失败，可能蓝牙目前没有连接',
						icon: 'none'
					});
				}
			});
		},

		startNotify(item) {
			let that = this;
			uni.notifyBLECharacteristicValueChange({
				deviceId: that.bluetoothObj.deviceId,
				serviceId: that.newServiceId,
				characteristicId: that.readcharacteristicId,
				state: true,
				success(res) {
					console.log(res);
					that.onBLECharacteristicValueChange();
					setTimeout(() => {
						that.writeBLE('AT+CC\r\n');
					}, 600);
					setTimeout(() => {
						that.writeBLE('AT+CA\r\n');
					}, 300);

					console.log('NotifyBLECharacteristic: success');
				},
				fail(res) {
					console.log('NotifyBLECharacteristic: fail', res);
				}
			});
		},
		stringToHex(str){  
　　　　    var val="";  
　　　　    for(var i = 0; i < str.length; i++){  
　　　　　　  if(val == "")  
　　　　　　　　    val = str.charCodeAt(i).toString(16);  
　　　　　　  else  
　　　　　　　　    val += "," + (str.charCodeAt(i).toString(16).length < 2 ? '0' + str.charCodeAt(i).toString(16) : str.charCodeAt(i).toString(16));  
　　　　    }
			
　　　　     return val.replaceAll(',', ' ');  
		},
		startNotifyTwo(item) {
			let that = this;
			uni.notifyBLECharacteristicValueChange({
				deviceId: that.bluetoothObj.deviceId,
				serviceId: that.newServiceId,
				characteristicId: that.readcharacteristicId,
				state: true,
				success(res) {
					console.log(res);
					that.onBLECharacteristicValueChange();
					
					setTimeout(() => {
						that.shakeNum = 1;
						that.writeBLETwo('05 5A 02 00 00 0D');
						// that.writeBLETwo('05 5A 06 00 00 0A 02 10 E8 03');
						// that.writeBLETwo('05 5A 06 00 00 0A B0 F2 E8 03');
					}, 300);
		
					console.log('NotifyBLECharacteristic: success');
				},
				fail(res) {
					console.log('NotifyBLECharacteristic: fail', res);
				}
			});
		},
		hex2ab(hexStr) {
			let strs = hexStr.split(' ');
			console.log('发送指令原文：', hexStr);
			console.log('发送指令长度：', strs.length);
			var typedArray = new Uint8Array(strs.join('').match(/[\da-f]{2}/gi).map(function(h) {
				return parseInt(h, 16)
			}))
			return typedArray.buffer;
		},
		onBLECharacteristicValueChange() {
			let that = this;

			that.isconnected = '已连接';
			that.isset = '设置';

			that.earname = that.bluetoothObj.name ? that.bluetoothObj.name : '未知设备';
			// that.maskShow = false;
			// that.raebox = true;
			that.maskclose();
			uni.setStorageSync('bluetoothObj', that.bluetoothObj);

			that.onBLEConnectionStateChange();
			uni.onBLECharacteristicValueChange((res) => {
				console.log(JSON.stringify(res));
				let returnVal = that.ab2hex(res.value);
				console.log('耳机返回的十六进制信息：', returnVal);
				
				if (that.newServiceId == '5052494D-2DAB-0341-6972-6F6861424C45') {
					if (returnVal.indexOf("055b") == 0) {
						if (that.shakeNum == 1) {
							that.shakeNum++;
							that.writeBLETwo('05 5A 06 00 00 0A 02 10 E8 03');
						} else if (that.shakeNum == 2) {
							that.shakeNum++;
							that.writeBLETwo('05 5A 06 00 00 0A B0 F2 E8 03');
						} else if (that.shakeNum == 3) {
							that.shakeNum++;
							that.writeBLEThree('AT+CA\r\n');
						    setTimeout(() => {
								that.writeBLEThree('AT+CC\r\n');
							}, 300);
						}
					} else if (that.shakeNum == 4) {
						let code = returnVal.split('0138');
						console.log('code:', code);
						if (!code[1]) { return; }
						that.switchReturnValue(that.hexCharCodeToStr(code[1]));
						that.analysis(that.str2ab(code[1]));
					}
					
				} else {
					let code = that.str2ab(that.ab2hex(res.value))
					that.switchReturnValue(that.hexCharCodeToStr(that.ab2hex(res.value)));
					that.analysis(code);
				}
				
				/* let code = that.str2ab(that.ab2hex(res.value))
				that.switchReturnValue(that.hexCharCodeToStr(that.ab2hex(res.value)));
				that.analysis(code); */
			});
		},
  
		onBLEConnectionStateChange() {
			let that = this;
			uni.onBLEConnectionStateChange(function (res) {
				// 该方法回调中可以用于处理连接意外断开等异常情况
				console.log(`device ${res.deviceId} state has changed, connected: ${res.connected}`);
				if (!res.connected) {
					uni.showToast({
						title: '蓝牙已断开',
						icon: 'none'
					});
					// that.maskShow = false;
					// that.raebox = true;
					// that.maskclose();
					that.connStatus = 1;
					that.earleft = '0';
					that.earright = '0';
					that.warehouselv = '0';
					that.earlv_img = '../../../static/img/ic_battery_20.png';
					that.earrv_img = '../../../static/img/ic_battery_20.png';
					that.warehouselv_img = '../../../static/img/ic_battery_20.png';
					that.isconnected = '未连接';
					that.isset = '点击添加';

					that.startSearch('重连');
				}
			});
		},

		// ----------------------ble蓝牙心跳重连
		startSearch(text) {
			let that = this;
			// 如果正在重连，则直接返回
			if (that.intervalId) {
				return;
			}
			
			that.intervalId = true;
			that.reconnectionNum = 0;
			
			function retryConn() {
				console.log("进入心跳重连：", ++that.reconnectionNum);
				uni.startBluetoothDevicesDiscovery({
					allowDuplicatesKey: true,
					success: (res) => {
						if (that.reconnectionNum > 5) {
							if (text == '连接') {
								uni.hideLoading();
							}
							that.stopSearch();
							uni.removeStorageSync('bluetoothObj');
						} else {
							uni.onBluetoothDeviceFound((res1) => {
								if (res1.devices[0].deviceId == that.bluetoothObj.deviceId && that.intervalId) {
									that.intervalId = false;
									console.log('res1.devices[0].deviceId', res1.devices[0].deviceId);
									console.log('that.bluetoothObj.deviceId', that.bluetoothObj.deviceId);
									console.log(res1);
									that.connect(that.bluetoothObj);
								}
							});
							setTimeout(() => {
								uni.stopBluetoothDevicesDiscovery({
									success(res1) {
										console.log('重连搜索停止成功', res1);
										console.log('当前重连次数：', that.reconnectionNum);
										if (that.intervalId && that.reconnectionNum < 5) {
											retryConn();
										} else {
											that.intervalId = false;
											that.reconnectionNum = 0;
										}
									},
									fail(err1) {
										that.intervalId = false;
										that.reconnectionNum = 0;
										console.log('重连搜索停止失败', err1);
									}
								});
								
							}, 3000);
						}
					},
					fail: (err) => {
						console.error('设备重连失败', err);
						that.stopSearch();
					}
				});
			}
			
			retryConn();
		},

		stopSearch() {
			let that = this;
			uni.stopBluetoothDevicesDiscovery({
				success: () => {
					console.log('stopBluetoothDevicesDiscovery success');
				},
				fail: (err) => {
					console.error('stopBluetoothDevicesDiscovery fail', err);
				},
				complete: () => {
					that.intervalId = false;
					that.reconnectionNum = 0;
				}
			});
		},

		// --------------------ble蓝牙心跳重连

		// ble蓝牙重新连接
		bleReconnection() {
			uni.getBluetoothAdapterState({
				success: (res) => {
					that.searchBle();
				},
				fail: (err) => {
					that.startBluetooth(() => {
						that.searchBle();
					});
				}
			});
		},

		// 重构蓝牙模块

		firstlink() {
			let that = this;
			uni.getBluetoothAdapterState({
				success: (res) => {
					// 蓝牙适配器可用且不处于搜索设备状态
					if (res.available && !res.discovering) {
						that.firstvalue = uni.getStorageSync('bluetoothObj');
						console.log(that.firstvalue.deviceId);
						
						if (that.firstvalue.deviceId != '' && that.firstvalue.deviceId) {
							uni.showLoading({
								title: '正在重连...',
							})
							
							that.maskShow=false;
							that.raebox = true; 
							that.earname = that.firstvalue.name;
							that.isconnected = '未连接';
							that.isset = '点击添加';
							// that.reconnectionDevicesId = that.firstvalue.deviceId;
							that.bluetoothObj = that.firstvalue;
							
							that.startSearch('连接');
						}	
					}
				},
				fail: (err) => {
					console.log('重连出现错误：', err);
				}
			});
		},
		//弹窗公告
		getPop() {
			console.log('弹窗公告');
			getPop().then((res) => {
				console.log(res);
				if (res.data.code == 0) {
					if (res.data.popupOpen == '0') {
						if (!this.$f.myCache('lf_pop_info')) {
							console.log('开');
							this.showNotice = true;
							this.popTitle = res.data.popTitle;
							this.popContent = res.data.popContent;
							let day = parseInt(res.data.popTime);
							this.$f.myCache('lf_pop_info', 'lf', day * 3600 * 24);
						}
					}
				}
			});
		},
		closePop() {
			this.showNotice = false;
		},
		/**
		 * 关闭遮罩
		 */
		//渲染对应列表
		applist(index) {
			getAppsByRemark({
					remark: index
				}).then((res) => {
					this.forlist(res.data.data);
				});
		},
		maskclose(name) {
			let that = this;
			
			uni.getBluetoothAdapterState({
			  success(res) {
			    console.log(res);
				if (res.available && res.discovering) {
					uni.stopBluetoothDevicesDiscovery({
						success: () => {
							console.log('stopBluetoothDevicesDiscovery success');
						},
						fail: (err) => {
							console.error('stopBluetoothDevicesDiscovery fail', err);
						},
						complete: () => {
							that.intervalId = false;
							that.reconnectionNum = 0;
							that.maskShow = false;
							that.imgshow = false;
							that.raebox = true;
							that.isClickSearch = false;
						}
					});
				} else {
					that.intervalId = false;
					that.reconnectionNum = 0;
					that.maskShow = false;
					that.imgshow = false;
					that.raebox = true;
					that.isClickSearch = false;
				}
			  },
			  fail(err) {
				  that.intervalId = false;
				  that.reconnectionNum = 0;
				  that.maskShow = false;
				  that.imgshow = false;
				  that.raebox = true;
				  that.isClickSearch = false;
				  console.log(err);
			  }
			});
		},
		tourl() {
			// this.stopSearch();
			if (this.isset == '点击添加') {
				let that = this;
				if (that.isClickSearch) {
					return;
				}
				that.isClickSearch = true;
				uni.getBluetoothAdapterState({
				  success(res) {
				    console.log('蓝牙适配器当前状态', res);
					// 蓝牙适配器可用
					if (res.available) {
						// 蓝牙适配器不处于重连状态
						if (!res.discovering && that.reconnectionNum == 0 && !that.intervalId) {
							that.searchBle();
						} else if (that.intervalId || that.reconnectionNum !== 0) {
							uni.showModal({
								title: '温馨提示',
								content: '后台正在重连，是否取消然后重新搜索设备？',
								success: function (res1) {
									if (res1.confirm) {
										uni.showLoading({
											title: '正在取消重连，请稍后...',
											mask: true
										});
										
										that.reconnectionNum = 5;
										
										uni.onBluetoothAdapterStateChange(function (res2) {
										  console.log('adapterState changed, now is', res2)
										  if (res2.available && !res2.discovering) {
											  if (that.reconnectionNum == 0 && !that.intervalId && that.isClickSearch) {
												  uni.hideLoading();
												  that.searchBle();
											  }
										  }
										});
									} else {
										that.isClickSearch = false;
									}
								}
							});
						} else {
							that.maskclose();
						}
					} else {
						that.isClickSearch = false;
					}
				  },
				  fail(err) {
					  console.log('getBluetoothAdapterState返回错误', err);
					  if (err.errCode == 10001) {
						  uni.showModal({
							  title: '温馨提示',
							  content: '请检查手机蓝牙是否开启',
							  showCancel: false,
							  complete() {
								  that.isClickSearch = false;
							  }
						  });
					  } else {
						  that.startBluetooth(() => {
						  	that.searchBle();
						  });
					  }
				  }
				});
			} else {
				// uni.navigateTo({
				// 	url:'/pages/webview/webview?src=http://47.116.77.23/tws/'
				// })
				console.log(this.bluetoothObj)
				const name = this.bluetoothObj.name
				if (name === 'HS-H4'){
					this.handleHref('AT+CB\r\n','/pages/webview/webview?appStartActivity=',{
								appStartActivity: '/hybrid/html/hs/index.html',
								appName: '微纳/清微厂商设备'
							})
					return;
				}
				if (name === 'GM Pro2') {
					uni.navigateTo({
						url:
							'/pages/webview/webview?appStartActivity=' +
							JSON.stringify({
						appStartActivity: 'https://mall-app.huashangjishu.com/app/earphone/hr/index.html?t=135',
						appName: '光芒系列厂商设备'
					})+ '&notBle=' + true
					});
			
					return;
				}

			// this.stringwr()
			}
		},
		handleHref(code, url, pageInfo){
			this.writeBLE(code);
			uni.showLoading({
				title: '正在获取设备状态...',
				mask: true
			});
			setTimeout(() => {
				uni.navigateTo({
					url:
						url +
						JSON.stringify(pageInfo)
				});
				uni.hideLoading();
			}, 1000);
		},
		//循环排序
		forlist(data) {
			console.log(data);
			data.sort((a, b) => parseInt(a.orderNo) - parseInt(b.orderNo));
			this.tabindexlist = data;
			console.log(data);
		},
		tablist(index) {
			console.log(index);
			//蓝牙设备下的列表
			if (index == 4) {
				getAppsByRemark({
						remark: 'app_ble'
					}).then((res) => {
						this.forlist(res.data.data);
					});
			} else {
				getAppsByRemarkAndHeadphoneType({
						remark: 'app_ble',
						deviceType: index
					}).then((res) => {
						this.forlist(res.data.data);
					});
			}
		},
		selcli(app, index) {
			this.selindex = index;
			this.applist(app);
			this.showList = false; // 隐藏下拉列表
		},
		showDropdown() {
			this.showList = !this.showList; // 切换下拉列表的显示状态
			this.border = !this.border; //控制下拉列表圆角
		},
		selectItem(item) {
			//选择蓝牙设备下的列表
			this.selectedItem = item.dictLabel; // 选择下拉列表项
			console.log(item);
			this.tablist(item.dictSort);
			this.showList = false; // 隐藏下拉列表
			this.border = true; //显示圆角
		},
		go(item) {
			console.log(item.appPlatform);
			if (item.appPlatform == 'H5') {
				// uni.navigateTo({
				// 	url: '/pages/webview/webview?src=' + item.appStartActivity
				// })
				
				item.appStartActivity =
					item.appStartActivity == 'http://47.116.77.23/ows/'
						? '/hybrid/html/ows/index.html'
						: item.appStartActivity == 'http://47.116.77.23/tws/'
						? '/hybrid/html/hs/index.html'
						: item.appStartActivity;
				let srcZero = item.appStartActivity.split('/');
				if (srcZero[1] == 'hybrid') {
					// for (let i = 0; i < this.queryInstructionsArr.length; i++) {
					// 	setTimeout(() => {
					// 		this.writeBLE(this.queryInstructionsArr[i]);
					// 	}, 2000)
					// }
					
					this.writeBLE("AT+CB\r\n")
					uni.showLoading({
						title: '正在获取设备状态...',
						mask: true
					});
					
					setTimeout(() => {
						uni.navigateTo({
							url: '/pages/webview/webview?appStartActivity=' + JSON.stringify(item)
						});
						console.log(item);
					}, 500);
					uni.hideLoading();
				}else {
					uni.navigateTo({
						url: '/pages/webview/webview?appStartActivity=' + JSON.stringify(item) + '&notBle=' + true
					});
				}
				// return
				
			} else {
				uni.getSystemInfo({
					success: function (res) {
						console.log(res.osName, '系统信息');
						console.log(res, '系统信息1');
						if (res.osName == 'android') {
							uni.showLoading({
								title: '下载中'
							});
							uni.downloadFile({
								url: item.appDownloadUrl, //仅为示例，并非真实的资源
								success: (res) => {
									console.log(res, '下载成功');
									if (res.statusCode === 200) {
										console.log('下载成功');
										uni.hideLoading();
										uni.showToast({
											title: '下载成功',
											icon: 'success'
										});
										uni.saveFile({
											tempFilePath: res.tempFilePath,
											success: function (res) {
												uni.openDocument({
													filePath: res.savedFilePath,
													success: function (res) {
														console.log(res, '打开安装包');
													}
												});
											},
											fail: (err) => {
												console.log(err, '打开安装包-失败');
											}
										});
									}
								},
								fail: (err) => {
									console.log(err, '下载失败');
									uni.hideLoading();
									uni.showToast({
										title: '下载失败,请检查网络',
										icon: 'none',
										duration: 1500
									});
								}
							});
						} else {
							uni.showToast({
								title: '当前手机系统不支持',
								icon: 'none',
								mask: true
							});
						}
					}
				});
				// uni.navigateTo({
				// 	url:'/pages/webview/webview?src='+item.appDownloadUrl
				// })
			}
			// // #ifdef APP-PLUS
			//     plus.runtime.openURL(video);
			//     // #endif
		},
		share() {
			miniConfig().then((res) => {
				let shareCover = res.data.result.intro;
				uni.share({
					provider: 'weixin',
					scene: 'WXSceneSession',
					type: 0,
					href: 'https://mall-app.huashangjishu.com/app/download/h5',
					title: '邀请你一起下载天空之城！',
					summary: '我正在使用华上在线，赶紧跟我一起来体验！',
					imageUrl: shareCover,
					success: function (res) {
						console.log('success:' + JSON.stringify(res));
					},
					fail: function (err) {
						console.log('fail:' + JSON.stringify(err));
					}
				});
			});
		},
		scanCode() {
			uni.scanCode({
				success: (res) => {
					console.log(res);
					if (res.scanType == 'QR_CODE' && res.result != '') {
						this.urltit = res.result;
						console.log(this.urltit);
						uni.navigateTo({
							url: '/pages/webview/webview?src=' + this.urltit
						});
					}
				},
				fail: (err) => {
					console.error('扫码失败：' + err);
				}
			});
			// uni.navigateTo({
			// 		url: '/pages/index/ceshi'
			// });
		},
		// 初始化蓝牙
		init(item) {
			let that = this;
			// that.openear = false
			// that.bluetoothObj = item
			// console.log(that.bluetoothObj)
			// uni.setStorageSync("bluetoothObj", item)
			uni.getBluetoothAdapterState({
				success: (res) => {
					// that.openear = true
					that.searchBle();
				},
				fail: (err) => {
					that.startBluetooth(() => {
						// that.openear = true;
						that.searchBle();
					});
				}
			});
		},
		//获取蓝牙设备信息
		getBluetoothDevices() {
			let that = this;
			// 	that.bluetoothList = [];
			//  uni.startBluetoothDevicesDiscovery({
			// 		success(res) {
			// 			//蓝牙设备监听 uni.onBluetoothDeviceFound
			// 			uni.onBluetoothDeviceFound((result) => {
			// 				let arr = that.bluetoothList;
			// 				let devices = [];
			// 				let list = result.devices;
			// 				for (let i = 0; i < list.length; ++i) {
			// 					if (list[i].name && list[i].name != "未知设备") {
			// 						let arrNew = arr.filter((item) => {
			// 							return item.deviceId == list[i].deviceId;
			// 						});
			// 						// console.log('arrNew:',arrNew.length)
			// 						if (arrNew.length == 0) {
			// 							devices.push(list[i]);
			// 						}
			// 					}
			// 				}
			// 				that.bluetoothList = arr.concat(devices);
			// 				console.log("bluetoothList", that.bluetoothList)
			// 			});
			// 			that.time = setTimeout(() => {
			// 				// uni.getBluetoothDevices
			// uni.getBluetoothDevices({
			// 	success(res2) {
			// 		let devices = [];
			// 		let list = res2.devices;
			// 		for (let i = 0; i < list.length; ++i) {
			// 			if (list[i].name && list[i].name != "未知设备") {
			// 				devices.push(list[i]);
			// 			}
			// 		}
			// 		that.bluetoothList = devices;
			// 	},
			// })
			// 				clearTimeout(that.time);
			// 			}, 3000);
			// 		}
			// 	});
		},

		// 绑定蓝牙
		// onBind(item) {
		// 	// uni.stopBluetoothDevicesDiscovery();
		// 	let that = this;
		// 	let { address, name } = item;
		// 	uni.showLoading({
		// 		title: '连接中'
		// 	});
		// 	console.log('item', item);
		// 	that.bluetoothObj.address = address;
		// 	that.bluetoothObj.deviceId = address;
		// 	that.bluetoothObj.name = name;
		// 	uni.setStorageSync('bluetoothObj', item);
		// 	console.log(uni.getStorageInfoSync('bluetoothObj'));
		// 	that.serviceId = 0;
		// 	this.earname = name;
		// 	// that.writeCharacter = false;
		// 	// that.readCharacter = false;
		// 	// that.notifyCharacter = false;
		// 	// that.connect();

		// 	// if(!that.intervalTime) {
		// 	// 	that.intervalTime = setInterval(() => {
		// 	// 		if(that.newServiceId == '') {
		// 	// that.getBluetoothAdapterState();
		// 	// 		}
		// 	// 	}, 2000)
		// 	// }

		// 	console.log(address);
		// 	bt.connectBT(
		// 		{
		// 			btAddress: address
		// 		},
		// 		(result) => {
		// 			//result数据：{"code":100,"msg":"连接成功"}，并接收数据
		// 			const msg = JSON.stringify(result);
		// 			console.log(result);
		// 			if (result.code == 200 && result.value) {
		// 				// console.log("成功判断");
		// 				that.isconnected = '已连接';
		// 				that.isset = '设置';
		// 				that.analysis(result.value);
		// 				this.earname = name;
		// 				that.maskShow = false;
		// 				that.raebox = true;
		// 				// that.earconnectivity=true

		// 				that.switchReturnValue(result.value);
		// 				uni.setStorageSync('bluetoothObj', item);
		// 				console.log(that.newServiceId, that.connectNum);

		// 				// uni.hideLoading()
		// 			} else if (result.code == 200 && result.msg == '与设备已连接') {
		// 				bt.breakBT((result) => {});
		// 				// that.connect();
		// 				that.firstvalue = uni.getStorageSync('bluetoothObj');
		// 				that.onBind(that.firstvalue);
		// 				// uni.hideLoading()
		// 			} else if (result.code == -300 && result.msg == '与设备连接断开') {
		// 				console.log('fasle');
		// 				this.maskShow = false;
		// 				this.raebox = true;
		// 				this.earleft = '0';
		// 				this.earright = '0';
		// 				this.warehouselv = '0';
		// 				this.earlv_img = '../../static/images/mipmap/ic_battery_20.png';
		// 				this.earrv_img = '../../static/images/mipmap/ic_battery_20.png';
		// 				this.warehouselv_img = '../../static/images/mipmap/ic_battery_20.png';
		// 				this.isconnected = '未连接';
		// 				this.isset = '添加';
		// 				console.log(this.bluetoothObj);
		// 				that.newServiceId = '';
		// 				that.connectNum = 0;
		// 				// uni.closeBluetoothAdapter()
		// 				console.log('/****************************************************************************/');
		// 				// clearInterval(that.intervalTime)
		// 				that.intervalTime = null;
		// 				// uni.hideLoading()
		// 			} else if (result.code == 200 && result.msg == '已经连接，请勿重复连接') {
		// 				bt.breakBT((result) => {});
		// 				// uni.closeBluetoothAdapter()
		// 				that.firstvalue = uni.getStorageSync('bluetoothObj');
		// 				that.onBind(that.firstvalue);
		// 				// that.connect();
		// 			}
		// 			uni.hideLoading();

		// 			// else if(result.code==-200){
		// 			// bt.breakBT(result => {this.firstvalue = uni.getStorageSync('bluetoothObj');
		// 			// this.onBind(this.firstvalue)});

		// 			// }

		// 			// modal.toast({
		// 			//     message: msg,
		// 			//     duration: 1.5
		// 			// });
		// 		}
		// 	);

			// this.name = item.name
			// uni.setStorageSync('name', item)
			// this.imgshow = fasle
		// },

		switchReturnValue(str) {
			let that = this;
			// str = String(str)
			// console.log(str)
			// return
			console.log('switchReturnValue:', str);
			if(str.includes('CA=')) {
				this.earname = str.split('=')[1];
			}
			if (str.includes('CA=') && str.includes('CB=') && str.includes('CD=')) {
				uni.removeStorageSync('stateObj');
				// 代表是返回耳机状态
				// let JsonStr = JSON.stringify(str)
				let lines = str.trim().split('\r\n');
				let dataArray = lines.map((line) => {
					// 使用正则表达式匹配键值对
					let match = line.match(/^([^=]+)=(.*)$/);
					if (match) {
						let key = match[1].trim();
						let value = match[2].trim();
						
						// 如果值中包含逗号，则分割成数组
						if (value.includes(',')) {
							value = value.split(',').map((item) => item.trim());
						}

						// 返回格式化后的对象
						return {
							[key]: value
						};
					}
				});

				dataArray = dataArray.filter((item) => item);
				// that.returnValue(dataArray)
				console.log('dataArray', dataArray)
				uni.setStorageSync('stateObj', dataArray);
			}
			
			/* let lines = str.trim().split('\r\n');
			let dataArray = lines.map((line) => {
				// 使用正则表达式匹配键值对
				let match = line.match(/^([^=]+)=(.*)$/);
				if (match) {
					let key = match[1].trim();
					let value = match[2].trim();
					
					// 如果值中包含逗号，则分割成数组
					if (value.includes(',')) {
						value = value.split(',').map((item) => item.trim());
					}
			
					// 返回格式化后的对象
					return {
						[key]: value
					};
				}
			});
			
			dataArray = dataArray.filter((item) => item);
			
			let arr1 = uni.getStorageSync('stateObj');
			let arr2 = that.mergeArrays(arr1 ? arr1 : [], dataArray);
			
			console.log('arr1', JSON.stringify(arr1));
			console.log('arr2', JSON.stringify(arr2));
			
			uni.setStorageSync('stateObj', arr2); */
		},
		mergeArrays(arr1, arr2) {
			console.log('mergeArrays arr1', JSON.stringify(arr1));
			console.log('mergeArrays arr2', JSON.stringify(arr2));
		    var merged = [];
		    var propMap = {};
		
		    // 遍历第一个数组，将属性添加到merged和propMap中
		    for (var i = 0; i < arr1.length; i++) {
		        var prop = arr1[i];
		        merged.push(prop);
		        propMap[JSON.stringify(prop)] = prop;
		    }
		
		    // 遍历第二个数组，检查属性是否已存在于propMap中
		    for (var j = 0; j < arr2.length; j++) {
		        var prop = arr2[j];
		        if (propMap.hasOwnProperty(JSON.stringify(prop))) {
		            // 如果属性已存在，则覆盖
		            var index = merged.indexOf(propMap[JSON.stringify(prop)]);
		            merged[index] = prop;
		        } else {
		            // 如果属性不存在，则添加
		            merged.push(prop);
		        }
		    }
		
		    return merged;
		},

		//获取蓝牙设备所有服务(service)。
		getSeviceId() {
			let that = this;
			let t = setTimeout(() => {
				uni.getBLEDeviceServices({
					deviceId: that.bluetoothObj.address,
					success(res) {
						console.log('getBLEDeviceServices success', res);
						that.services = res.services;
						that.getCharacteristics();
					},
					fail: function (e) {}
				});
				clearTimeout(t);
			}, 1500);
		},
		bt(deviceId) {
			let that = this;
			that.getBluetoothDevices();
			// bt.breakBT(result1 => {
			//     //result数据：{"code":100,"msg":"连接成功"}
			//     console.log("断开连接",result1);
			// 	    bt.removeBond({
			// 	        "btAddress": deviceId
			// 	    }, result2 => {
			// 	        //result数据：{"code":100,"msg":"配对完成，请刷新列表"}
			// 	        console.log("取消配对",result2);
			// 			if(result2.msg=="与设备连接断开"){
			// 				that.earconnectivity=false
			// 			}

			// bt.pairBT({
			//     "btAddress": deviceId//  88:10:8F:C9:33:C5
			// }, result3 => {
			//     //result数据：{"code":100,"msg":"配对完成，请刷新列表"}
			//     console.log("蓝牙配对",result3);

			// 	if(result3.msg=="配对成功"||result3.msg=="与设备已连接"){
			// 			uni.stopBluetoothDevicesDiscovery()
			// 			console.log(deviceId);
			// 			uni.createBLEConnection({
			// 				deviceId,
			// 				success(res) {
			// 					console.log('createBLEConnection success', res)
			// 					uni.hideLoading()
			// 					that.getSeviceId()
			// 				},
			// 				fail(e) {
			// 					console.log('createBLEConnection fail', e)
			// 					// uni.hideLoading()
			// 				}
			// 			})
			// 	}else if(result3.msg=="与设备连接断开"){
			// 		console.log("result3.msg");
			// 		that.earconnectivity=false
			// 		uni.closeBLEConnection({
			// 		  deviceId: that.bluetoothObj.deviceId,
			// 		  success(res) {
			// 		    console.log(res)
			// 		  }
			// 		})
			// 		uni.hideLoading()
			// 		console.log(that.earconnectivity);
			// 	}else{
			// 		uni.hideLoading()
			// 		console.log("连接失败，请再次点击连接");
			// 	}
			// });
			// 	    });
			// });
		},
		//获取对应service服务下的特征
		getCharacteristics() {
			var that = this;
			let { services: list, serviceId: num, writeCharacter: write, readCharacter: read, notifyCharacter: notify } = that;
			// uni.getBLEDeviceCharacteristics
			console.log(that.bluetoothObj.deviceId);
			console.log(list[num].uuid);
			uni.getBLEDeviceCharacteristics({
				deviceId: that.bluetoothObj.address,
				// deviceId:"00001101-0000-1000-8000-00805f9b34fb",
				serviceId: list[num].uuid,
				// serviceId: "0177F5DA-0000-1000-8000-00805F9B34FB",
				// serviceId:"0000ff00-0000-1000-8000-00805f9b34fb",

				success(res) {
					console.log('getBLEDeviceCharacteristics success', res);
					// 过滤出满足条件的characteristics
					const filteredCharacteristics = res.characteristics.filter((characteristic) => {
						return characteristic.properties.read && characteristic.properties.write && characteristic.properties.notify;
					});

					// 提取出满足条件的characteristics的uuid
					const uuids = filteredCharacteristics.map((characteristic) => characteristic.uuid);
					console.log(uuids);
					// if (!read) {
					// 	if (properties.read) {
					// that.bluetoothObj.readServiceId = ;
					// 		read = true
					// 	}
					// }
					// }
					if (uuids.length == 0) {
						console.log('12313212132');
						num++;
						that.writeCharacter = write;
						that.readCharacter = read;
						that.notifyCharacter = notify;
						that.serviceId = num;
						if (num == list.length) {
							uni.showModal({
								title: '温馨提示',
								content: '找不到该读写的特征值'
							});
						} else {
							that.getCharacteristics();
						}
					} else {
						// ok
						// ok
						// wx.writeBLECharacteristicValue
						console.log(that.bluetoothObj);
						that.bluetoothObj.serviceId = list[num].uuid;
						that.bluetoothObj.notifyCharaterId = uuids[0];
						console.log(that.bluetoothObj.deviceId);
						console.log(that.bluetoothObj.serviceId);
						console.log(that.bluetoothObj.notifyCharaterId);
						uni.notifyBLECharacteristicValueChange({
							state: true, // 启用 notify 功能
							// 这里的 deviceId 需要已经通过 createBLEConnection 与对应设备建立链接
							deviceId: that.bluetoothObj.deviceId,
							// 这里的 serviceId 需要在 getBLEDeviceServices 接口中获取
							serviceId: that.bluetoothObj.serviceId,
							// serviceId: "0177F5DA-0000-1000-8000-00805F9B34FB",
							// serviceId:"0000ff00-0000-1000-8000-00805f9b34fb",
							// 这里的 characteristicId 需要在 getBLEDeviceCharacteristics 接口中获取
							characteristicId: that.bluetoothObj.notifyCharaterId,
							// characteristicId: "00008888-0000-1000-8000-00805F9B34FB",
							// characteristicId:"0000ff01-0000-1000-8000-00805f9b34fb",
							success(res) {
								console.log('notifyBLECharacteristicValueChange success', res);
								setTimeout(() => {
									that.stringwr('AT+CC');
								}, 1000);
								uni.onBLECharacteristicValueChange(function (e) {
									/**对设备发送过来的参数进行解密 */
									console.log(e);
									let str = that.ab2hex(e.value);
									that.val = that.str2ab(str);
									console.log(that.val);
									that.analysis(that.val);
									console.log('解密str', str);
								});
							},
							fail: function (e) {
								console.log('getBLEDeviceCharacteristics fail：', e);
							}
						});
						console.log('that.bluetoothObj', that.bluetoothObj);
						that.maskShow = false;
						that.raebox = true;
						that.earconnectivity = true;
						that.isconnected = '已连接';
						that.isset = '设置';
						uni.setStorageSync('bluetoothObj', that.bluetoothObj);
						uni.showToast({
							icon: 'none',
							title: '绑定成功'
						});
						//跳转
						// setTimeout(()=>{
						// 	// uni.navigateBack({
						// 	// 	delta:1
						// 	// })
						// 	that.stringwr()
						// },1000)
					}
				},
				fail: function (e) {
					console.log('getBLEDeviceCharacteristics fail：', e);
				}
			});
		},
		
		// 16进制转ascii码
		hexCharCodeToStr(hex) {
			if (hex.length % 2 !== 0) {
				// 如果是奇数长度，前面补零
				hex = '0' + hex;
			}
			var ascii = '';
			for (var i = 0; i < hex.length; i += 2) {
				// 将每两个16进制字符转为10进制，然后转为ASCII字符
				ascii += String.fromCharCode(parseInt(hex.substr(i, 2), 16));
			}
			// console.log('ascii', ascii)
			return ascii;
		},
		    buf2str (buffer) {
		      let encodedString = String.fromCodePoint.apply(
		        null,
		        new Uint8Array(buffer)
		      );
		      let decodedString = decodeURIComponent(escape(encodedString));
		      //没有这一步中文会乱码
		      return decodedString;
		    },

		
		//写数据
		stringwr(value) {
			//发送给蓝牙设备的数据
			let buffer = this.generate_command(value);
			console.log(this.ab2hex(buffer));
			console.log(this.bluetoothObj.deviceId);
			console.log(this.bluetoothObj.serviceId);
			console.log(this.bluetoothObj.notifyCharaterId);
			uni.writeBLECharacteristicValue({
				// deviceId: this.deviceId,  // 在第 3 步可以拿到蓝牙的 deviceId 的值
				deviceId: this.bluetoothObj.deviceId,
				serviceId: this.bluetoothObj.serviceId, // 在第 5 步可以拿到蓝牙的 serviceId 的值
				// serviceId: "0177F5DA-0000-1000-8000-00805F9B34FB",
				// serviceId:"0000ff00-0000-1000-8000-00805f9b34fb",
				characteristicId: this.bluetoothObj.notifyCharaterId, // 在第 6 步可以拿到蓝牙的 characteristic 的值，这里使用的是 “写characteristicId”
				// characteristicId: "00008888-0000-1000-8000-00805F9B34FB",
				// characteristicId:"0000ff01-0000-1000-8000-00805f9b34fb",
				value: buffer, // buffer 的格式为 ArrayBuffer
				success(res) {
					console.log('指令下发成功', res);
					setTimeout(() => {}, 1000);
				},
				fail(err) {
					console.log('指令发送失败', err);
				}
			});
		},
		/**
		 * 生成 ArrayBuffer 格式的数据，可以直接用于给蓝牙设备发送数据
		 * @param {Object} startCode 起始码
		 * @param {Object} dataType 数据类型
		 * @param {Object} sentData 发送给蓝牙的数据数组，例如：[0x12,0x01]
		 */
		generate_command(sentData) {
			const buffer = new ArrayBuffer(sentData.length);
			const u8Array = new Uint8Array(buffer);
			// u8Array[0] = 0x41;
			// u8Array[1] = 0x54;
			for (let i = 0; i < sentData.length; i++) {
				let hex = sentData.charCodeAt(i).toString(16); // 获取字符的ASCII码，并转换为十六进制字符串
				u8Array[i] = '0x' + hex; // 添加 '0x' 前缀表示十六进制数
			}
			const hexArr = Array.prototype.map.call(new Uint8Array(buffer), function (bit) {
				return ('00' + bit.toString(16)).slice(-2);
			});
			console.log(hexArr.join(''));
			return buffer;
		},
		ab2hex(buffer) {
			const hexArr = Array.prototype.map.call(new Uint8Array(buffer), function (bit) {
				return ('00' + bit.toString(16)).slice(-2);
			});
			return hexArr.join('');
		},

		//十六进制转字符串
		str2ab(hex) {
			var str = '';
			for (var i = 0; i < hex.length; i += 2) {
				str += String.fromCharCode(parseInt(hex.substr(i, 2), 16));
			}
			str = str.slice(0, -2);
			return str;
		},
		analysis(data) {
			// console.log(data);
			this.isconnected = '已连接';
			this.isset = '设置';
			
			console.log('data:', data);
			// document.getElementById("state").textContent = "已连接";
			var arry = data.split('=');
			switch (arry[0].replace(/\r\n/g, '')) {
				case 'CA':
					this.earname = arry[1];
					uni.setStorageSync('name', this.earname);
					break;
				case 'CC':
					var v = arry[1].split(',');
					uni.setStorageSync('CC_L_V', v[0]);
					uni.setStorageSync('CC_R_V', v[1]);
					uni.setStorageSync('CC_C_V', v[2]);
					this.earvshow(v[0], v[1], v[2]);
					break;
			}
			// uni.hideLoading()
		},

		earvshow(bt_lv, bt_rv, bt_washhousev) {
			// this.stringwr("AT+CA")
			if (bt_lv >= 128) {
				this.earleft = bt_lv - 128;
				this.earlv_img = this.getVimg(bt_lv);
			} else {
				this.earleft = parseInt(bt_lv);
				this.earlv_img = this.getVimg(bt_lv);
			}
			if (bt_rv >= 128) {
				this.earright = bt_rv - 128;
				this.earrv_img = this.getVimg(bt_rv);
			} else {
				this.earright = parseInt(bt_rv);
				this.earrv_img = this.getVimg(bt_rv);
			}
			if (bt_washhousev >= 128) {
				this.warehouselv = bt_washhousev - 128;
				this.warehouselv_img = this.getVimg(bt_washhousev);
			} else {
				this.warehouselv = parseInt(bt_washhousev);
				this.warehouselv_img = this.getVimg(bt_washhousev);
			}
		},
		getVimg(number) {
			// console.log("选择图片");
			if (number >= 128) {
				number -= 128;
				if (number >= 100) {
					return '../../../static/img/ic_battery_charging_100.png';
				} else if (number >= 90) {
					return '../../../static/img/ic_battery_charging_90.png';
				} else if (number >= 80) {
					return '../../../static/img/ic_battery_charging_80.png';
				} else if (number >= 60) {
					return '../../../static/img/ic_battery_charging_60.png';
				} else if (number >= 50) {
					return '../../../static/img/ic_battery_charging_50.png';
				} else if (number >= 21) {
					return '../../../static/img/ic_battery_charging_30.png';
				} else {
					return '../../../static/img/ic_battery_charging_20.png';
				}
			} else {
				if (number >= 100) {
					return '../../../static/img/ic_battery_100.png';
				} else if (number >= 90) {
					return '../../../static/img/ic_battery_90.png';
				} else if (number >= 80) {
					return '../../../static/img/ic_battery_80.png';
				} else if (number >= 60) {
					return '../../../static/img/ic_battery_60.png';
				} else if (number >= 50) {
					return '../../../static/img/ic_battery_50.png';
				} else if (number >= 21) {
					return '../../../static/img/ic_battery_30.png';
				} else {
					return '../../../static/img/ic_battery_20.png';
				}
			}
		},
		hrefTo() {
			uni.navigateTo({url:"/pages/tabbar/device/func"})
		},
	}
};
</script>
<style>
page {
	background-color: #f5f5f5;
}
</style>
<style lang="scss" scoped>
.content {
	width: 100%;

	.uni-mask {
		position: fixed;
		z-index: 100;
		top: 0;
		left: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		width: 100%;
		background: rgba(0, 0, 0, 0.6);
		padding: 10rpx 30rpx;
		box-sizing: border-box;
		.icon {
			position: absolute;
			top: 20rpx;
			right: 20rpx;
		}

		.uni-title {
			text-align: center;
			font-size: 36rpx;
			line-height: 36rpx;
			padding: 16rpx auto;
		}
	}

	.uni-scroll_box {
		height: 50%;
		background: #fff;
		border-radius: 20rpx;
	}

	.uni-list-box {
		margin: 0 20rpx;
		padding: 15rpx 0;
		border-bottom: 1px #f5f5f5 solid;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		align-content: flex-start;

		// font-size: 50rpx;
		image {
			width: 80rpx;
			height: 80rpx;
			margin-right: 10rpx;
		}
	}

	.uni-list:last-child {
		border: none;
	}

	.uni-list_name {
		font-size: 30rpx;
		color: #333;
	}

	.top {
		width: 100%;
		padding-top: 120rpx;

		.dropdown-list {
			width: 200rpx;
			padding: 20rpx;
			position: absolute;
			background-color: #d4ced6;
			opacity: 0.9;
			z-index: 100;

			// filter: alpha(opacity=10); //设置filter属性
			.dropdown-item {
				margin-top: 20rpx;
				text-align: center;
			}
		}

		.borderradio {
			border-radius: 0rpx 0rpx 20rpx 20rpx;
		}

		.share {
			width: 100%;
			padding: 0rpx 20rpx;
			height: 100rpx;
			display: flex;
			justify-content: flex-end;
			align-items: center;
		}

		.raebox {
			// width: 650rpx;
			height: 360rpx;
			display: flex;
			margin: 0 32rpx;
			border: 1rpx solid #bbbbbb;
			border-radius: 20rpx;
			flex-direction: row;
			align-items: center;
			margin-bottom: 32rpx;

			.raebox_left {
				width: 300rpx;
				height: 240rpx;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;

				image {
					width: 300rpx;
				}
			}

			.raebox_right {
				width: 350rpx;
				height: 360rpx;
				display: flex;
				align-items: center;
				justify-content: space-evenly;
				flex-direction: column;
				
				.name {
					width: 300rpx;
					font-size: 40rpx;
					font-weight: bold;
					text-align: center;
					white-space: nowrap;
					/* 确保文本在一行内显示 */
					overflow: hidden;
					/* 超出容器部分隐藏 */
					text-overflow: ellipsis;
					/* 超出部分显示三个点 */
				}

				.container {
					width: 350rpx;
					display: flex;
					justify-content: center;

					// justify-content: space-between;
					.container_1 {
						display: flex;
						// margin-left: 10rpx;
						flex-direction: column;
						align-items: center;
					}

					.container_2 {
						display: flex;
						flex-direction: row;
						justify-content: flex-start;
						align-items: center;
					}
				}
				
				.conn-status {
					width: 300rpx;
				}

				.ear {
					padding: 10rpx 30rpx;
					width: 100%;
					text-align: center;
				}

				.set {
					padding: 10rpx 50rpx;
					background-color: #4095e5;
					color: #fff;
					border-radius: 10rpx;
				}
			}
		}

		.devicebox {
			width: 100%;
			height: 300rpx;
			display: flex;
			flex-direction: column;
			align-items: center;

			// background-color: antiquewhite;
			.deviceimg {
				display: flex;

				image {
					width: 150rpx;
					height: 150rpx;
					// transform: rotate(-30deg);
				}
			}

			.devicetitle {
				margin-top: 20rpx;
				font-size: 40rpx;
			}
		}

		.select {
			width: 700rpx;
			height: 60rpx;
			display: flex;
			font-size: 30rpx;
			font-weight: 600;
			letter-spacing: 5rpx;
			flex-direction: row;
			justify-content: space-evenly;
			align-items: center;
			margin: 0 auto;
			color: #2b8ffc;

			.sel {
				padding: 5rpx 40rpx;
			}

			.selbox {
				background-color: #2b8ffc;
				color: #fff;
				border-radius: 10rpx;
			}

			// background-color: aqua;
		}

		.whole {
			width: 300rpx;
			padding: 20rpx;
			font-size: 40rpx;
			font-weight: 600;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: flex-start;
			position: relative;

			image {
				width: 40rpx;
				height: 40rpx;
			}
		}

		.boslist {
			width: 700rpx;
			margin: 0 auto;
			// margin-top: 20rpx;
			display: flex;
			flex-direction: row;
			flex-wrap: wrap;
			align-items: center;
			justify-content: flex-start;

			.box {
				width: 280rpx;
				height: 250rpx;
				padding: 20rpx;
				display: flex;
				flex-direction: column;
				justify-content: space-around;
				align-items: center;
				margin-top: 30rpx;
				margin-left: 50rpx;
				border: 1rpx solid #c0c0c0;
				border-radius: 20rpx;

				image {
					width: 150rpx;
					height: 150rpx;
				}

				.title {
					width: 240rpx;
					font-size: 30rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					text-align: center;
				}
			}
		}
	}
}
</style>
