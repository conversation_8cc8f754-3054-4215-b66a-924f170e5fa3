/**
 * AI聊天服务
 * 用于处理与小叶同学(星火认知大模型Spark Lite)的对话交互
 */
import sparkAi from './sparkAi.js';

/**
 * AI聊天服务类
 */
class AiChatService {
  constructor() {
    // 初始化状态
    this.initialized = false;
    this.connecting = false;
    this.messageQueue = [];
    this.callbacks = {
      onMessageStart: null,
      onMessageStream: null,
      onMessageComplete: null,
      onError: null,
    };
  }
  
  /**
   * 设置回调函数
   * @param {Object} callbacks 回调函数集合
   */
  setCallbacks(callbacks = {}) {
    // 更新本地回调
    this.callbacks = { ...this.callbacks, ...callbacks };
    
    // 立即设置到sparkAi
    if (sparkAi) {
      sparkAi.setCallbacks({
        onStream: (content, isComplete) => {
          if (this.callbacks.onMessageStream) {
            this.callbacks.onMessageStream(content, isComplete);
          }
          
          if (isComplete && this.callbacks.onMessageComplete) {
            this.callbacks.onMessageComplete(content);
          }
        },
        onError: (error) => {
          console.error('星火AI错误:', error);
          if (this.callbacks.onError) {
            this.callbacks.onError(error);
          }
        },
        onClose: () => {
          console.log('星火AI连接已关闭');
          this.initialized = false;
        },
        onComplete: (content) => {
          // 由onStream处理
        }
      });
    }
  }

  /**
   * 初始化AI聊天服务
   * @param {Object} callbacks 回调函数
   * @returns {Promise} 初始化结果
   */
  init(callbacks = {}) {
    return new Promise((resolve, reject) => {
      if (this.initialized || this.connecting) {
        resolve(true);
        return;
      }
      
      this.connecting = true;
      
      // 设置回调
      this.setCallbacks(callbacks);
      
      
      // 建立连接
      sparkAi.connect()
        .then(() => {
          console.log('星火AI连接建立成功');
          this.initialized = true;
          this.connecting = false;
          
          // 如果有排队中的消息，处理它们
          this.processMessageQueue();
          
          resolve(true);
        })
        .catch((error) => {
          console.error('星火AI连接建立失败:', error);
          this.connecting = false;
          if (this.callbacks.onError) {
            this.callbacks.onError(error);
          }
          reject(error);
        });
    });
  }
  
  /**
   * 发送消息给小叶同学
   * @param {string} content 消息内容
   * @returns {Promise} 发送结果
   */
  sendMessage(content) {
    return new Promise((resolve, reject) => {
      // 如果正在初始化连接，将消息加入队列
      if (this.connecting) {
        this.messageQueue.push({
          content,
          resolve,
          reject
        });
        return;
      }
      
      // 如果未初始化，先初始化
      if (!this.initialized) {
        this.messageQueue.push({
          content,
          resolve,
          reject
        });
        
        this.init().catch((error) => {
          // 初始化失败，清空队列
          this.messageQueue.forEach(item => item.reject(error));
          this.messageQueue = [];
        });
        return;
      }
      
      // 通知开始处理消息
      if (this.callbacks.onMessageStart) {
        this.callbacks.onMessageStart();
      }
      
      // 发送消息
      sparkAi.sendMessage(content)
        .then(() => resolve(true))
        .catch((error) => {
          console.error('发送消息失败:', error);
          if (this.callbacks.onError) {
            this.callbacks.onError(error);
          }
          reject(error);
        });
    });
  }
  
  /**
   * 处理消息队列
   */
  processMessageQueue() {
    if (this.messageQueue.length === 0) {
      return;
    }
    
    // 获取并移除队列中的第一条消息
    const { content, resolve, reject } = this.messageQueue.shift();
    
    // 发送消息
    this.sendMessage(content)
      .then(() => {
        resolve(true);
        // 处理下一条消息
        this.processMessageQueue();
      })
      .catch((error) => {
        reject(error);
        // 处理下一条消息
        this.processMessageQueue();
      });
  }
  
  /**
   * 清空消息历史
   */
  clearHistory() {
    if (sparkAi) {
      sparkAi.clearHistory();
    }
  }
  
  /**
   * 关闭连接
   */
  closeConnection() {
    if (sparkAi) {
      sparkAi.closeConnection();
      this.initialized = false;
    }
  }
}

// 导出AI聊天服务实例
const aiChatService = new AiChatService();
export default aiChatService;
