<!DOCTYPE html>
<html>

	<head>
		<meta charset="utf-8">
		<title>Hello MUI</title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">

		<!--标准mui.css-->
		<link rel="stylesheet" href="../css/mui.min.css">
		<!--App自定义的css-->
		<!--<link rel="stylesheet" type="text/css" href="../css/app.css"/>-->
		<style>
			.mui-row.mui-fullscreen>[class*="mui-col-"] {
				height: 100%;
			}

			.title {
				margin: 20px 15px 10px;
				color: #6d6d72;
				font-size: 15px;
			}

			.oa-contact-cell.mui-table .mui-table-cell {
				padding: 11px 0;
				vertical-align: middle;
			}

			.oa-contact-cell {
				position: relative;
				margin: -11px 0;
			}

			.mui-checkbox {

				width: 20px;
				height: 20px;
				margin: 10px;
			}

			#head {
				line-height: 20px;
			}

			.head-img {
				width: 32px;
				height: 32px;
				margin: 10px;
			}

			#head-img1 {
				position: absolute;
				bottom: 10px;
				right: 40px;
				width: 40px;
				height: 40px;
			}

			.oa-contact-avatar {
				width: 75px;
			}

			.oa-contact-avatar img {
				border-radius: 50%;
			}

			.oa-contact-content {
				width: 100%;
			}

			.oa-contact-name {
				margin-right: 20px;
			}

			.oa-contact-name,
			oa-contact-position {
				float: left;
			}

			.mui-backdrop {
				position: fixed;
				top: 0;
				right: 0;
				bottom: 0;
				left: 0;
				z-index: 998;
				background-color: rgba(0, 0, 0, .3);
			}

			.container {

				display: flex;
				/* 将容器设置为弹性盒子 */
				justify-content: center;
				/* 将弹性盒子内的内容水平居中 */
				color: white;

			}

			.switchtext {

				display: flex;
				/* 将容器设置为弹性盒子 */
				justify-content: center;
				/* 将弹性盒子内的内容水平居中 */
				color: white;
				margin: 10px;
				background-color: #6d6d72;
				border-radius: 20px;

			}
		</style>

	</head>
	<body style=" background-color: black;">
		<header class="mui-bar mui-bar-nav" style=" background-color: black;">
			<a class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></a>
			<h1 class="mui-title " style="color: white;">姿态控制</h1>
		</header>

		<div class="mui-content mui-content-padded" style=" background-color: black;">
			<!-- 开关打开状态，多了一个.mui-active类 -->
			<div id="attitude" class="switchtext">
				通话控制开关
				<div class="mui-switch mui-switch-mini  mui-switch-blue">
					<div class="mui-switch-handle"></div>
				</div>
			</div>

			<div class="nav">
				<ul class="mui-table-view mui-grid-view" style=" background-color: black; color: white;">
					<div class="container" style=" background-color: black; color: white;">
						<h4>点头两次</h4>
						<img src="../image/right_double_arrow.png" />
						<img src="../image/answer_call.png" />
						<h4>接听电话</h4>
					</div>
					<div class="container" style=" background-color: black; color: white;">
						<h4>摇头两次</h4>
						<img src="../image/right_double_arrow.png" />
						<img src="../image/end_call.png" />
						<h4>拒接电话</h4>
					</div>
				</ul>
			</div>
			<div style="
				border: 2px  dashed white;
				border-radius:20px;">
				<div class=" container">
					<img src="../image/nod_head.png" />
					<img src="../image/shake_head.png" />
				</div>

			</div>
			<!-- 开关打开状态，多了一个.mui-active类 -->
			<div id="attitude1" class="switchtext">
				音乐控制开关
				<div class="mui-switch mui-switch-mini  mui-switch-blue">
					<div class="mui-switch-handle"></div>
				</div>
			</div>
			<ul class="mui-table-view mui-grid-view" style=" background-color: black; color: white;">
				<div class="container" style=" background-color: black; color: white;">
					<h4>向左转头然后回正</h4>
					<img src="../image/right_double_arrow.png" />
					<img src="../image/previous_song.png" />
					<h4>上一曲</h4>
				</div>
				<div class="container" style=" background-color: black; color: white;">
					<h4>向左转头然后回正</h4>
					<img src="../image/right_double_arrow.png" />
					<img src="../image/next_song.png" />
					<h4>下一曲</h4>
				</div>
			</ul>
		</div>

	</body>
	<script src="../js/mui.min.js"></script>
	<script>
		mui.init({
			swipeBack: true //启用右滑关闭功能
		});
		document.getElementById("attitude").addEventListener("toggle", function(event) {
			if (event.detail.isActive) {
				// console.log("AT+CK=1\r\n");
				SPP_sendAT("AT+PT=0\r\n");
			} else {
				// console.log("AT+CK=0\r\n");
				SPP_sendAT("AT+PT=1\r\n");
			}
		});
		document.getElementById("attitude1").addEventListener("toggle", function(event) {
			if (event.detail.isActive) {
				// console.log("AT+CK=1\r\n");
				SPP_sendAT("AT+PT=2\r\n");
			} else {
				// console.log("AT+CK=0\r\n");
				SPP_sendAT("AT+PT=3\r\n");
			}
		});
	</script>
</html>