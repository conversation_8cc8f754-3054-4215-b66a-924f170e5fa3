<template>
  <view class="container">
    <!-- 顶部状态栏占位 -->
    <view class="status-bar" v-if="false"></view>
    
    <!-- 主体内容区 -->
    <view class="content">
      <!-- 标题区域 -->
      <view class="header">
		<view class="title">小叶同学<slot name="func"></slot></view>
		<view class="subtitle">为您提供无限可能的智能体验！</view>
      </view>

      <!-- 添加新设备卡片 -->
      <view class="device-card">
        <!-- 初始状态 -->
        <view v-if="deviceStatus === 'initial'" class="card-content" @click="startDeviceAdd">
          <view class="icon-box" v-if="!loading">
            <u-icon name="plus-circle" color="#4B7BEC" size="60"></u-icon>
          </view>
          <view v-else class="loading-box">
            <u-loading size="60" mode="circle"></u-loading>
          </view>
          <view class="text-box">
            <text class="item-title">添加绑定新设备</text>
            <text class="item-desc">连接授权智能设备</text>
          </view>
        </view>

        <!-- 连接状态 -->
        <view v-if="deviceStatus === 'connecting'" class="card-content">
          <view class="device-info">
            <view class="device-details">
              <text class="device-name">H104</text>
              <text class="device-mac">00:19:00:00:C4:0A</text>
            </view>
          </view>
          <view class="connect-input">
            <input
              v-model="connectCode"
              placeholder="请输入连接码"
              class="input-box"
            />
            <view class="scan-icon" @click="scanQRCode">
              <u-icon name="scan" size="40" color="#4B7BEC"></u-icon>
            </view>
          </view>
          <view v-if="!loading" class="connect-btn" @click="connectDevice">
            <text>连接</text>
          </view>
          <view v-else class="loading-box">
            <u-loading size="40" mode="circle"></u-loading>
          </view>
        </view>

        <!-- 已连接状态 -->
        <view v-if="deviceStatus === 'connected'" class="card-content">
          <view class="device-info">
            <view class="info-row">
              <view class="connected-status">
                <u-icon name="checkmark-circle" color="#ffffff" size="44"></u-icon>
                <text>已连接</text>
              </view>
              <view class="device-details">
                <text class="device-name">H104</text>
                <text class="device-mac">00:19:00:00:C4:0A</text>
              </view>
            </view>
          </view>
          <view class="disconnect-btn" @click="disconnectDevice">
            <text>解绑</text>
          </view>
        </view>
      </view>

      <!-- 功能区域 -->
      <view class="grid">
        <!-- 智能助手 -->
        <view class="grid-item assistant" @click="navigateTo('/pages/aiku/assistant/index')">
          <view class="icon-box">
            <u-icon name="account" color="#4B7BEC" size="60"></u-icon>
          </view>
          <view class="text-box">
            <text class="item-title">智能助手</text>
            <text class="item-desc">智能AI小帮手</text>
          </view>
        </view>

        <!-- 同传翻译 -->
        <view class="grid-item translate" @click="navigateTo('/pages/aiku/translate')">
          <view class="icon-box">
            <u-icon name="chat" color="#FF7F50" size="60"></u-icon>
          </view>
          <view class="text-box">
            <text class="item-title">同传翻译</text>
            <text class="item-desc">多语种实时翻译</text>
          </view>
        </view>

        <!-- 会议助理 -->
        <view class="grid-item meeting" @click="navigateTo('/pages/ai/meeting/index')">
          <view class="icon-box">
            <u-icon name="man-add-fill" color="#00CED1" size="60"></u-icon>
          </view>
          <view class="text-box">
            <text class="item-title">会议助理</text>
            <text class="item-desc">智能会议整理</text>
          </view>
        </view>

        <!-- 蓝牙翻译棒 -->
        <view class="grid-item bluetooth" @click="navigateTo('/pages/ai/bluetooth/index')">
          <view class="icon-box">
            <u-icon name="gift-fill" color="#9B59B6" size="60"></u-icon>
          </view>
          <view class="text-box">
            <text class="item-title">蓝牙翻译棒</text>
            <text class="item-desc">蓝牙翻译设备</text>
          </view>
        </view>
      </view>

      <!-- FAQ 区域 -->
      <view class="faq" @click="navigateTo('/pages/aiku/faq')">
        <view class="faq-bg">
          <view class="faq-content">
            <view class="faq-image">
              <image src="/static/images/faq-illustration.png" mode="aspectFit"></image>
            </view>
            <view class="faq-text">
              <text class="faq-title">FAQ</text>
              <text class="faq-desc">常见问题解答</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      deviceStatus: 'initial', // initial, connecting, connected
      loading: false,
      connectCode: '',
    }
  },
  methods: {
    navigateTo(url) {
      uni.navigateTo({ url })
    },
    
    // 开始添加设备
    async startDeviceAdd() {
      this.loading = true
      await this.delay(1500) // 显示1.5秒加载动画
      this.loading = false
      this.deviceStatus = 'connecting'
    },
    
    // 扫描二维码
    scanQRCode() {
      uni.scanCode({
        success: (res) => {
          this.connectCode = res.result
        },
        fail: (err) => {
          uni.showToast({
            title: '扫码失败',
            icon: 'none'
          })
        }
      })
    },
    
    // 连接设备
    async connectDevice() {
      if (!this.connectCode) {
        uni.showToast({
          title: '请输入连接码',
          icon: 'none'
        })
        return
      }
      
      this.loading = true
      await this.delay(1500) // 显示1.5秒加载动画
      this.loading = false
      this.deviceStatus = 'connected'
      
      uni.showToast({
        title: '连接成功',
        icon: 'success'
      })
    },
    
    // 解绑设备
    async disconnectDevice() {
      this.loading = true
      await this.delay(1500)
      this.loading = false
      this.deviceStatus = 'initial'
      this.connectCode = ''
      
      uni.showToast({
        title: '已解绑',
        icon: 'success'
      })
    },
    
    // 延时函数
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  // background: linear-gradient(180deg, #E8F0FF 0%, #FFFFFF 100%);
}

.status-bar {
  height: var(--status-bar-height);
  width: 100%;
  background: transparent;
}

.content {
  padding: 32rpx;
}

.header {
  margin-bottom: 40rpx;
  .title {
    font-size: 48rpx;
    font-weight: bold;
    color: #333;
    line-height: 1.4;
	display: flex;
	justify-content: space-between;
	align-items: center;
    // display: inline-block;
    margin-bottom: 16rpx;
  }
  
  .subtitle {
    font-size: 28rpx;
    color: #666;
    line-height: 1.4;
    display: block;
  }
}

.device-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin: 32rpx auto;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  position: relative;
  width: 100%;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(75,123,236,0.1), rgba(75,123,236,0.05));
    border-radius: 20rpx;
    z-index: 0;
  }
  
  .card-content {
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .loading-box {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20rpx;
  }
  
  .icon-box {
    margin-bottom: 20rpx;
    position: relative;
    z-index: 1;
  }
  
  .text-box {
    text-align: center;
    position: relative;
    z-index: 1;
    
    .item-title {
      font-size: 32rpx;
      color: #333;
      font-weight: bold;
      line-height: 1.4;
      display: block;
      margin-bottom: 8rpx;
    }
    
    .item-desc {
      font-size: 24rpx;
      color: #666;
      line-height: 1.4;
      display: block;
    }
  }
  
  .device-info {
    width: 100%;
    margin-bottom: 20rpx;
    
    .device-details {
      text-align: center;
      margin-bottom: 30rpx;
      
      .device-name {
        font-size: 32rpx;
        color: #333;
        font-weight: bold;
        line-height: 1.4;
        display: block;
        margin-bottom: 12rpx;
      }
      
      .device-mac {
        font-size: 24rpx;
        color: #666;
        line-height: 1.4;
        display: block;
      }
    }
    
    .info-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20rpx;
      background: rgba(76, 217, 100, 0.1);
      border-radius: 16rpx;
      
      .connected-status {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 16rpx 24rpx;
        background: #4cd964;
        border-radius: 12rpx;
        box-shadow: 0 4rpx 8rpx rgba(76, 217, 100, 0.2);
        animation: fadeIn 0.3s ease-in-out;
        
        .u-icon {
          margin-bottom: 8rpx;
        }
        
        text {
          font-size: 24rpx;
          color: #ffffff;
          font-weight: bold;
        }
      }
      
      .device-details {
        flex: 1;
        margin: 0 0 0 40rpx;
        text-align: left;
        
        .device-name {
          margin-bottom: 8rpx;
        }
      }
    }
  }
  
  .connect-input {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    width: 100%;
    
    .input-box {
      flex: 1;
      height: 80rpx;
      border: 2rpx solid #e4e7ed;
      border-radius: 10rpx;
      padding: 0 20rpx;
      font-size: 28rpx;
    }
    
    .scan-icon {
      margin-left: 20rpx;
      padding: 10rpx;
    }
  }
  
  .connect-btn {
    background-color: #4B7BEC;
    color: #fff;
    padding: 20rpx 60rpx;
    border-radius: 10rpx;
    text-align: center;
    font-size: 28rpx;
    width: 100%;
  }
  
  .disconnect-btn {
    background-color: #FF7F50;
    color: #fff;
    padding: 20rpx 60rpx;
    border-radius: 10rpx;
    text-align: center;
    font-size: 28rpx;
    width: 100%;
  }
}

.grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.grid-item {
  padding: 30rpx;
  border-radius: 20rpx;
  position: relative;
  overflow: hidden;
  min-height: 180rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  
  &.assistant {
    background: linear-gradient(135deg, rgba(75,123,236,0.1) 0%, rgba(56,103,214,0.1) 100%);
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(75,123,236,0.05) 0%, rgba(56,103,214,0.05) 100%);
      z-index: 0;
    }
  }
  
  &.translate {
    background: linear-gradient(135deg, rgba(255,127,80,0.1) 0%, rgba(255,99,72,0.1) 100%);
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255,127,80,0.05) 0%, rgba(255,99,72,0.05) 100%);
      z-index: 0;
    }
  }
  
  &.meeting {
    background: linear-gradient(135deg, rgba(0,206,209,0.1) 0%, rgba(0,180,216,0.1) 100%);
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(0,206,209,0.05) 0%, rgba(0,180,216,0.05) 100%);
      z-index: 0;
    }
  }
  
  &.bluetooth {
    background: linear-gradient(135deg, rgba(155,89,182,0.1) 0%, rgba(142,68,173,0.1) 100%);
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(155,89,182,0.05) 0%, rgba(142,68,173,0.05) 100%);
      z-index: 0;
    }
  }
  
  .icon-box {
    margin-bottom: 20rpx;
    position: relative;
    z-index: 1;
  }
  
  .text-box {
    position: relative;
    z-index: 1;
    
    .item-title {
      font-size: 32rpx;
      color: #333;
      font-weight: bold;
      line-height: 1.4;
      display: block;
      margin-bottom: 8rpx;
    }
    
    .item-desc {
      font-size: 24rpx;
      color: #666;
      line-height: 1.4;
      display: block;
    }
  }
}

.faq {
  position: relative;
  width: 100%;
  height: 260rpx;
  border-radius: 20rpx;
  overflow: hidden;
  background: linear-gradient(135deg, #6C5CE7 0%, #4834D4 100%);
  
  .faq-bg {
    position: relative;
    width: 100%;
    height: 100%;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(108,92,231,0.2) 0%, rgba(72,52,212,0.2) 100%);
      z-index: 1;
    }
  }
  
  .faq-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    padding: 0 40rpx;
    z-index: 2;
    
    .faq-image {
      position: absolute;
      right: 40rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 400rpx;
      height: 200rpx;
      opacity: 0.9;
      
      image {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
    
    .faq-text {
      position: relative;
      z-index: 3;
      
      .faq-title {
        font-size: 48rpx;
        color: #fff;
        font-weight: bold;
        line-height: 1.4;
        display: block;
        margin-bottom: 8rpx;
        text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
      }
      
      .faq-desc {
        font-size: 28rpx;
        color: rgba(255,255,255,0.9);
        line-height: 1.4;
        display: block;
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>