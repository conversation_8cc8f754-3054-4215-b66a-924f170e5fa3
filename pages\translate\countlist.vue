<template>
	<view class="content">
		<!-- <u-navbar title="自由说模式" leftIcon="arrow-left" :autoBack="true" @rightClick="rightClick" :placeholder="true" bgColor="#f3f4f6"  rightIcon="list-dot"></u-navbar> -->
		<view class="top">
			<u-icon name="arrow-left" size="28" color="#515151" @click="arroeleft"></u-icon>
			<view :class="this.tab=='true'?'targett':'target'" @click="countlist('true')">
				<view class="tit">源语言</view>
			</view>
			<view :class="this.tab=='false'?'targett':'target'" @click="countlist('false')">
				<view class="tit">目标语言</view>
			</view>
			<u-icon name="arrow-left" size="28" color="#f3f4f6" ></u-icon>
			
		</view>
		<view class="search">
			<u-search v-model="searchcalue"  :showAction="true" actionText="搜索" :animation="true" @change="search" @custom="search"></u-search>
			
		</view>
		<view class="list" v-if="searchcalue==''">
			<view class="listbox" v-for="(item,index) in jsonData" :key="index" @click="setlanguage(item.language)">
				{{item.language}}
				<u-icon name="checkbox-mark" color="red" v-if="languagee==item.language"></u-icon>
			</view>
			
		</view>
		<view class="list" v-if="searchcalue!=''">
			<view class="listbox" v-for="(item,index) in filteredLanguages" :key="index" @click="setlanguage(item.language)">
				{{item.language}}
				<u-icon name="checkbox-mark" color="red" v-if="languagee==item.language"></u-icon>
			</view>
			
		</view>
		
	</view>
</template>

<script>
	import countdata from '@/static/aiku/countlist.json';
	export default {
		data() {
			return {
				tab:'true',//true就是源语言，false就是目标语言
				jsonData: countdata,
				languagee:"",
				searchcalue:"",//搜索框的值
				filteredLanguages:[]
			}
		},
		onLoad(option) {
			this.tab=option.tab
			if (this.tab=='true') {
				this.languagee= uni.getStorageSync('sourcelanguagee');
			} else{
				this.languagee= uni.getStorageSync('targetlanguagee');
			}
		},
		methods: {
			search(){
				this.filteredLanguages = this.jsonData.filter(language => language.language.includes(this.searchcalue));
			},
			arroeleft() {
				this.$router.back();
			},
			countlist(value){
				this.tab=value
				if (this.tab=='true') {
					this.languagee= uni.getStorageSync('sourcelanguagee');
				} else{
					this.languagee= uni.getStorageSync('targetlanguagee');
				}
			},
			setlanguage(value){
				this.searchcalue=""
				if (this.tab=='true') {
					uni.setStorageSync('sourcelanguagee', value);
					this.languagee= uni.getStorageSync('sourcelanguagee');
				} else{
					uni.setStorageSync('targetlanguagee', value);
					this.languagee= uni.getStorageSync('targetlanguagee');
				}
			}
			
		}
	}
</script>

<style lang="scss">
	page{
		height: 100%;
		background-color: #f8f8f8;
	}
	.content{
		width: 100%;
		.top{
			width: 100%;
			height: 120rpx;
			display: flex;
			    align-items: center;
			    justify-content: space-around;
			background-color: #f3f4f6;
			.target{
				width: 200rpx;
				height: 80rpx;
				padding: 0rpx 10rpx;
				border-radius: 20rpx;
				font-size: 35rpx;
				background-color: #cfe9f9;
				
				 box-shadow: 0rpx 0rpx 3px #cacaca;
				.tit{
					text-align: center;
					line-height: 80rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					width: 200rpx;
					height: 80rpx;
				}
			  
			
			}
			.targett{
				width: 200rpx;
				height: 80rpx;
				padding: 0rpx 10rpx;
				border-radius: 20rpx;
				font-size: 35rpx;
				background-color: #89c9f1;
				
				box-shadow: 0rpx 0rpx 3px #cacaca;
				.tit{
					text-align: center;
					line-height: 80rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					width: 200rpx;
					height: 80rpx;
					// color: #fff;
				}
			}
		}
		.search{
			padding: 30rpx;
		}
		.list{
			.listbox{
				width: 650rpx;
				height: 120rpx;
				padding: 0rpx 50rpx;
				border-bottom: 1rpx solid #cacaca;
				font-size: 35rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
			}
		}
	}
</style>
