<template>
	<view class="content">
		<!-- <u-navbar title="自由说模式" leftIcon="arrow-left" :autoBack="true" @rightClick="rightClick" :placeholder="true" bgColor="#f3f4f6"  rightIcon="list-dot"></u-navbar> -->
		<navname :targetlanguage="targetlanguagee" :sourcelanguage="sourcelanguagee"></navname>
		<view class="dialoguebox" :style="{ fontSize: fontsizee + 'rpx' }">
			<view class="left">
				<view class="icon">
					<u-icon name="server-fill" size="28"></u-icon>
				</view>
				<view class="dialogue">
					<view class="isaid">
						{{isaid}}
					</view>
					<view class="translational">
						<view class="hesaid">
							what
						</view>
						<u-icon name="volume" size="28"></u-icon>
					</view>
				</view>
			</view>
			<view class="right">
				<view class="icon">
					<u-icon name="kefu-ermai" size="28"></u-icon>
				</view>
				<view class="dialogue">
					<view class="isaid">
						今天天气很好
					</view>
					<view class="translational">
						<view class="hesaid">
							what
						</view>
						<u-icon name="volume" size="28"></u-icon>
					</view>
				</view>
			</view>
		</view>
		<button type="primary" @click="hasPermission()">检查是否有蓝牙权限</button>
		        <button type="primary" @click="isSupport">检查设备是否支持蓝牙</button>
		        <button type="primary" @click="isOpen">判断蓝牙是否打开</button>
		        <button type="primary" @click="openBT">打开蓝牙</button>
		        <button type="primary" @click="closeBT">关闭蓝牙</button>
		        <button type="primary" @click="listBondedBT">获取已配对列表</button>
		        <button type="primary" @click="searchBT">查找蓝牙设备</button>
		        <button type="primary" @click="stopSearch">停止查找蓝牙</button>
		        <button type="primary" @click="connect">连接蓝牙</button>
		        <button type="primary" @click="sendBytes">发送字节数组</button>
		        <button type="primary" @click="sendHexStr">发送十六进制字符串</button>
		        <button type="primary" @click="sendGBKStr">发送GBK字符</button>
		        <button type="primary" @click="isConnected">连接状态</button>
		        <button type="primary" @click="pairBT">配对</button>
		        <text>2023-09-09更新</text>
		        <button type="primary" @click="createBond">新配对</button>
		        <button type="primary" @click="removeBond">取消配对</button>
		        <button type="primary" @click="disconnectAll">断开所有连接</button>
		        <button type="primary" @click="checkConnect">检查连接</button>
		        <button type="primary" @click="registerReceivedCallback">注册数据接收回调</button>
		        <button type="primary" @click="unRegisterReceivedCallback">注销数据接收回调</button>
		        <button type="primary" @click="registerConnectStatusChangeCallback">注册连接状态回调</button>
		        <button type="primary" @click="unRegisterConnectStatusChangeCallback">注销连接状态回调</button>
		        <button type="primary" @click="sendData">发送数据（整合版）</button>
		<view class="bot">
			<view class="conversation" @click="press">
				开始对话
			</view>
			<view v-if="longPress == '1'" class="loaderbox">
				<view class="prompt-loader">
					<view class="em" v-for="(item,index) in 20" :key="index"></view>
				</view>
			</view>
		</view>
	</view>
</template>

 <script >
	 // src="https://cdn.jsdelivr.net/npm/dsbridge/dist/dsbridge.js"
	import nav from "@/components/nav.vue"
	// import free from "@/api/api.js";
	 var blueModule = uni.requireNativePlugin("XM-Bluetooth2Module")
	    const modal = uni.requireNativePlugin('modal');
	export default {
		data() {
			return {
				targetlanguagee: "", //目标语言
				sourcelanguagee: "", //源语言
				longPress: "0", //是否开始语言0未开始1开始
				isaid: "今天天气很好",
				fontsizee: "",
				datamete: {
					src: "zh",
					tgt: "en",
					text: this.isaid,
					appkey: "B262EB2D7226C2FE12B19EE8C2CA4DFB"
				},
				recorderManager: null,
				isRecording: false
			}
		},
		onLoad() {
			  // const dsBridge = this.$dsBridge;
			  //  dsBridge.init();
			  //    dsBridge.call('nativeMethod', 'data', (response) => {
			  //          // 处理原生返回的结果
			  //          console.log('Received response from Android:', response);
			  //        });
			// free(this.datamete).then((res) => {
			// 	console.log(res)
			// 	// uni.hideLoading();
			// });
			// console.log(free);
			// uni.request({
			// 	url: 'http://120.77.30.57/test_api', // 请求的URL
			// 	method: 'POST', // 请求方法
			// 	data: {
			// 		// 请求参数
			// 		src: "zh",
			// 		tgt: "en",
			// 		text: this.isaid,
			// 		appkey: "B262EB2D7226C2FE12B19EE8C2CA4DFB"
			// 	},
			// 	header: {
			// 		'Content-Type': 'application/json; charset=utf-8' // 请求头
			// 	},
			// 	success: function(res) {
			// 		console.log(res.data); // 请求成功的回调
			// 	},
			// 	fail: function(err) {
			// 		console.log(err); // 请求失败的回调
			// 	}
			// });
			// uni.getBluetoothDevices({
			//  success: function (res) {
			//     console.log('获取到的蓝牙设备列表：', res.devices);
			//     res.devices.forEach(device => {
			//       console.log('设备名称：', device.name);
			//     });
			//  }
			// });
			// uni.openBluetoothAdapter({
			//         success: (res) => {
			//           console.log('蓝牙适配器开启成功', res);
			//           // this.startBluetoothDevicesDiscovery();
			// 		   uni.startBluetoothDevicesDiscovery({
			// 		          success: (res) => {
			// 		            console.log('搜索设备成功', res);
			// 		            // this.onBluetoothDeviceFound();
			// 		          },
			// 		          fail: (err) => {
			// 		            console.log('搜索设备失败', err);
			// 		          }
			// 		        });
			//         },
			//         fail: (err) => {
			//           console.log('蓝牙适配器开启失败', err);
			//         }
			//       });
		},
		components: {
			navname: nav
		},
		onShow() {
			this.fontsizee = uni.getStorageSync('fontsizee');
			console.log(this.fontsizee);
			this.targetlanguagee = uni.getStorageSync('targetlanguagee');
			this.sourcelanguagee = uni.getStorageSync('sourcelanguagee');

		},
		methods: {
			hasPermission() {
			                blueModule.hasPermission((ret) => {
			                    console.log(ret)
			                    modal.toast({
			                        //发送操作结果
			                        message: ret,
			                        duration: 1.5
			                    });
			                });
			            },
			            isSupport() {
			                blueModule.isSupport((ret) => {
			                    console.log(ret)
			                    modal.toast({
			                        //发送操作结果
			                        message: ret,
			                        duration: 1.5
			                    });
			                });
			            },
			            isOpen() {
			                blueModule.isOpen((ret) => {
			                    console.log(ret)
			                    modal.toast({
			                        //发送操作结果
			                        message: ret,
			                        duration: 1.5
			                    });
			                });
			            },
			            openBT() {
			                blueModule.openBT((ret) => {
			                    console.log(ret)
			                    modal.toast({
			                        //发送操作结果
			                        message: ret,
			                        duration: 1.5
			                    });
			                });
			            },
			            closeBT() {
			                blueModule.closeBT((ret) => {
			                    console.log(ret)
			                    modal.toast({
			                        //发送操作结果
			                        message: ret,
			                        duration: 1.5
			                    });
			                });
			            },
			            listBondedBT() {
			                blueModule.listBondedBT((ret) => {
			                    console.log(ret)
			                    modal.toast({
			                        //发送操作结果
			                        message: ret,
			                        duration: 1.5
			                    });
			                });
			            },
			            searchBT() {
			                blueModule.searchBT((ret) => {
			                    console.log(ret)
			                    modal.toast({
			                        //发送操作结果
			                        message: ret,
			                        duration: 1.5
			                    });
			                });
			            },
			            stopSearch() {
			                blueModule.stopSearch();
			            },
			            //连接
			            connect() {
			                blueModule.connect({
			                    MACAddress: 'DC:0D:30:22:FC:FB',
			                    uuid: '00001101-0000-1000-8000-00805f9b34fb',
			                    sleepTime: 50 //接收对面设备发来的信息刷新间隔
			                }, (result) => {
			                    //连接结果
			                    console.log(result)
			                    modal.toast({
			                        message: result,
			                        duration: 1.5
			                    });
			                }, (data) => {
			                    //接收的数据回调
			                    console.log(data)
			                    modal.toast({
			                        //发送操作结果
			                        message: data,
			                        duration: 1.5
			                    });
			                })
			            },
			            //断开连接
			            disConnect() {
			                blueModule.disconnect({
			                    MACAddress: 'DC:0D:30:22:FC:FB',
			                    uuid: '00001101-0000-1000-8000-00805f9b34fb',
			                    sleepTime: 50 //接收对面设备发来的信息刷新间隔
			                }, (result) => {
			                    //连接结果
			                    console.log(result)
			                    modal.toast({
			                        message: result,
			                        duration: 1.5
			                    });
			                });
			            },
			            //写入数据
			            sendBytes() {
			                blueModule.sendBytes([0x00, 0xff, 0xee], (result) => {
			                    //结果
			                    console.log(result)
			                    modal.toast({
			                        message: result,
			                        duration: 1.5
			                    });
			                });
			            },
			            sendGBKStr() {
			                var ret = blueModule.sendGBKStr('我是gbk', (result) => {
			                    //结果
			                    console.log(result)
			                    modal.toast({
			                        message: result,
			                        duration: 1.5
			                    });
			                });
			                console.log(ret)
			                modal.toast({
			                    //发送操作结果
			                    message: ret,
			                    duration: 1.5
			                });
			            },
			            sendHexStr() {
			                var ret = blueModule.sendHexStr('FFFF', (result) => {
			                    //结果
			                    console.log(result)
			                    modal.toast({
			                        message: result,
			                        duration: 1.5
			                    });
			                });
			                console.log(ret)
			                modal.toast({
			                    //发送操作结果
			                    message: ret,
			                    duration: 1.5
			                });
			            },
			            sendUtf8Str() {
			                var ret = blueModule.sendUtf8Str('我是utf-8', (result) => {
			                    //结果
			                    console.log(result)
			                    modal.toast({
			                        message: result,
			                        duration: 1.5
			                    });
			                });
			                console.log(ret)
			                modal.toast({
			                    //发送操作结果
			                    message: ret,
			                    duration: 1.5
			                });
			            },
			            pairBT() {
			                blueModule.pairBT({
			                    mac: ''
			                }, (ret) => {
			                    console.log(ret);
			                    modal.toast({
			                        message: ret,
			                        duration: 1.5
			                    });
			                });
			            },
			            isConnected() {
			                var ret = blueModule.isConnected();
			                console.log(ret)
			                modal.toast({
			                    //发送操作结果
			                    message: ret,
			                    duration: 1.5
			                });
			            },
			            createBond() {
			                blueModule.createBond({
			                    mac: ''
			                }, (ret) => {
			                    console.log(ret);
			                    modal.toast({
			                        message: ret,
			                        duration: 1.5
			                    });
			                });
			            },
			            removeBond() {
			                blueModule.removeBond({
			                    mac: ''
			                }, (ret) => {
			                    console.log(ret);
			                    modal.toast({
			                        message: ret,
			                        duration: 1.5
			                    });
			                });
			            },
			            disconnectAll() {
			                blueModule.disconnectAll((ret) => {
			                    console.log(ret);
			                    modal.toast({
			                        message: ret,
			                        duration: 1.5
			                    });
			                });
			            },
			            checkConnect() {
			                blueModule.checkConnect({
			                    mac: '',
			                    uuid: ''
			                }, (ret) => {
			                    console.log(ret);
			                    modal.toast({
			                        message: ret,
			                        duration: 1.5
			                    });
			                });
			            },
			            registerReceivedCallback() {
			                blueModule.registerReceivedCallback((ret) => {
			                    console.log(ret);
			                    modal.toast({
			                        message: ret,
			                        duration: 1.5
			                    });
			                });
			            },
			            unRegisterReceivedCallback() {
			                blueModule.unRegisterReceivedCallback();
			            },
			            registerConnectStatusChangeCallback() {
			                blueModule.registerConnectStatusChangeCallback((ret) => {
			                    console.log(ret);
			                    modal.toast({
			                        message: ret,
			                        duration: 1.5
			                    });
			                });
			            },
			            unRegisterConnectStatusChangeCallback() {
			                blueModule.unRegisterConnectStatusChangeCallback();
			            },
			            sendData() {
			                blueModule.sendData({
			                    mac: '',
			                    uuid: '',
			                    bytes: [0xFF, 0xF0],//优先级第一
			                    hexStr: 'FFF0',//优先级第二
			                    utf8: '你好',//优先级第三
			                    gbk: '你好',//优先级第四
			                    ascii: 'abcd'//优先级第五
			                }, (ret) => {
			                    console.log(ret);
			                    modal.toast({
			                        message: ret,
			                        duration: 1.5
			                    });
			                });
			            },
					
			press() {
				if (this.longPress == "0") {
					this.longPress = "1"
					if (this.longPress = "1") {
						this.recorderManager = uni.getRecorderManager();
						this.recorderManager.start({
							duration: 60000, // 录音的最大时长，单位 ms
							sampleRate: 44100, // 采样率
							numberOfChannels: 1, // 录音的声道数
							encodeBitRate: 192000, // 编码码率
							format: 'aac' // 音频格式
						});
						this.isRecording = true;
						this.recorderManager.onStart(() => {
							console.log('录音开始');
						});
						this.recorderManager.onError((err) => {
							console.error('录音错误：', err);
						});
					}
				} else {
					this.longPress = "0"
					if (this.longPress = "0") {
						if (!this.isRecording || !this.recorderManager) {
							return;
						}
						this.recorderManager.stop(); // 停止录音
						this.recorderManager.onStop((res) => {
							console.log('录音停止', res);
							const {
								tempFilePath
							} = res;
							// 处理录音文件，例如上传到服务器
							console.log('录音文件:', tempFilePath);
						});
						this.isRecording = false;
					}
				}
			}
		}
	}
</script>

<style lang="scss">
	page {
		height: 100%;
		background-color: #f8f8f8;
	}

	.content {
		width: 100%;

		.dialoguebox {
			width: 710rpx;
			margin: 0 auto;

			// font-size: 45rpx;
			.left {
				// width: 100%;
				// padding: 20rpx;
				font-weight: 600;
				letter-spacing: 5rpx;
				display: flex;

				.icon {
					width: 80rpx;
					height: 80rpx;
					display: flex;
					justify-content: center;
					align-items: center;
					background-color: #cfe9f9;
					border-radius: 50rpx;
				}

				.dialogue {
					padding: 20rpx;
					margin: 20rpx;
					border-radius: 30rpx 30rpx 30rpx 0rpx;
					background-color: #cfe9f9;

					.isaid {
						color: #5681ce;
					}

					.translational {

						display: flex;
						align-items: center;
						justify-content: space-between;
					}
				}
			}

			.right {
				// width: 100%;
				// padding: 20rpx;
				font-weight: 600;
				letter-spacing: 5rpx;
				display: flex;
				flex-direction: row-reverse;

				.icon {
					width: 80rpx;
					height: 80rpx;
					display: flex;
					justify-content: center;
					align-items: center;
					background-color: #cfe9f9;
					border-radius: 50rpx;
				}

				.dialogue {
					padding: 20rpx;
					margin: 20rpx;
					border-radius: 30rpx 30rpx 30rpx 0rpx;
					background-color: #5681ce;
					color: #fff;

					.hesaid {
						color: #000;
					}

					.translational {

						display: flex;
						align-items: center;
						justify-content: space-between;
					}
				}
			}
		}

		.bot {
			width: 100%;
			height: 200rpx;
			background-color: #fff;
			z-index: 100;
			position: fixed;
			bottom: 0rpx;
			left: 0rpx;

			.conversation {
				width: 500rpx;
				height: 80rpx;
				border-radius: 20rpx;
				margin: 0 auto;
				text-align: center;
				line-height: 80rpx;
				margin-top: 20rpx;
				color: #fff;
				background: linear-gradient(to bottom, #5681ce, #cfe9f9);
			}

			.loaderbox {
				width: 100%;
				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				.prompt-loader {
					width: 200px;
					height: 20px;
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-bottom: 6px;
				}

				.prompt-loader .em {
					display: block;
					background: #399bee;
					width: 1px;
					height: 10%;
					margin-right: 2.5px;
					float: left;
				}

				.prompt-loader .em:last-child {
					margin-right: 0px;
				}

				.prompt-loader .em:nth-child(1) {
					animation: load 2s 1.4s infinite linear;
				}

				.prompt-loader .em:nth-child(2) {
					animation: load 2s 1.2s infinite linear;
				}

				.prompt-loader .em:nth-child(3) {
					animation: load 2s 1s infinite linear;
				}

				.prompt-loader .em:nth-child(4) {
					animation: load 2s 0.8s infinite linear;
				}

				.prompt-loader .em:nth-child(5) {
					animation: load 2s 0.6s infinite linear;
				}

				.prompt-loader .em:nth-child(6) {
					animation: load 2s 0.4s infinite linear;
				}

				.prompt-loader .em:nth-child(7) {
					animation: load 2s 0.2s infinite linear;
				}

				.prompt-loader .em:nth-child(8) {
					animation: load 2s 0s infinite linear;
				}

				.prompt-loader .em:nth-child(9) {
					animation: load 2s 0.2s infinite linear;
				}

				.prompt-loader .em:nth-child(10) {
					animation: load 2s 0.4s infinite linear;
				}

				.prompt-loader .em:nth-child(11) {
					animation: load 2s 0.6s infinite linear;
				}

				.prompt-loader .em:nth-child(12) {
					animation: load 2s 0.8s infinite linear;
				}

				.prompt-loader .em:nth-child(13) {
					animation: load 2s 1s infinite linear;
				}

				.prompt-loader .em:nth-child(14) {
					animation: load 2s 1.2s infinite linear;
				}

				.prompt-loader .em:nth-child(15) {
					animation: load 2s 1.4s infinite linear;
				}

				.prompt-loader .em:nth-child(16) {
					animation: load 2s 1.6s infinite linear;
				}

				.prompt-loader .em:nth-child(17) {
					animation: load 2s 1.8s infinite linear;
				}

				.prompt-loader .em:nth-child(18) {
					animation: load 2s 2s infinite linear;
				}

				.prompt-loader .em:nth-child(19) {
					animation: load 2s 2.2s infinite linear;
				}

				.prompt-loader .em:nth-child(20) {
					animation: load 2s 2.4s infinite linear;
				}

				@keyframes load {
					0% {
						height: 10%;
					}

					50% {
						height: 100%;
					}

					100% {
						height: 10%;
					}
				}

				// 	/* 语音音阶-------------------- */
				// 	.prompt-layer-2 {
				// 		top: -40px;
				// 	}

				// 	.prompt-layer-2 .text {
				// 		color: rgba(0, 0, 0, 1);
				// 		font-size: 12px;
				// 	}
			}

		}
	}
</style>