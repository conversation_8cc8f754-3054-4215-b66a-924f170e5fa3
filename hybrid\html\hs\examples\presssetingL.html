<!DOCTYPE html>
<html>

	<head>
		<meta charset="utf-8">
		<title></title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">

		<!--标准mui.css-->
		<link rel="stylesheet" href="../css/mui.min.css">
		<!--App自定义的css-->
		<!--<link rel="stylesheet" type="text/css" href="../css/app.css"/>-->
		<style>
			.mui-row.mui-fullscreen>[class*="mui-col-"] {
				height: 100%;
			}

			.title {
				margin: 20px 15px 10px;
				color: #6d6d72;
				font-size: 15px;
			}

			.oa-contact-cell.mui-table .mui-table-cell {
				padding: 11px 0;
				vertical-align: middle;
			}

			.oa-contact-cell {
				position: relative;
				margin: -11px 0;
			}

			.mui-checkbox {

				width: 20px;
				height: 20px;
				margin: 10px;
			}

			#head {
				line-height: 20px;
			}

			.head-img {
				width: 32px;
				height: 32px;
				margin: 10px;
			}

			#head-img1 {
				position: absolute;
				bottom: 10px;
				right: 40px;
				width: 40px;
				height: 40px;
			}

			.oa-contact-avatar {
				width: 75px;
			}

			.oa-contact-avatar img {
				border-radius: 50%;
			}

			.oa-contact-content {
				width: 100%;
			}

			.oa-contact-name {
				margin-right: 20px;
			}

			.oa-contact-name,
			oa-contact-position {
				float: left;
			}

			.mui-radio-right {
				display: flex;
				align-items: center;
			}

			.mui-radio-right input[type="checkbox"] {
				margin-left: auto;
				/* 确保复选框出现在正确的位置 */
			}
		</style>

	</head>
	<body>
		<header class="mui-bar mui-bar-nav">
			<a onClick="javascript:history.back(-1);" class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></a>
			<h1 id="name" class="mui-title">左耳按键</h1>
		</header>
		<div class="mui-content">
			<div class="mui-card">
				<ul id="eatsettinglist" class="mui-table-view mui-table-view-radio">
					<li class="mui-table-view-cell">
						<a class="mui-navigate-right">
							噪声控制
						</a>
					</li>
					<li class="mui-table-view-cell">
						<a class="mui-navigate-right">
							语音助手
						</a>
					</li>
				</ul>
			</div>

		</div>
		<div id="controlUI">
			<h5 class="mui-content-padded">噪声控制</h5>
			<div class="mui-card">
				<ul class="mui-table-view">
					<li class="mui-table-view-cell">
						<label class="mui-radio mui-radio-right">
							<img class="mui-pull-right head-img " src="../image/mipmap-xxhdpi/key_noise_down_icon.png">
							<p>噪声<br>阻隔外部声音</p>
							<input type="checkbox" id="noise-down">
						</label>
					</li>
					<li class="mui-table-view-cell">
						<label class="mui-radio mui-radio-right">
							<img class="mui-pull-right head-img" src="../image/mipmap-xxhdpi/key_noise_close_icon.png">
							<p>关闭<br>关闭降噪和通透</p>
							<input type="checkbox" id="noise-close">
						</label>
					</li>
					<li class="mui-table-view-cell">
						<label class="mui-radio mui-radio-right">
							<img class="mui-pull-right head-img" src="../image/mipmap-xxhdpi/key_noise_full_icon.png">
							<p>通透<br>允许外部声音</p>
							<input type="checkbox" id="noise-full">
						</label>
					</li>
				</ul>
			</div>
			<h5 class="mui-content-padded">按住耳机柄在所选噪声控制模式之间切换，可选择多种</h5>
		</div>
		<h5 class="mui-content-padded">按压力度设定</h5>
		<div style="margin: 10px;background-color: white">
			<h5>按压力度：<span id='rangeValue'>50</span></h5>
			<div class="mui-input-row mui-input-range">
				<input type="range" min="0" max="100" id="myRange">
			</div>
		</div>
	</body>

	<script src="../js/mui.min.js"></script>
	<script src="../js/device.js"></script>
	<script src="../js/dsbridge.js"> </script>
	<script type="text/javascript" src="https://unpkg.com/@dcloudio/uni-webview-js@0.0.3/index.js"></script>
	<script>
		function SPP_sendAT(message) {
			console.log(message);
			//异步调用
			uni.postMessage({
				data: {
					instructions: message, // 这是传的参数
				},
			});
			
			uni.postMessage({
				data: {
					editStore: {
						idx: 8,
						idxName: 'CI',
						data: [localStorage.getItem('CI_L_modle'), localStorage.getItem('CI_R_modle'), localStorage.getItem('CI_modle_detial')],
					}, // 这是传的参数
				},
			});
			
			// if (checkIsAppleDevice()) {
			// 	dsBridge.call("SPP.sendAT", message)
			// } else if (checkIsHarmonyOS()) {
			// 	SPP.sendAT(message);
			// } else {
			// 	SPP.sendAT(message);
			// }
		}
		

		function selectnoise_modle(modle) {
			switch (modle) {
				case "1":
					document.getElementById("noise-close").checked = true;
					break;
				case "2":
					document.getElementById("noise-down").checked = true;
					break;
				case "3":
					document.getElementById("noise-down").checked = true;
					document.getElementById("noise-close").checked = true;
					break;
				case "4":
					document.getElementById("noise-full").checked = true;
					break;
				case "5":
					document.getElementById("noise-close").checked = true;
					document.getElementById("noise-full").checked = true;
					break;
				case "6":
					document.getElementById("noise-down").checked = true;
					document.getElementById("noise-full").checked = true;
					break;
				case "7":
					document.getElementById("noise-down").checked = true;
					document.getElementById("noise-close").checked = true;
					document.getElementById("noise-full").checked = true;
					break;
			}
		};

		function ui_init() {
			console.log(localStorage.getItem('CI_L_modle'));
			if (localStorage.getItem('CI_L_modle') == "1") {
				document.getElementById("controlUI").style.display = "none";
				document.querySelector('.mui-table-view.mui-table-view-radio ' + "li:nth-child(" + 2 +
						')')
					.classList.add('mui-selected');
			} else {
				document.getElementById("controlUI").style.display = "";
				document.querySelector('.mui-table-view.mui-table-view-radio ' + "li:nth-child(" + 1 +
						')')
					.classList.add('mui-selected');
			}
			document.getElementById('rangeValue').innerText = localStorage.getItem('CJ_L_press')
			document.getElementById('myRange').value = localStorage.getItem('CJ_L_press')

			selectnoise_modle(localStorage.getItem('CI_modle_detial'))
		};



		function selectSET(eq_str) {
			switch (eq_str) {
				case "噪声控制":
					document.getElementById("controlUI").style.display = "";
					break;
				case "语音助手":
					document.getElementById("controlUI").style.display = "none";
					break;
			}

		};


		function sendselectnoise_modle() {
			if (document.getElementById("noise-full").checked && document.getElementById("noise-close").checked && document
				.getElementById("noise-down").checked) {
				localStorage.setItem('CI_modle_detial', "7")
			} else if (document.getElementById("noise-full").checked && document.getElementById("noise-down").checked) {
				localStorage.setItem('CI_modle_detial', "6")
			} else if (document.getElementById("noise-full").checked && document.getElementById("noise-close").checked) {
				localStorage.setItem('CI_modle_detial', "5")
			} else if (document.getElementById("noise-close").checked && document.getElementById("noise-down").checked) {
				localStorage.setItem('CI_modle_detial', "3")
			} else if (document.getElementById("noise-full").checked) {
				localStorage.setItem('CI_modle_detial', "4")
			} else if (document.getElementById("noise-close").checked) {
				localStorage.setItem('CI_modle_detial', "1")
			} else if (document.getElementById("noise-down").checked) {
				localStorage.setItem('CI_modle_detial', "2")
			}
			SPP_sendAT("AT+CI=" + localStorage.getItem('CI_L_modle') + "," + localStorage.getItem('CI_R_modle') +
				"," + localStorage.getItem('CI_modle_detial') +
				"\r\n");
		};
		mui.init({
			swipeBack: false, //启用右滑关闭功能
		});
		ui_init()
		// SPP_sendAT("AT+CB\r\n");
		document.getElementById('eatsettinglist').addEventListener('selected', function(e) {
			console.log("当前选中的文本值为:" + e.detail.el.innerText);
			if (e.detail.el.innerText == "噪声控制") {
				localStorage.setItem('CI_L_modle', 5);
				SPP_sendAT("AT+CI=" + localStorage.getItem('CI_L_modle') + "," + localStorage.getItem('CI_R_modle') +
					"," + localStorage.getItem('CI_modle_detial') +
					"\r\n");
			} else {
				localStorage.setItem('CI_L_modle', 1);
				SPP_sendAT("AT+CI=" + localStorage.getItem('CI_L_modle') + "," + localStorage.getItem('CI_R_modle') +
					"," + localStorage.getItem('CI_modle_detial') +
					"\r\n");
			}
			selectSET(e.detail.el.innerText)

		});
		// 获取复选框元素  
		document.getElementById('noise-down').addEventListener('change', function() {

			sendselectnoise_modle();
		});
		document.getElementById('noise-close').addEventListener('change', function() {

			sendselectnoise_modle();
		});
		document.getElementById('noise-full').addEventListener('change', function() {

			sendselectnoise_modle();
		});


		document.getElementById('myRange').addEventListener('change', function() {
			var rangeValue = document.getElementById('myRange').value;
			document.getElementById('rangeValue').innerText = rangeValue;
			localStorage.setItem('CJ_L_press', rangeValue);
			SPP_sendAT("AT+CJ=" + localStorage.getItem('CJ_L_press') + "," + localStorage.getItem('CJ_R_press') +
				"\r\n");
		});
	</script>
</html>