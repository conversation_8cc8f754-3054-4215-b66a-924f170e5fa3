<!DOCTYPE html>
<html>

	<head>
		<meta charset="utf-8">
		<title>Hello MUI</title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<!--标准mui.css-->
		<link rel="stylesheet" href="../css/mui.min.css">
		<!--App自定义的css-->
		<!--<link rel="stylesheet" type="text/css" href="../css/app.css"/>-->
		<style>
			.mui-row.mui-fullscreen>[class*="mui-col-"] {
				height: 100%;
			}

			.title {
				margin: 20px 15px 10px;
				color: #6d6d72;
				font-size: 15px;
			}

			#head {
				line-height: 20px;
			}

			.head-img {
				width: 32px;
				height: 32px;
				margin: 10px;
			}

			#head-img1 {
				position: absolute;
				bottom: 10px;
				right: 40px;
				width: 40px;
				height: 40px;
			}

			.code {
				word-wrap: break-word;
				word-break: normal;
				font-size: 90%;
				color: #c7254e;
				background-color: #f9f2f4;
				border-radius: 4px;
			}
		</style>

	</head>
	<body>
		<header class="mui-bar mui-bar-nav">
			<a class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></a>
			<h1 class="mui-title">更新固件</h1>
		</header>
		<div class="mui-content">
			<div class="mui-card">
				<div class="mui-card-content">
					<ul class="mui-table-view">
						<li class="mui-table-view-cell mui-media" style="display: flex;">
							<div class="mui-row">
								<div class="mui-col-xs-4">
									<img class="mui-pull-left head-img"
										src="../image/mipmap-xxhdpi/ota_app_version_icon.png">
								</div>
								<div class="mui-col-xs-4 ">
									<span>云检测</span>
								</div>
								<button id="detectupdates" type="button"
									class="mui-btn mui-btn-primary mui-col-xs-8 ">检测更新</button>

							</div>
						</li>
						<li class="mui-table-view-cell">

							<div class="mui-row">
								<div class="mui-col-xs-2 ">
									<img class="mui-pull-left head-img"
										src="../image/mipmap-xxhdpi/ota_version_icon.png">
								</div>
								<div class="mui-col-xs-10 ">
									<div class="mui-card-content-inner">
										<h4>当前耳机信息</h4>
										<div class="mui-content-padded mytb">
											<div class="mui-row">
												<div class="mui-col-xs-10 ">
													<p>左耳固件版本</p>
												</div>
												<div class="mui-col-xs-2" style="r">
													V<label id="earl_ver">0</label>
												</div>
											</div>
											<div class="mui-row">
												<div class="mui-col-xs-10 ">
													<p>右耳固件版本</p>
												</div>
												<div class="mui-col-xs-2" style="r">
													V<label id="earr_ver">0</label>
												</div>
											</div>
											<div class="mui-row">
												<div class="mui-col-xs-10 ">
													<p>左耳电量</p>
												</div>
												<div class="mui-col-xs-2" style="r">
													<label id="earlv">0</label>%
												</div>
											</div>
											<div class="mui-row">
												<div class="mui-col-xs-10 ">
													<p>右耳电量</p>
												</div>
												<div class="mui-col-xs-2" style="r">
													<label id="earrv">0</label>%
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>

						</li>
					</ul>
				</div>
			</div>

			<div class="mui-content">
				<div class="mui-card">
					<div class="mui-card-content">
						<ul class="mui-table-view">
							<li class="mui-table-view-cell">
								<img class="mui-pull-left head-img"
									src="../image/mipmap-xxhdpi/ota_update_status_icon.png">
								状态信息
								<div>
									<span>
										<p>升级状态</p>
									</span>
									<div id="ota_progress" class="mui-progressbar">
										<span></span>
									</div>
								</div>
							</li>
						</ul>
					</div>
				</div>
				<h4 class="mui-content-padded">耳机升级</h4>
				<div class="mui-card">
					<div class="mui-row">
						<div class="mui-col-xs-2 ">
							<img class="mui-pull-left head-img" src="../image/mipmap-xxhdpi/ota_notice_icon.png">
						</div>
						<div class="mui-col-xs-10 ">
							<div class="mui-card-content-inner">
								<h4>升级注意事项</h4>
								<p>1.请将两只耳机与手机保持连接状态</p>
								<p> 2.请务必将两只耳机都取出仓外放置进行升级，升级途中请勿退出APP</p>
								<p>3.确保两只耳机的电量大于20%</p>
								<p>4.升级完成后，务必将两只耳机放回仓内并合盖，待仓灯灭后重新取出耳机即可体验最新固件</p>
							</div>
						</div>
					</div>


				</div>
				<code id="response"></code>
			</div>




	</body>
	<script src="../js/mui.min.js"></script>
	<script>
		function SPP_sendAT(message) {
			console.log(message);
			SPP.sendAT(message);
		}
		mui.init({
			swipeBack: false, //启用右滑关闭功能

		});
		SPP_sendAT("AT+CB\r\n");

		function SPPReceive(data) {
			console.log(data);
			var arry = data.split('=');
			console.log(arry[1]);
			switch (arry[0]) {
				case "CC":
					var v = arry[1].split(',');
					var bt_lv = v[0];
					var bt_rv = v[1];
					if (bt_lv >= 128) {
						document.getElementById("earlv").textContent = bt_lv - 128;
					} else {
						document.getElementById("earlv").textContent = bt_lv;
					}
					if (bt_rv >= 128) {
						document.getElementById("earrv").textContent = bt_rv - 128;
					} else {
						document.getElementById("earrv").textContent = bt_rv;
					}
					break;
				case "VER":
					//Q4_ZJ_A19_M01A_EQ0_156_multilink
					var v = arry[1].split('_');
					ver = arry[1];
					document.getElementById("earl_ver").textContent = v[5];
					document.getElementById("earr_ver").textContent = v[5];
					version = v[5];
					break;
			}

		};
		var remark = "版本信息";
		var btnArray = ['否', '是'];

		function updateOTA() {
			mui.confirm((remark), '固件下载完成是否更新？', btnArray, function(e) {
				if (e.index == 1) {
					SPP.ota_binupdate();
				} else {

				}
			})
		};
		mui("#demo1").progressbar({
			progress: 20
		}).show();

		//发送请求按钮的点击事件
		document.getElementById("detectupdates").addEventListener('tap', function() {

			if (SPP.otastart() == "OK") {
				remark = SPP.ota_getremark();
				mui.confirm('有新的安装包请确认是否下载？', '固件升级', btnArray, function(e) {
					if (e.index == 1) {
						if (SPP.otadownloadfile() == "OK") {

							updateOTA();
						}
					} else {

					}
				})
			}

		});
	</script>


</html>