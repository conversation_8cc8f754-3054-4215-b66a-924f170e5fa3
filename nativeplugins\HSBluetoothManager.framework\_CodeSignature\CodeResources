<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/HSBluetoothManager.h</key>
		<data>
		sBmdMTfRqZ1D+ahzF+ax8+u+bY8=
		</data>
		<key>Headers/HSCBPCenterManager.h</key>
		<data>
		dvG3IqG750xMuMuZLsshq3jQTNo=
		</data>
		<key>Info.plist</key>
		<data>
		vtk68GQGMvkSUjY6SXEkiT+s1tw=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		kg5pQGC7pJTAVIqJn3wK7D/pYcI=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/HSBluetoothManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			sBmdMTfRqZ1D+ahzF+ax8+u+bY8=
			</data>
			<key>hash2</key>
			<data>
			LXeHyDYwHJ9dJl4C7itB6yswf+mfYv8G1hP7q6/tOf0=
			</data>
		</dict>
		<key>Headers/HSCBPCenterManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			dvG3IqG750xMuMuZLsshq3jQTNo=
			</data>
			<key>hash2</key>
			<data>
			kL/RNS8HElTYT5U9lVYhNVwDrB0a82HZVYOufOUYFMc=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			kg5pQGC7pJTAVIqJn3wK7D/pYcI=
			</data>
			<key>hash2</key>
			<data>
			92dK7OV/s/Vhni5rGsUOIxxTj8uAjSdOrIT6ZFnqS50=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
