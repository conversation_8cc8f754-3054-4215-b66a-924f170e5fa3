/**
 * 评论相关API
 */

import { http, Method } from "@/utils/request.js";

/**
 * 获取评论分页数据
 * @param params 查询参数
 */
export function getCommentPage(params) {
  return http.request({
    url: '/comments/comments/page',
    method: Method.GET,
    params
  });
}

/**
 * 发表评论
 * @param data 评论数据
 */
export function addComment(data) {
  return http.request({
    url: '/comments/comments',
    method: Method.POST,
    data
  });
}

/**
 * 回复评论
 * @param data 回复数据
 */
export function replyComment(data) {
  return http.request({
    url: '/comments/comments/reply',
    method: Method.POST,
    data
  });
}

/**
 * 删除评论
 * @param id 评论ID
 */
export function deleteComment(id) {
  return http.request({
    url: `/comments/comments/${id}`,
    method: Method.DELETE
  });
}

/**
 * 点赞/取消点赞评论
 * @param id 评论ID
 * @param type 操作类型
 */
export function toggleLike(id, type) {
  return http.request({
    url: `/comments/comments/like/${id}`,
    method: Method.POST,
    params: { type }
  });
}

/**
 * 删除回复
 * @param id 回复ID
 */
export function deleteReply(id) {
  return http.request({
    url: `/comments/comments/reply/${id}`,
    method: Method.DELETE
  });
}