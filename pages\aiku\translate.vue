<template>
  <view class="translate-page">
    <!-- 主要内容区域 -->
    <view class="content">
      <!-- 翻译耳机介绍 -->
      <view class="intro-section">
        <view class="intro-title">轻松将你的耳机变成翻译耳机</view>
        <view class="intro-desc">
          你可以将自己的蓝牙耳机，通过蓝牙连上手机，再返回本界面，就可以变成翻译耳机！
        </view>
      </view>

      <!-- 功能模式选择 -->
      <view class="mode-section">
        <!-- 自由说模式 -->
        <view class="mode-item">
          <view class="mode-icon">
            <u-icon name="account" size="60" color="#4B7BEC"></u-icon>
          </view>
          <view class="mode-content">
            <text class="mode-title">自由说模式</text>
            <text class="mode-desc">两人各带一个耳机，实时翻译，释放双手，自由沟通</text>
          </view>
        </view>

        <!-- 外放模式 -->
        <view class="mode-item">
          <view class="mode-icon">
            <u-icon name="volume-up" size="60" color="#4B7BEC"></u-icon>
          </view>
          <view class="mode-content">
            <text class="mode-title">外放模式</text>
            <text class="mode-desc">一人佩戴耳机，一人拿手机，适合陌生人不方便佩戴您的耳机</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {}
  }
}
</script>

<style lang="scss" scoped>
.translate-page {
  min-height: 100vh;
  background-color: #f8f8f8;

  .content {
    padding: 30rpx;

    .intro-section {
      background: #E7F1FF;
      padding: 40rpx 30rpx;
      border-radius: 20rpx;
      margin-bottom: 40rpx;

      .intro-title {
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 20rpx;
        text-align: center;
      }

      .intro-desc {
        font-size: 28rpx;
        color: #666;
        line-height: 1.6;
        text-align: center;
      }
    }

    .mode-section {
      .mode-item {
        background: #fff;
        border-radius: 20rpx;
        padding: 30rpx;
        margin-bottom: 30rpx;
        display: flex;
        align-items: center;
        box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);

        .mode-icon {
          width: 120rpx;
          height: 120rpx;
          background: rgba(75,123,236,0.1);
          border-radius: 60rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 30rpx;
        }

        .mode-content {
          flex: 1;

          .mode-title {
            font-size: 32rpx;
            font-weight: bold;
            color: #333;
            margin-bottom: 12rpx;
            display: block;
          }

          .mode-desc {
            font-size: 26rpx;
            color: #666;
            line-height: 1.4;
          }
        }

        &:active {
          transform: scale(0.98);
          transition: transform 0.2s;
        }
      }
    }
  }
}
</style>