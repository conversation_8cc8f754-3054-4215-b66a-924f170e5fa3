//
//  HSBluetoothManager.h
//  HSBluetoothManager
//
//  Created by 陈永盛 on 2024/3/26.
//

#import <Foundation/Foundation.h>

//! Project version number for HSBluetoothManager.
FOUNDATION_EXPORT double HSBluetoothManagerVersionNumber;

//! Project version string for HSBluetoothManager.
FOUNDATION_EXPORT const unsigned char HSBluetoothManagerVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <HSBluetoothManager/PublicHeader.h>


#import <HSBluetoothManager/HSCBPCenterManager.h>
