<script>
	/**
	 * vuex管理登录状态，具体可以参考官方登录模板示例
	 */
	import config from "@/config/config";
import {
	getClipboardData
} from "@/js_sdk/h5-copy/h5-copy.js";
import APPUpdate from "@/plugins/APPUpdate";
import storage from "@/utils/storage";
import {
	mapMutations
} from "vuex";
import { getPop } from "@/api/device.js";
import { getUnreadMessages } from "@/api/login.js";

	
	
	/**
	 * 路由监听并删除路由
	 * https://developers.weixin.qq.com/miniprogram/dev/api/route/wx.navigateTo.html
	 * */
	// #ifdef MP-WEIXIN
	wx.onAppRoute((res) => {
		
	})
	// #endif

	export default {
		data() {
			return {
				config,
			};
		},


		/**
		 * 监听返回
		 */
		onBackPress(e) {
			console.log("onBackPress-APP", e);
			if (e.from == "backbutton") {
				let routes = getCurrentPages();
				let curRoute = routes[routes.length - 1].options;
				routes.forEach((item) => {
					// if (
					// 	item.route == "pages/tabbar/cart/cartList" ||
					// 	item.route.indexOf("pages/product/goods") != -1
					// ) 
					if (item.route == "/pages/mine/cart/cartList" || 
					item.route.indexOf("pages/product/goods") != -1)
					{
						uni.redirectTo({
							url: item.route,
						});
					}
				});

				if (curRoute.addId) {
					uni.reLaunch({
						// url: "/pages/tabbar/cart/cartList",
						url: "/pages/mine/cart/cartList",
					});
				} else {
					uni.navigateBack();
				}
				return true; //阻止默认返回行为
			}
		},
		methods: {
			...mapMutations(["login"]),
		},
		onLaunch: function() {
			// #ifdef APP-PLUS
			this.checkArguments(); // 检测启动参数
			// this.checkLoginAndOpenPage();
			APPUpdate();

			// 重点是以下： 一定要监听后台恢复 ！一定要
			plus.globalEvent.addEventListener("newintent", (e) => {
				this.checkArguments(); // 检测启动参数
				this.checkLoginAndOpenPage();
			});
			// #endif

			// #ifdef MP-WEIXIN
			this.applyUpdateWeChat();
			// #endif
			
			// 自定义首屏页面
			/* if (this.$options.filters.isLogin("auth")) {
				uni.reLaunch({
					url: '/pages/tabbar/device/index',
					success() {
						plus.navigator.closeSplashscreen();
					}
				})
			} else {
				uni.reLaunch({
					url: '/pages/passport/login',
					success() {
						plus.navigator.closeSplashscreen();
					}
				})
			} */
			this.initUnreadMes();
			this.initJPMessage();
			this.handleJpush();
		},

		onShow() {
			// #ifndef H5
			if(this.config.enableGetClipboard){
				this.getClipboard();
			}
			// #endif
			
			let isAgree = true;
			// #ifdef APP-PLUS

			if (storage.getShow()) {
				if (uni.getSystemInfoSync().platform == 'ios') {
					isAgree = false;
					this.$u.route("/pages/tabbar/screen/fullScreen");

				}
			}
			// #endif
			
			if (isAgree) {
				this.checkLoginAndOpenPage();
			}
		},
		methods: {
			/**
			 * 微信小程序版本提交更新版本 解决缓存问题
			 */
			applyUpdateWeChat() {
				const updateManager = uni.getUpdateManager();

				updateManager.onCheckForUpdate(function(res) {
					// 请求完新版本信息的回调
				});

				updateManager.onUpdateReady(function(res) {
					uni.showModal({
						title: "更新提示",
						content: "发现新版本，是否重启应用？",
						success(res) {
							if (res.confirm) {
								// 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
								updateManager.applyUpdate();
							}
						},
					});
				});
				updateManager.onUpdateFailed(function(res) {
					// 新的版本下载失败
				});
			},

			//  TODO 开屏广告 后续优化添加
			launch() {
				try {
					// 获取本地存储中launchFlag标识 开屏广告
					const value = uni.getStorageSync("launchFlag");
					if (!value) {
						// this.$u.route("/pages/index/agreement");
					} else {
						//app启动时打开启动广告页
						var w = plus.webview.open(
							"/hybrid/html/advertise/advertise.html",
							"本地地址", {
								top: 0,
								bottom: 0,
								zindex: 999,
							},
							"fade-in",
							500
						);
						//设置定时器，4s后关闭启动广告页
						setTimeout(function() {
							plus.webview.close(w);
							APPUpdate();
						}, 3000);
					}
				} catch (e) {
					// error
					uni.setStorage({
						key: "launchFlag",
						data: true,
						success: function() {
							console.log("error时存储launchFlag");
						},
					});
				}
			},

			/**
			 * 获取粘贴板数据
			 */
			async getClipboard() {
				let res = await getClipboardData();

				/**
				 * 解析粘贴板数据
				 */

				if (res.indexOf(config.shareLink) != -1 && (res != this.$store.state.shareLink)) {
					this.$store.state.shareLink = res
					uni.showModal({
						title: "提示",
						content: "检测到一个分享链接是否跳转？",
						confirmText: "跳转",
						success: function(callback) {
							if (callback.confirm) {
								const path = res.split(config.shareLink)[1];
								if (path.indexOf("tabbar") != -1) {
									uni.switchTab({
										url: path,
									});
								} else {
									uni.navigateTo({
										url: path,
									});
								}
							}
						},
					});
				}
			},

			/**
			 * h5中打开app获取跳转app的链接并跳转
			 */
			checkArguments() {
				// #ifdef APP-PLUS
				setTimeout(() => {
					const args = plus.runtime.arguments;
					if (args) {
						const argsStr = decodeURIComponent(args);
						const path = argsStr.split("//")[1];
						uni.setStorageSync("sharePageUrl", path);
						/* if (path.indexOf("tabbar") != -1) {
							uni.switchTab({
								url: `/${path}`,
							});
						} else {
							uni.navigateTo({
								url: `/${path}`,
							});
						} */
					}
				});
				// #endif
			},
			checkLoginAndOpenPage() {
				let accessToken = storage.getAccessToken();
				let refreshToken = storage.getRefreshToken();
				
				if (!accessToken || !refreshToken) {
					// uni.redirectTo({
					// 	url: '/pages/passport/login'
					// })
					this.$options.filters.navigateToLogin("redirectTo");
				} else {
					getPop().then((res) => {
						console.log(res);
						if (res.data.code == 0) {
							let sharePageUrl = uni.getStorageSync("sharePageUrl");
							if (sharePageUrl) {
								uni.removeStorageSync("sharePageUrl");
								if (sharePageUrl.indexOf("tabbar") != -1) {
									uni.switchTab({
										url: `/${sharePageUrl}`,
									});
								} else {
									uni.navigateTo({
										url: `/${sharePageUrl}`,
									});
								}
							}
						}
					});
				}
			},
			//初始化极光推送
		  initJPMessage() {
			var jpushModule = uni.requireNativePlugin("JG-JPush");
			jpushModule.initJPushService();
			jpushModule.setLoggerEnable(true);
			jpushModule.addConnectEventListener(result => {
			  let connectEnable = result.connectEnable
			  uni.$emit('connectStatusChange', connectEnable)
			});
	
			//获取注册id
			jpushModule.getRegistrationID((e) => {
			  console.log("getRegistrationID", e)
			  if (e.code == 0 && e.registerID) {
				  uni.setStorageSync("registration_id", e.registerID);
			  }
			})
			
			//#ifdef APP-PLUS
			// 更新应用图标上的角标
			plus.runtime.setBadgeNumber(0);
		    jpushModule.setBadge(0);
			//#endif
		  },
		  handleJpush() {
			  var jpushModule = uni.requireNativePlugin("JG-JPush");
	  
			  jpushModule.addNotificationListener(result => {
				let notificationEventType = result.notificationEventType
				let messageID = result.messageID
				let title = result.title
				let content = result.content
				let extras = result.extras
				
				console.log('addNotificationListener,返回值：', result);
	  
				/**
				 * @description 根据消息的类型决定后续操作，仅从通知栏消息跳转进app这种类型，做页面分发跳转操作
				 * 
				 * @param {string} notificationEventType  notificationArrived（应用在前台时收到消息）；notificationOpened（通知栏消息跳转）
				 * 
				 */
				if (notificationEventType === 'notificationOpened') {
					this.handleJpushJump(extras);
				} else if (extras && extras.type) {
					this.updateLocalMsg(extras);
					this.$options.filters.updateTabbarBadge();
					this.updateMsgNum();
				}
			  });
			},
			handleJpushJump(extras) {
				let url = extras.url;
				if (url) {
					uni.setStorageSync("sharePageUrl", url);
					this.checkLoginAndOpenPage();
				}
			},
			updateMsgNum() {
				let waitPay = uni.getStorageSync("ORDER_WAIT_PAY") || [];
				let waitShip = uni.getStorageSync("ORDER_WAIT_SHIP") || [];
				let waitEv = uni.getStorageSync("ORDER_WAIT_EVALUATE") || [];
				let waitAfter = uni.getStorageSync("ORDER_WAIT_AFTER_SALES") || [];
				
				let imMsg = uni.getStorageSync("IM_NEW_MESSAGE") || [];
				let mailMsg = uni.getStorageSync("MAIL_NEW_MESSAGE") || [];
				
				this.$store.state.waitPay = new Set(waitPay).size;
				this.$store.state.waitShip = new Set(waitShip).size;
				this.$store.state.waitEv = new Set(waitEv).size;
				this.$store.state.waitAfter = new Set(waitAfter).size;
				
				this.$store.state.imMsg = new Set(imMsg).size;
				this.$store.state.mailMsg = new Set(mailMsg).size;
			},
			initUnreadMes() {
				let that = this;
				getUnreadMessages().then(res => {
					if (res.data.success) {
						let msgList = res.data.result;
						if (msgList && msgList.length > 0) {
							for (let i = 0; i < msgList.length; i++) {
								that.updateLocalMsg(msgList[i]);
							}
						}
					}
					that.$options.filters.updateTabbarBadge();
					that.updateMsgNum();
				});
			},
			updateLocalMsg(extras) {
				if (!extras || !extras.type) { return; }
				let msg = uni.getStorageSync(extras.type) || [];
				let msgSet = new Set(msg);
				msgSet.add(extras.id);
				uni.setStorageSync(extras.type, Array.from(msgSet));
			}
		},
	};
</script>

<style lang="scss">
	@import "uview-ui/index.scss";

	// -------适配底部安全区  苹果x系列刘海屏

	// #ifdef MP-WEIXIN
	.mp-iphonex-bottom {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		box-sizing: content-box;
		height: auto !important;
		padding-top: 10rpx;
	}

	// #endif

	body {
		background-color: $bg-color;
	}

	/************************ */
	.w200 {
		width: 200rpx !important;
	}

	.flex1 {
		flex: 1; //必须父级设置flex
	}
</style>
