<template>
	<view>
		<view class="select">
			<view :class="index == selindex ? 'selbox sel' : ''" v-for="(item, index) in sellist" :key="index" @click="selcli(item.app, index)">
				{{ item.name }}
			</view>
		</view>
			<view class="whole" v-if="selindex == 0&&false" @tap="showDropdown" :class="border == true ? 'borderradio' : ''">
				<!-- <image src="../../static/whole.svg" mode=""></image> -->
				{{ selectedItem }}
				<image src="../../../static/down.svg" mode=""></image>
			</view>
			<view class="boslist">
				<view class="box" @click="go(item)" v-for="(item, index) in tabindexlist" :key="index" v-if="item.status == 0">
					<view class="title">{{ item.appName }}</view>
					<image :src="item.appIcon" mode=""></image>
				</view>
			</view>
	</view>
</template>

<script>
	import { getDictList, getAppsByRemarkAndHeadphoneType,
	getAppsByRemark, getPop, miniConfig } from "@/api/device.js";
	export default {
		data() {
			return {
					selindex: '0',
					sellist: [
						{
							name: '蓝牙设备',
							app: 'app_ble'
						},
						{
							name: '智汇之城',
							app: 'app_zhcc'
						},
						{
							name: '天空之城',
							app: 'app_bbs'
						}
					],
					border: true,
					showList: false, // 控制下拉列表的显示和隐藏
					selectedItem: '所有设备',
					tabindexlist: [],
			}
		},
		onShow() {
			//判断是否可重连
			if (this.connStatus === 1) {
				this.firstlink();
			}
			// this.getPop();
			this.applist(this.sellist[this.selindex].app);
			// this.tablist(4);
		},
		onPullDownRefresh() {
			alert("aaaaaaaaaaa")
			// this.getPop();
			this.applist(this.sellist[this.selindex].app);
			// this.tablist(4);
			uni.stopPullDownRefresh();
		},
		methods: {
			selcli(app, index) {
				this.selindex = index;
				this.applist(app);
				this.showList = false; // 隐藏下拉列表
			},
			showDropdown() {
				this.showList = !this.showList; // 切换下拉列表的显示状态
				this.border = !this.border; //控制下拉列表圆角
			},
			selectItem(item) {
				//选择蓝牙设备下的列表
				this.selectedItem = item.dictLabel; // 选择下拉列表项
				console.log(item);
				this.tablist(item.dictSort);
				this.showList = false; // 隐藏下拉列表
				this.border = true; //显示圆角
			},
			go(item) {
				console.log(item.appPlatform, item.appStartActivity);
				if (item.appPlatform == 'H5') {
					// uni.navigateTo({
					// 	url: '/pages/webview/webview?src=' + item.appStartActivity
					// })
					item.appStartActivity =
						item.appStartActivity == 'http://47.116.77.23/ows/'
							? '/hybrid/html/ows/index.html'
							: item.appStartActivity == 'http://47.116.77.23/tws/'
							? '/hybrid/html/hs/index.html'
							: item.appStartActivity;
					let srcZero = item.appStartActivity.split('/');
					if (srcZero[1] == 'hybrid') {
						// for (let i = 0; i < this.queryInstructionsArr.length; i++) {
						// 	setTimeout(() => {
						// 		this.writeBLE(this.queryInstructionsArr[i]);
						// 	}, 2000)
						// }
						
						this.writeBLE("AT+CB\r\n")
						uni.showLoading({
							title: '正在获取设备状态...',
							mask: true
						});
						
						setTimeout(() => {
							uni.navigateTo({
								url: '/pages/webview/webview?appStartActivity=' + JSON.stringify(item)
							});
							console.log(item);
						}, 500);
						uni.hideLoading();
					}else {
						uni.navigateTo({
							url: '/pages/webview/webview?appStartActivity=' + JSON.stringify(item) + '&notBle=' + true
						});
					}
					// return
					
				} else {
					uni.getSystemInfo({
						success: function (res) {
							console.log(res.osName, '系统信息');
							console.log(res, '系统信息1');
							if (res.osName == 'android') {
								uni.showLoading({
									title: '下载中'
								});
								uni.downloadFile({
									url: item.appDownloadUrl, //仅为示例，并非真实的资源
									success: (res) => {
										console.log(res, '下载成功');
										if (res.statusCode === 200) {
											console.log('下载成功');
											uni.hideLoading();
											uni.showToast({
												title: '下载成功',
												icon: 'success'
											});
											uni.saveFile({
												tempFilePath: res.tempFilePath,
												success: function (res) {
													uni.openDocument({
														filePath: res.savedFilePath,
														success: function (res) {
															console.log(res, '打开安装包');
														}
													});
												},
												fail: (err) => {
													console.log(err, '打开安装包-失败');
												}
											});
										}
									},
									fail: (err) => {
										console.log(err, '下载失败');
										uni.hideLoading();
										uni.showToast({
											title: '下载失败,请检查网络',
											icon: 'none',
											duration: 1500
										});
									}
								});
							} else {
								uni.showToast({
									title: '当前手机系统不支持',
									icon: 'none',
									mask: true
								});
							}
						}
					});
					// uni.navigateTo({
					// 	url:'/pages/webview/webview?src='+item.appDownloadUrl
					// })
				}
				// // #ifdef APP-PLUS
				//     plus.runtime.openURL(video);
				//     // #endif
			},
			//循环排序
			forlist(data) {
				console.log(data);
				data.sort((a, b) => parseInt(a.orderNo) - parseInt(b.orderNo));
				this.tabindexlist = data;
				console.log(data);
			},
			//渲染对应列表
			applist(index) {
				getAppsByRemark({
						remark: index
					}).then((res) => {
						this.forlist(res.data.data);
					});
			},
			
			writeBLE(data) {
				let that = this;
				if (!that.readcharacteristicId) {
					return;
				}
				// const buffer = [0x41, 0x54, 0x2b, 0x43, 0x45, 0x3d, 0x3c, 0x32, 0x32, 0x0d, 0x0a];
				console.log('that.bluetoothObj.deviceId', that.bluetoothObj.deviceId);
				console.log('that.newServiceId', that.newServiceId);
				console.log('that.readcharacteristicId', that.readcharacteristicId);
				console.log('that.generate_command(data)', that.generate_command(data));
				uni.writeBLECharacteristicValue({
					deviceId: that.bluetoothObj.deviceId,
					serviceId: that.newServiceId,
					characteristicId: that.readcharacteristicId,
					value: that.generate_command(data),
					writeType: 'write',
					success: function (res) {
						console.log('已发送指令', JSON.stringify(res));
					},
					fail: function (res) {
						console.log('发送指令时报错：', JSON.stringify(res));
						uni.showToast({
							title: '发送失败，可能蓝牙目前没有连接',
							icon: 'none'
						});
					}
				});
			},
			
			writeBLETwo(data) {
				let that = this;
				if (!that.readcharacteristicId) {
					return;
				}
				// const buffer = [0x41, 0x54, 0x2b, 0x43, 0x45, 0x3d, 0x3c, 0x32, 0x32, 0x0d, 0x0a];
				console.log('that.bluetoothObj.deviceId', that.bluetoothObj.deviceId);
				console.log('that.newServiceId', that.newServiceId);
				console.log('that.readcharacteristicId', that.readcharacteristicId);
				// console.log('that.hex2ab(data)', that.hex2ab(data));
				uni.writeBLECharacteristicValue({
					deviceId: that.bluetoothObj.deviceId,
					serviceId: that.newServiceId,
					characteristicId: '43484152-2DAB-3241-6972-6F6861424C45',
					value: that.hex2ab(data),
					writeType: 'write',
					success: function (res) {
						console.log('已发送指令', JSON.stringify(res));
					},
					fail: function (res) {
						console.log('发送指令时报错：', JSON.stringify(res));
						uni.showToast({
							title: '发送失败，可能蓝牙目前没有连接',
							icon: 'none'
						});
					}
				});
			},
			
			writeBLEThree(data) {
				let that = this;
				if (!that.readcharacteristicId) {
					return;
				}
				// const buffer = [0x41, 0x54, 0x2b, 0x43, 0x45, 0x3d, 0x3c, 0x32, 0x32, 0x0d, 0x0a];
				let hexStrData = that.stringToHex(data);
				let bitNum = hexStrData.split(' ').length + 2;
				if (bitNum < 10) {
					bitNum = '0' + bitNum;
				}
				let result = '05 5A ' + bitNum + ' 00 01 38 ' + hexStrData;
				console.log('that.bluetoothObj.deviceId', that.bluetoothObj.deviceId);
				console.log('that.newServiceId', that.newServiceId);
				console.log('that.readcharacteristicId', that.readcharacteristicId);
				// console.log('that.hex2ab(data)', that.hex2ab(result));
				uni.writeBLECharacteristicValue({
					deviceId: that.bluetoothObj.deviceId,
					serviceId: that.newServiceId,
					characteristicId: '43484152-2DAB-3241-6972-6F6861424C45',
					value: that.hex2ab(result),
					writeType: 'write',
					success: function (res) {
						console.log('已发送指令', JSON.stringify(res));
					},
					fail: function (res) {
						console.log('发送指令时报错：', JSON.stringify(res));
						uni.showToast({
							title: '发送失败，可能蓝牙目前没有连接',
							icon: 'none'
						});
					}
				});
			},
			
		},
		onReachBottom() {
		},
		
	}
</script>

<style lang="scss" scoped>

		.select {
			width: 700rpx;
			height: 60rpx;
			display: flex;
			font-size: 30rpx;
			font-weight: 600;
			letter-spacing: 5rpx;
			flex-direction: row;
			justify-content: space-evenly;
			align-items: center;
			margin: 0 auto;
			color: #2b8ffc;
			position: fixed;
			top: 0;
			background-color: #f9f9f9;
			.sel {
				// padding: 5rpx 40rpx;
				padding: 8rpx 24rpx;
			}

			.selbox {
				background-color: #2b8ffc;
				color: #fff;
				border-radius: 8rpx;
			}

			// background-color: aqua;
		}

		.whole {
			width: 300rpx;
			padding: 20rpx;
			font-size: 40rpx;
			font-weight: 600;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: flex-start;
			position: relative;

			image {
				width: 40rpx;
				height: 40rpx;
			}
		}

		.boslist {
			width: 700rpx;
			// margin: 0 auto;
			padding:60rpx 0 20rpx;
			display: flex;
			flex-direction: row;
			flex-wrap: wrap;
			align-items: center;
			justify-content: flex-start;

			.box {
				width: 280rpx;
				height: 250rpx;
				padding: 20rpx;
				display: flex;
				flex-direction: column;
				justify-content: space-around;
				align-items: center;
				margin-top: 30rpx;
				margin-left: 50rpx;
				border: 1rpx solid #c0c0c0;
				border-radius: 20rpx;

				image {
					width: 150rpx;
					height: 150rpx;
				}

				.title {
					width: 240rpx;
					font-size: 30rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					text-align: center;
				}
			}
		}
	
</style>
