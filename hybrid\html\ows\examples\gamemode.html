<!DOCTYPE html>
<html>

	<head>
		<meta charset="utf-8">
		<title>Hello MUI</title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">

		<!--标准mui.css-->
		<link rel="stylesheet" href="../css/mui.min.css">
		<!--App自定义的css-->
		<!--<link rel="stylesheet" type="text/css" href="../css/app.css"/>-->
		<style>
			.mui-row.mui-fullscreen>[class*="mui-col-"] {
				height: 100%;
			}

			#head {
				line-height: 20px;
			}

			.head-img {
				width: 32px;
				height: 32px;
				margin: 10px;
			}

			#head-img1 {
				position: absolute;
				bottom: 10px;
				right: 40px;
				width: 40px;
				height: 40px;
			}

			.mui-backdrop {
				position: fixed;
				top: 0;
				right: 0;
				bottom: 0;
				left: 0;
				z-index: 998;
				background-color: rgba(0, 0, 0, .3);
			}

			.container {

				display: flex;
				/* 将容器设置为弹性盒子 */
				justify-content: center;
				/* 将弹性盒子内的内容水平居中 */
				color: white;
				margin: 10px;

			}

			.switchtext {

				display: flex;
				/* 将容器设置为弹性盒子 */
				justify-content: center;
				/* 将弹性盒子内的内容水平居中 */
				color: white;
				margin: 10px;
				background-color: #6d6d72;
				border-radius: 20px;

			}
		</style>

	</head>
	<body style=" background-color: black;">
		<header class="mui-bar mui-bar-nav" style=" background-color: black;">
			<a class="mui-action-back mui-icon mui-icon-left-nav mui-pull-left"></a>
			<h1 class="mui-title " style="color: white;">游戏模式</h1>
		</header>
		<div class="mui-content mui-content-padded" style=" background-color: black;">

			<div style="
				border: 2px  dashed white;
				border-radius:20px;">
				<div class=" container">
					<img src="../image/gaming_mode.png" />
				</div>
				<div class=" container">
					<h5>提示：开启游戏模式后能有效的降低声音的延时</h4>
				</div>
			</div>
		</div>
		<div id="gamemode" class="switchtext">
			游戏模式
			<div id="gamemode_switch" class="mui-switch mui-switch-mini  mui-switch-blue ">
				<div class="mui-switch-handle"></div>
			</div>
		</div>

	</body>
	<script src="../js/mui.min.js"></script>
	<script>
		mui.init({
			swipeBack: false, //启用右滑关闭功能

		});

		function SPP_sendAT(message) {
			console.log(message);
			SPP.sendAT(message);
		}
		SPP_sendAT("AT+CK\r\n");


		function SPPReceive(data) {
			console.log(data);
			var arry = data.split('=');
			console.log(arry[1]);
			switch (arry[0]) {

				case "CK":
					if (arry[1] == "0") {
						// mui("#gamemode").switch().toggle();
					} else {
						mui("#gamemode_switch").switch().toggle();
					}
					break;
			}

		};
		document.getElementById("gamemode").addEventListener("toggle", function(event) {
			if (event.detail.isActive) {
				// console.log("AT+CK=1\r\n");
				SPP_sendAT("AT+CK=1\r\n");
			} else {
				// console.log("AT+CK=0\r\n");
				SPP_sendAT("AT+CK=0\r\n");
			}
		});
	</script>
</html>