<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1">
		<title>平台协议</title>
	</head>
	<body>
		<div id="app"></div>
	</body>
	<script type="text/javascript" src="./js/jquery-3.7.1.min.js"></script>
	<script type="text/javascript">
		function GetQueryString(name) {
		    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
		    var r = window.location.search.substr(1).match(reg); //获取url中"?"符后的字符串并正则匹配
		    var context = "";
		    if (r != null)
		        context = decodeURIComponent(r[2]);
		    reg = null;
		    r = null;
		    return context == null || context == "" || context == "undefined" ? "" : context;
		}
		
		$(function() {
			let type = GetQueryString("type");
			
			if (!type) { 
				console.log('传递过来的协议type不可为空');
				return;
		    } 
			
			$.get(`https://mall-app.huashangjishu.com/buyer/other/article/type/${type}`, function(res) {
				if (res.code == 200) {
					$("#app").html(res.result.content);
					$("title").text(res.result.title);
				}
			});
		});
		
	</script>
</html>