<template>
	<view class="root-container">
		<!-- 顶部导航栏 -->
		<view class="nav-bar">
			<view class="nav-content">
				<view class="back-btn" @click="goBack">
					<u-icon name="arrow-left" color="#4B7BEC" size="36"></u-icon>
				</view>
				<view class="nav-title">
					<text>小叶同学</text>
					<view class="online-indicator">
						<view class="dot"></view>
						<text>在线</text>
					</view>
				</view>
				<view class="nav-right">
					<u-icon name="plus" color="#4B7BEC" size="40" @click="clearMessages"></u-icon>
				</view>
			</view>
		</view>

		<!-- 聊天消息区域 -->
		<scroll-view id="chat-scroll-view" class="chat-container" scroll-y :scroll-with-animation="true"
			:scroll-top="scrollTop" :enable-back-to-top="false" :enhanced="false" :bounces="false"
			:show-scrollbar="false" @scrolltoupper="loadMoreMessages" @scroll="handleScroll" upper-threshold="200"
			lower-threshold="50">
			<!-- 欢迎消息和提示 -->
			<view class="welcome-tips" v-if="messages.length === 0">
				<image src="/static/aiku/ai-assistant.png" class="ai-avatar-large" mode="aspectFit"></image>
				<text class="welcome-title">我是小叶同学</text>
				<text class="welcome-desc">您的智能AI助手，我可以回答问题、提供建议，陪您聊天</text>

				<!-- 推荐问题 -->
				<view class="suggested-questions">
					<view class="question-item" v-for="(item, index) in suggestedQuestions" :key="index"
						@click="askSuggestedQuestion(item)">
						<text>{{item}}</text>
					</view>
				</view>
			</view>

			<!-- 消息列表 -->
			<block v-for="(item, index) in messages" :key="index">
				<!-- 时间分割线 -->
				<view class="time-divider" v-if="showTimeDivider(item, index)">
					<text>{{ formatMessageTime(item.timestamp) }}</text>
				</view>

				<!-- 用户消息 -->
				<view class="message-item user-message" v-if="item.role === 'user'">
					<view class="avatar-container">
						<image :src="userInfo.face || userImage" class="avatar" mode="aspectFill"></image>
					</view>
					<view class="message-content">
						<text>{{ item.content }}</text>
					</view>
				</view>

				<!-- AI消息 -->
				<view class="message-item ai-message" v-else>
					<view class="avatar-container">
						<image src="/static/aiku/ai-assistant.png" class="avatar" mode="aspectFill"></image>
					</view>
					<view class="message-content">
						<view>
							<text selectable>{{ item.content }}</text>
						</view>

						<view class="typing-indicator" v-if="item.isTyping">
							<view class="typing-dot"></view>
							<view class="typing-dot"></view>
							<view class="typing-dot"></view>
						</view>
					</view>
				</view>
			</block>

			<!-- 底部滚动锚点 - 增加高度确保可见 -->
			<view id="chat-bottom" class="bottom-anchor"></view>
		</scroll-view>

		<!-- 底部输入区域 -->
		<view class="input-container">
			<!-- 语音/文字输入切换按钮 -->
			<view class="input-type-btn" @click="toggleInputMode">
				<u-icon :name="isVoiceMode ? 'list-dot' : 'mic'" size="46" color="#4B7BEC"></u-icon>
			</view>

			<!-- 文本输入框 -->
			<view class="input-box" v-if="!isVoiceMode">
				<textarea class="input-textarea" v-model="inputText" :disabled="isSending" auto-height :maxlength="-1"
					:cursor-spacing="20" @confirm="sendMessage" :placeholder="isSending ? '发送中...' : '请输入问题...'"
					confirm-type="send" />
			</view>

			<!-- 语音按钮 -->
			<view class="voice-btn" v-else @touchstart="startRecording" @touchend="stopRecording"
				@touchcancel="cancelRecording">
				<text>{{ recording ? '松开发送' : '按住说话' }}</text>
			</view>

			<!-- 发送按钮 -->
			<view class="send-btn" :class="{'send-btn-active': canSend}" @click="sendMessage">
				<u-icon name="arrow-upward" color="#FFFFFF" size="48"></u-icon>
			</view>
		</view>

		<!-- 录音提示层 -->
		<view class="recording-mask" v-if="recording">
			<view class="recording-indicator">
				<view class="recording-wave" :class="{'recording-wave-active': recordingVolume > 0}">
					<view class="wave-item" v-for="i in 5" :key="i" :style="{height: 10 + recordingVolume * 2 + 'rpx'}">
					</view>
				</view>
				<text>{{ recordingTime }}s</text>
				<text class="cancel-text">上滑取消</text>
			</view>
		</view>
	</view>
</template>

<script>
	import configs from '@/config/config'
	import aiChatService from '@/api/aiChatService.js'

	export default {
		data() {
			return {
				// 页面数据
				configs,
				userImage: configs.defaultUserPhoto,
				userInfo: {},
				messages: [],
				inputText: '',

				// 打字效果控制
				isTyping: false, // 是否正在打字

				// 优化后的滚动控制
				scrollTop: 0,
				isNearBottom: true, // 用户是否在底部区域，是自动滚动的唯一依据

				// 加载历史消息控制
				_isLoadingHistory: false, // 是否正在加载历史消息

				// 其他状态
				isVoiceMode: false,
				recording: false,
				recordingTime: 0,
				recordingVolume: 0,
				recordTimer: null,
				volumeTimer: null,
				isSending: false,
				recorderManager: null,

				lastMessageLength: 0, // 用于跟踪AI消息的长度变化

				// 示例推荐问题
				suggestedQuestions: [
					'你能做什么？',
					'请讲个笑话',
					'今天天气怎么样？',
					'你是谁？'
				],

				// 测试数据（用于展示）
				testMessages: [{
						role: 'assistant',
						content: '你好！我是小叶同学，很高兴认识你。有什么我可以帮助你的吗？',
						timestamp: Date.now() - 24 * 60 * 60 * 1000 // 昨天
					},
					{
						role: 'user',
						content: '你好，我想了解一下你能做什么？',
						timestamp: Date.now() - 24 * 60 * 60 * 1000 + 1000
					},
					{
						role: 'assistant',
						content: '我可以帮助你回答问题、提供信息、进行日常对话、讲笑话、提供学习帮助等。你有什么具体需要吗？',
						timestamp: Date.now() - 24 * 60 * 60 * 1000 + 5000
					},
					{
						role: 'user',
						content: '给我讲个笑话吧',
						timestamp: Date.now() - 5 * 60 * 1000 // 5分钟前
					},
					{
						role: 'assistant',
						content: '好的，这是一个关于程序员的笑话：\n\n程序员去面试，面试官问："你有5年经验吗？"\n程序员："我有1年经验用了5年。"',
						timestamp: Date.now() - 5 * 60 * 1000 + 3000
					}
				]
			}
		},
		computed: {
			// 判断是否可以发送消息
			canSend() {
				return this.inputText.trim().length > 0 && !this.isSending;
			}
		},
		mounted() {
			this.userInfo = this.$options.filters.isLogin() || {};

			// 初始化AI聊天服务
			this.initAiChatService();

			// 初始化录音管理器
			this.initRecorderManager();

			this.scrollToBottom();
		},

		beforeDestroy() {
			if (this.recordTimer) clearInterval(this.recordTimer);
			if (this.volumeTimer) clearInterval(this.volumeTimer);
			// 关闭AI聊天服务连接
			aiChatService.closeConnection();
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 显示欢迎消息
			showWelcomeMessage() {
				// 如果消息列表为空，添加一条欢迎消息
				if (this.messages.length === 0) {
					// 生成唯一ID
					const welcomeId = 'welcome_' + Date.now();
					const welcomeMessage = {
						id: welcomeId,
						role: 'assistant',
						content: '你好！我是小叶同学，很高兴能与你交流。有什么我能帮你的吗？',
						timestamp: Date.now()
					};
					this.messages.push(welcomeMessage);
				}
			},

			// 清空对话记录
			clearMessages() {
				this.messages = [];
				aiChatService.clearHistory();
				uni.showToast({
					title: '对话已清空',
					icon: 'none'
				});
			},

			// 初始化AI聊天服务
			initAiChatService() {
				try {
					// 成功后显示欢迎消息
					if (this.messages.length === 0) {
						this.showWelcomeMessage();
					}
				} catch (error) {
					console.error('初始化AI聊天服务失败:', error);
					uni.showToast({
						title: '连接AI服务失败',
						icon: 'none'
					});
				}
			},

			// 发送消息
			sendMessage() {
				if (!this.canSend) return;

				const content = this.inputText.trim();
				this.inputText = '';
				this.isSending = true;

				// 1. 先将用户消息推入数组
				this.messages.push({
					role: 'user',
					content,
					timestamp: Date.now()
				});

				// 2. 立即调用一次滚动！确保用户消息能立刻被看到
				this.scrollToBottom();

				// 准备一个空的AI消息对象
				const assistantMessage = {
					role: 'assistant',
					content: '',
					isTyping: true,
					timestamp: Date.now()
				};
				this.messages.push(assistantMessage);

				// 关键：在发送前，重新设置回调，让它能捕获到最新的 assistantMessage
				this.handleAiStreamResponse();

				// 调用星火认知大模型API获取回复
				aiChatService.sendMessage(content)
					.catch(error => {
						console.error('AI回复出错:', error);
						const lastMsg = this.messages[this.messages.length - 1];
						if (lastMsg.role === 'assistant') {
							lastMsg.isTyping = false;
							lastMsg.content = '抱歉，我遇到了一些问题，请稍后再试。';
						}
						this.isSending = false;
					});
			},

			// 处理AI流式输出的打字效果 - 优化版
			handleAiStreamResponse() {
				this.isTyping = true;

				// 重置长度跟踪
				this.lastMessageLength = 0;

				// 配置星火AI聊天服务回调
				aiChatService.setCallbacks({
					onMessageStream: (content, isComplete) => {
						const lastMsg = this.messages[this.messages.length - 1];
						if (lastMsg && lastMsg.role === 'assistant') {
							lastMsg.content = content;
							this.scrollToBottom();
						}
					},
					onMessageComplete: (finalContent) => {
						console.log('AI回复完成:', finalContent.substring(0, 50) + '...');
						const lastMsg = this.messages[this.messages.length - 1];
						if (lastMsg && lastMsg.role === 'assistant') {
							lastMsg.content = finalContent;
							lastMsg.isTyping = false;
						}
						this.isSending = false;

						// 执行最后一次、确保的强制滚动
						this.$nextTick(() => {
							setTimeout(() => {
								this.scrollToBottom();
							}, 200);
						});
					},
					onError: (error) => {
						console.error('AI聊天服务错误:', error);
						const lastMsg = this.messages[this.messages.length - 1];
						if (lastMsg && lastMsg.role === 'assistant') {
							lastMsg.isTyping = false;
							lastMsg.content = '抱歉，我遇到了一些问题，请稍后再试。';
						}
						this.isSending = false;
						this.$nextTick(() => {
							setTimeout(() => {
								this.scrollToBottom();
							}, 200);
						});
					}
				});
			},

			// ==================== 豆包式滚动系统核心方法 ====================

			// 滚动事件处理，只用来更新 isNearBottom 状态
			handleScroll(e) {
				// 如果正在加载历史，则暂时不处理滚动逻辑，防止冲突
				if (this._isLoadingHistory) return;

				const {
					scrollTop,
					scrollHeight,
					clientHeight
				} = e.detail;
				// 定义一个阈值，比如50px，在阈值内都算作“在底部”
				const threshold = 50;
				this.isNearBottom = scrollHeight - scrollTop - clientHeight < threshold;
			},

			// 加载更多历史消息
			loadMoreMessages() {
				// 防止AI回复过程中触发加载历史
				if (this.isTyping) {
					console.log('正在打字时忽略加载历史消息');
					return;
				}

				// 设置加载标记，防止滚动冲突
				this._isLoadingHistory = true;

				// 实际项目中这里应该加载历史消息
				console.log('加载更多历史消息');

				// 模拟加载完成后重置标记
				setTimeout(() => {
					this._isLoadingHistory = false;
				}, 500);
			},

			// 格式化消息时间
			formatMessageTime(timestamp) {
				const date = new Date(timestamp);
				const today = new Date();
				const yesterday = new Date(today);
				yesterday.setDate(yesterday.getDate() - 1);

				// 判断是今天、昨天还是更早
				if (date.toDateString() === today.toDateString()) {
					return this.formatTime(date); // 今天，显示时间
				} else if (date.toDateString() === yesterday.toDateString()) {
					return '昨天 ' + this.formatTime(date); // 昨天
				} else {
					return `${date.getMonth() + 1}月${date.getDate()}日 ${this.formatTime(date)}`; // 更早
				}
			},

			// 格式化时间为 HH:MM
			formatTime(date) {
				const hours = date.getHours().toString().padStart(2, '0');
				const minutes = date.getMinutes().toString().padStart(2, '0');
				return `${hours}:${minutes}`;
			},

			// 判断是否显示时间分隔线
			showTimeDivider(message, index) {
				if (index === 0) return true;

				// 与上一条消息相比，如果时间间隔超过5分钟，则显示时间分隔线
				const prevMessage = this.messages[index - 1];
				return message.timestamp - prevMessage.timestamp > 5 * 60 * 1000;
			},

			// 切换输入模式（语音/文字）
			toggleInputMode() {
				this.isVoiceMode = !this.isVoiceMode;
			},

			// 初始化录音管理器
			initRecorderManager() {
				// #ifdef APP-PLUS || MP-WEIXIN
				this.recorderManager = uni.getRecorderManager();

				this.recorderManager.onStart(() => {
					console.log('录音开始');
					this.recordingTime = 0;
					this.recordTimer = setInterval(() => {
						this.recordingTime++;

						// 最长录音60秒
						if (this.recordingTime >= 60) {
							this.stopRecording();
						}
					}, 1000);

					// 模拟录音音量
					this.volumeTimer = setInterval(() => {
						this.recordingVolume = Math.floor(Math.random() * 10);
					}, 200);
				});

				this.recorderManager.onStop((res) => {
					console.log('录音结束', res);
					clearInterval(this.recordTimer);
					clearInterval(this.volumeTimer);
					this.recording = false;

					// 模拟语音识别
					setTimeout(() => {
						// 随机选择一个示例问题作为语音识别结果
						const recognizedText = this.suggestedQuestions[Math.floor(Math.random() * this
							.suggestedQuestions.length)];
						this.inputText = recognizedText;
						this.sendMessage();
					}, 1000);
				});
				// #endif
			},

			// 开始录音
			startRecording() {
				// #ifdef APP-PLUS || MP-WEIXIN
				this.recording = true;
				this.recorderManager.start({
					duration: 60000, // 最长60s
					sampleRate: 16000,
					numberOfChannels: 1,
					encodeBitRate: 64000,
					format: 'mp3'
				});
				// #endif

				// #ifdef H5
				// H5环境下模拟录音行为
				this.recording = true;
				this.recordingTime = 0;
				this.recordTimer = setInterval(() => {
					this.recordingTime++;
					if (this.recordingTime >= 60) {
						this.stopRecording();
					}
				}, 1000);

				this.volumeTimer = setInterval(() => {
					this.recordingVolume = Math.floor(Math.random() * 10);
				}, 200);
				// #endif
			},

			// 停止录音
			stopRecording() {
				// #ifdef APP-PLUS || MP-WEIXIN
				if (this.recording) {
					this.recorderManager.stop();
				}
				// #endif

				// #ifdef H5
				// H5环境下模拟停止录音
				clearInterval(this.recordTimer);
				clearInterval(this.volumeTimer);
				this.recording = false;

				// 模拟语音识别
				setTimeout(() => {
					// 随机选择一个示例问题作为语音识别结果
					const recognizedText = this.suggestedQuestions[Math.floor(Math.random() * this
						.suggestedQuestions.length)];
					this.inputText = recognizedText;
					this.sendMessage();
				}, 1000);
				// #endif
			},

			// 取消录音
			cancelRecording() {
				// #ifdef APP-PLUS || MP-WEIXIN
				if (this.recording) {
					this.recorderManager.stop();
					clearInterval(this.recordTimer);
					clearInterval(this.volumeTimer);
					this.recording = false;
				}
				// #endif

				// #ifdef H5
				clearInterval(this.recordTimer);
				clearInterval(this.volumeTimer);
				this.recording = false;
				uni.showToast({
					title: '录音已取消',
					icon: 'none'
				});
				// #endif
			},

			// 询问推荐问题
			askSuggestedQuestion(question) {
				this.inputText = question;
				this.sendMessage();
			},

			// 滚动到底部方法，continuous参数决定是单次执行还是持续执行
			scrollToBottom(continuous = false) {
				console.log("进入了scrollToBottom方法");

				const query = uni.createSelectorQuery().in(this);
				query.select('#chat-scroll-view').scrollOffset(res => {
					console.log("res.scrollHeight:", res.scrollHeight);
					console.log("this.scrollTop:", this.scrollTop);
					if (res) {
						// 直接设置scrollTop为滚动内容的总高度
						this.scrollTop = res.scrollHeight + 600;
						console.log("设置scrollTop为:", res.scrollHeight);
					}
				}).exec();
			},
		}
	}
</script>

<style lang="scss" scoped>
	/* 页面整体样式 */
	page {
		height: 100%;
		overflow: hidden;
		/* 防止页面整体滚动 */
	}

	/* 解决小程序和app滚动条的问题 */
	/* #ifdef MP-WEIXIN || APP-PLUS */
	::-webkit-scrollbar {
		display: none;
		width: 0 !important;
		height: 0 !important;
		-webkit-appearance: none;
		background: transparent;
	}

	/* #endif */

	/* 解决H5的问题 */
	/* #ifdef H5 */
	uni-scroll-view .uni-scroll-view::-webkit-scrollbar {
		display: none;
		width: 0 !important;
		height: 0 !important;
		-webkit-appearance: none;
		background: transparent;
	}

	/* #endif */

	/* 根容器样式 */
	.root-container {
		height: 100%;
		overflow: hidden;
		display: flex;
		flex-direction: column;
		background: linear-gradient(180deg, #F2F7FF 0%, #FFFFFF 50%);
	}

	/* 导航栏样式 */
	.nav-bar {
		width: 100%;
		padding: 20rpx 32rpx;
		padding-top: calc(20rpx + var(--status-bar-height));
		box-sizing: border-box;
		background: #FFFFFF;
		box-shadow: 0 1px 8px rgba(0, 0, 0, 0.05);
		position: relative;
		z-index: 100;

		.nav-content {
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 80rpx;

			.back-btn {
				width: 80rpx;
				height: 80rpx;
				display: flex;
				align-items: center;
			}

			.nav-title {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;

				text {
					font-size: 36rpx;
					font-weight: bold;
					color: #333;
				}

				.online-indicator {
					display: flex;
					align-items: center;
					margin-top: 4rpx;

					.dot {
						width: 16rpx;
						height: 16rpx;
						border-radius: 50%;
						background-color: #4CD964;
						margin-right: 8rpx;
					}

					text {
						font-size: 22rpx;
						color: #666;
						font-weight: normal;
					}
				}
			}

			.nav-right {
				width: 80rpx;
				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: flex-end;
			}
		}
	}

	/* 聊天容器样式 - 豆包式滚动优化 */
	.chat-container {
		flex: 1;
		width: 100%;
		padding: 20rpx 26rpx 0 26rpx;
		box-sizing: border-box;
		overflow: hidden;
		position: relative;
		height: 0;

		/* 豆包式滚动性能优化 */
		-webkit-overflow-scrolling: touch;
		will-change: scroll-position;
		transform: translateZ(0);
		backface-visibility: hidden;

		/* 滚动条隐藏 */
		scrollbar-width: none;
		-ms-overflow-style: none;

		/* 超平滑滚动优化 */
		scroll-behavior: smooth;
		scroll-snap-type: none;

		/* 防止滚动时的闪烁和抖动 */
		-webkit-transform: translate3d(0, 0, 0);
		-moz-transform: translate3d(0, 0, 0);
		-ms-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0);

		/* 优化滚动动画曲线 */
		transition: scroll-behavior 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);

		/* 优化触摸滚动 */
		-webkit-touch-callout: none;
		-webkit-user-select: none;
		-khtml-user-select: none;
		-moz-user-select: none;
		-ms-user-select: none;
		user-select: none;

		/* 防止过度滚动 */
		overscroll-behavior: contain;
	}

	/* 欢迎提示样式 */
	.welcome-tips {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 30rpx;
		margin: 40rpx 0;

		.ai-avatar-large {
			width: 120rpx;
			height: 120rpx;
			border-radius: 60rpx;
			margin-bottom: 20rpx;
			background-color: #fff;
		}

		.welcome-title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 16rpx;
		}

		.welcome-desc {
			font-size: 28rpx;
			color: #666;
			text-align: center;
			margin-bottom: 30rpx;
			line-height: 1.5;
		}
	}

	/* 推荐问题样式 */
	.suggested-questions {
		width: 100%;
		display: flex;
		flex-direction: column;
		gap: 20rpx;
		margin-top: 20rpx;

		.question-item {
			background: #F2F7FF;
			border: 2rpx solid rgba(75, 123, 236, 0.2);
			padding: 24rpx;
			border-radius: 16rpx;
			text-align: left;
			font-size: 28rpx;
			color: #4B7BEC;
			position: relative;
			transition: all 0.3s;

			&:active {
				transform: scale(0.98);
				background: rgba(75, 123, 236, 0.1);
			}

			&::after {
				content: "";
				position: absolute;
				width: 16rpx;
				height: 16rpx;
				border-top: 4rpx solid #4B7BEC;
				border-right: 4rpx solid #4B7BEC;
				transform: rotate(45deg);
				top: 50%;
				right: 24rpx;
				margin-top: -8rpx;
			}
		}
	}

	/* 时间分割线 */
	.time-divider {
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 30rpx 0;

		text {
			font-size: 24rpx;
			color: #999;
			background-color: rgba(255, 255, 255, 0.8);
			padding: 4rpx 16rpx;
			border-radius: 10rpx;
		}
	}

	/* 消息项样式 */
	.message-item {
		display: flex;
		position: relative;
		margin-bottom: 30rpx;

		.avatar-container {
			width: 80rpx;
			height: 80rpx;
			flex-shrink: 0;

			.avatar {
				width: 80rpx;
				height: 80rpx;
				border-radius: 50%;
				border: 2rpx solid #f5f5f5;
			}
		}

		.message-content {
			max-width: 70%;
			padding: 16rpx;
			border-radius: 16rpx;
			font-size: 28rpx;
			line-height: 1.5;
			word-break: break-all;
			position: relative;

			/* 豆包式渲染优化 */
			contain: layout style paint;
			will-change: contents;

			/* 优化文字渲染 */
			text-rendering: optimizeSpeed;
			-webkit-font-smoothing: antialiased;
			-moz-osx-font-smoothing: grayscale;

			/* 防止打字时的重排 */
			min-height: 1.5em;

			/* 优化动画性能 */
			transform: translateZ(0);
			backface-visibility: hidden;
		}
	}

	/* 用户消息样式 */
	.user-message {
		flex-direction: row-reverse;

		.message-content {
			margin-right: 20rpx;
			background-color: #4B7BEC;
			color: #fff;

			&::after {
				content: '';
				position: absolute;
				right: -12rpx;
				top: 20rpx;
				width: 0;
				height: 0;
				border-style: solid;
				border-width: 8rpx 0 8rpx 14rpx;
				border-color: transparent transparent transparent #4B7BEC;
			}
		}
	}

	/* AI消息样式 */
	.ai-message {
		flex-direction: row;

		.message-content {
			margin-left: 20rpx;
			background-color: #f5f7fa;
			color: #333;
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

			&::after {
				content: '';
				position: absolute;
				left: -12rpx;
				top: 20rpx;
				width: 0;
				height: 0;
				border-style: solid;
				border-width: 8rpx 14rpx 8rpx 0;
				border-color: transparent #f5f7fa transparent transparent;
			}
		}
	}

	/* 打字指示器样式 */
	.typing-indicator {
		display: flex;
		align-items: center;
		margin-top: 8rpx;

		.typing-dot {
			width: 12rpx;
			height: 12rpx;
			border-radius: 50%;
			background-color: #999;
			margin-right: 6rpx;
			animation: typing-animation 1.5s infinite ease-in-out;

			&:nth-child(1) {
				animation-delay: 0s;
			}

			&:nth-child(2) {
				animation-delay: 0.2s;
			}

			&:nth-child(3) {
				animation-delay: 0.4s;
				margin-right: 0;
			}
		}

		@keyframes typing-animation {

			0%,
			60%,
			100% {
				transform: translateY(0);
				opacity: 0.6;
			}

			30% {
				transform: translateY(-8rpx);
				opacity: 1;
			}
		}
	}

	/* 底部滚动锚点 - 确保可靠滚动 */
	.bottom-anchor {
		height: 10rpx;
		/* 增加高度确保滚动可见 */
		width: 100%;
		background: transparent;
		pointer-events: none;
		/* 确保锚点始终可见 */
		display: block;
		flex-shrink: 0;
	}

	.message-padding {
		height: 30rpx;
		width: 100%;
	}

	/* 输入区域样式 */
	.input-container {
		padding: 20rpx 32rpx;
		background-color: #fff;
		border-top: 1rpx solid #eee;
		display: flex;
		align-items: center;
		position: relative;

		.input-type-btn {
			width: 80rpx;
			height: 80rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			flex-shrink: 0;
		}

		.input-box {
			flex: 1;
			background-color: #F5F7FA;
			border-radius: 36rpx;
			padding: 16rpx 24rpx;
			margin: 0 20rpx;
			max-height: 200rpx;
			overflow-y: auto;

			.input-textarea {
				width: 100%;
				min-height: 40rpx;
				max-height: 160rpx;
				font-size: 28rpx;
				line-height: 1.5;
			}
		}

		.voice-btn {
			flex: 1;
			height: 72rpx;
			background-color: #F5F7FA;
			border-radius: 36rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 0 20rpx;

			text {
				font-size: 28rpx;
				color: #666;
			}

			&:active {
				background-color: #E0E3E8;
			}
		}

		.send-btn {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
			background-color: #CCCCCC;
			display: flex;
			justify-content: center;
			align-items: center;
			flex-shrink: 0;
			transition: all 0.3s;
		}

		.send-btn-active {
			background: linear-gradient(135deg, #4B7BEC, #3867D6);
			box-shadow: 0 4rpx 12rpx rgba(56, 103, 214, 0.3);
		}
	}

	/* 录音提示蒙层 */
	.recording-mask {
		position: fixed;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.4);
		z-index: 999;
		display: flex;
		justify-content: center;
		align-items: center;

		.recording-indicator {
			width: 300rpx;
			height: 300rpx;
			background-color: rgba(0, 0, 0, 0.7);
			border-radius: 24rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;

			.recording-wave {
				display: flex;
				align-items: flex-end;
				height: 100rpx;
				margin-bottom: 30rpx;

				.wave-item {
					width: 8rpx;
					height: 20rpx;
					background-color: #4B7BEC;
					margin: 0 6rpx;
					border-radius: 4rpx;
					transition: height 0.2s ease-in-out;
				}
			}

			.recording-wave-active {
				.wave-item {
					animation: wave-animation 1s infinite ease-in-out;

					&:nth-child(1) {
						animation-delay: 0s;
					}

					&:nth-child(2) {
						animation-delay: 0.2s;
					}

					&:nth-child(3) {
						animation-delay: 0.4s;
					}

					&:nth-child(4) {
						animation-delay: 0.6s;
					}

					&:nth-child(5) {
						animation-delay: 0.8s;
					}
				}
			}

			@keyframes wave-animation {

				0%,
				100% {
					height: 20rpx;
				}

				50% {
					height: 60rpx;
				}
			}

			text {
				color: #FFFFFF;
				font-size: 32rpx;
				margin-bottom: 20rpx;
			}

			.cancel-text {
				color: #CCCCCC;
				font-size: 24rpx;
			}
		}
	}

	/* ==================== 豆包式滚动专用动画 ==================== */

	/* 消息出现动画 */
	@keyframes message-appear {
		0% {
			opacity: 0;
			transform: translateY(20rpx);
		}

		100% {
			opacity: 1;
			transform: translateY(0);
		}
	}

	/* 打字内容变化时的微动画 */
	@keyframes content-update {
		0% {
			transform: translateY(0);
		}

		50% {
			transform: translateY(-2rpx);
		}

		100% {
			transform: translateY(0);
		}
	}

	/* 滚动指示器动画 */
	@keyframes scroll-hint {

		0%,
		100% {
			opacity: 0.3;
			transform: translateY(0);
		}

		50% {
			opacity: 0.8;
			transform: translateY(-10rpx);
		}
	}

	/* 新消息时的消息项动画 */
	.message-item {
		animation: message-appear 0.3s ease-out;
	}

	/* 打字时内容的微动画 */
	.ai-message .message-content {
		transition: all 0.1s ease-out;
	}

	/* 滚动容器的平滑过渡 */
	.chat-container {
		transition: scroll-behavior 0.3s ease;
	}

	/* 优化滚动时的性能 */
	.chat-container::-webkit-scrollbar {
		display: none;
		width: 0;
		height: 0;
	}

	/* 确保在不同平台下滚动条都隐藏 */
	.chat-container {
		scrollbar-width: none;
		-ms-overflow-style: none;
	}

	/* 豆包式滚动的关键帧优化 */
	@media (prefers-reduced-motion: no-preference) {
		.chat-container {
			scroll-behavior: smooth;
		}

		.message-content {
			transition: transform 0.1s ease-out;
		}
	}

	/* 减少动画的用户偏好设置 */
	@media (prefers-reduced-motion: reduce) {
		.chat-container {
			scroll-behavior: auto;
		}

		.message-item {
			animation: none;
		}

		.message-content {
			transition: none;
		}
	}

	/* Markdown富文本样式 */
	::v-deep .markdown-content {
		width: 100%;
		overflow-x: auto;

		/* 表格样式 */
		table {
			width: 100%;
			margin: 10rpx 0;
			border-collapse: collapse;
			background-color: #fff;
			font-size: 28rpx;
			border: 1px solid #e0e0e0;
		}

		th,
		td {
			padding: 16rpx 20rpx;
			border: 1px solid #e0e0e0;
			text-align: left;
		}

		th {
			background-color: #f5f5f5;
			color: #333;
			font-weight: 500;
		}

		tr:nth-child(even) {
			background-color: #f9f9f9;
		}

		/* 代码块样式 */
		pre {
			background-color: #f5f5f5;
			border-radius: 8rpx;
			padding: 20rpx;
			margin: 16rpx 0;
			overflow-x: auto;
		}

		code {
			font-family: Consolas, Monaco, 'Andale Mono', monospace;
			font-size: 28rpx;
			color: #333;
		}

		/* 行内代码 */
		p code {
			background-color: #f0f0f0;
			padding: 4rpx 8rpx;
			border-radius: 6rpx;
		}

		/* 标题样式 */
		h1,
		h2,
		h3,
		h4,
		h5,
		h6 {
			margin: 30rpx 0 20rpx 0;
			font-weight: 500;
			color: #333;
		}

		/* 引用样式 */
		blockquote {
			border-left: 8rpx solid #e0e0e0;
			padding: 16rpx 30rpx;
			margin: 20rpx 0;
			background-color: #f9f9f9;
			color: #666;
		}

		/* 列表样式 */
		ul,
		ol {
			padding-left: 40rpx;
			margin: 16rpx 0;
		}

		li {
			margin: 10rpx 0;
		}
	}
</style>