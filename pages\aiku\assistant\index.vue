<template>
  <view class="root-container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="nav-content">
        <view class="back-btn" @click="goBack">
          <u-icon name="arrow-left" color="#4B7BEC" size="36"></u-icon>
        </view>
        <view class="nav-title">
          <text>小叶同学</text>
          <view class="online-indicator">
            <view class="dot"></view>
            <text>在线</text>
          </view>
        </view>
        <view class="nav-right">
          <u-icon name="plus" color="#4B7BEC" size="40" @click="clearMessages"></u-icon>
        </view>
      </view>
    </view>
    
    <!-- 聊天消息区域 -->
    <scroll-view 
    	  class="chat-container"
    	  scroll-y
    	  :scroll-into-view="scrollIntoViewId"
    	  :scroll-with-animation="true"
    	  @scrolltoupper="loadMoreMessages"
    	  @scroll="onScroll"
    	  upper-threshold="200"
    	>
      <!-- 欢迎消息和提示 -->
      <view class="welcome-tips" v-if="messages.length === 0">
        <image src="/static/aiku/ai-assistant.png" class="ai-avatar-large" mode="aspectFit"></image>
        <text class="welcome-title">我是小叶同学</text>
        <text class="welcome-desc">您的智能AI助手，我可以回答问题、提供建议，陪您聊天</text>
        
        <!-- 推荐问题 -->
        <view class="suggested-questions">
          <view class="question-item" v-for="(item, index) in suggestedQuestions" :key="index" @click="askSuggestedQuestion(item)">
            <text>{{item}}</text>
          </view>
        </view>
      </view>
      
      <!-- 消息列表 -->
      <block v-for="(item, index) in messages" :key="index">
        <!-- 时间分割线 -->
        <view class="time-divider" v-if="showTimeDivider(item, index)">
          <text>{{ formatMessageTime(item.timestamp) }}</text>
        </view>
        
        <!-- 用户消息 -->
        <view class="message-item user-message" v-if="item.role === 'user'">
          <view class="avatar-container">
            <image :src="userInfo.face || userImage" class="avatar" mode="aspectFill"></image>
          </view>
          <view class="message-content">
            <text>{{ item.content }}</text>
          </view>
        </view>
        
        <!-- AI消息 -->
        <view class="message-item ai-message" v-else>
          <view class="avatar-container">
            <image src="/static/aiku/ai-assistant.png" class="avatar" mode="aspectFill"></image>
          </view>
          <view class="message-content">
            <text selectable>{{ item.content }}</text>
            <view class="typing-indicator" v-if="item.isTyping">
              <view class="typing-dot"></view>
              <view class="typing-dot"></view>
              <view class="typing-dot"></view>
            </view>
          </view>
        </view>
      </block>
      
      <view id="chat-bottom" class="bottom-space"></view>
    </scroll-view>
    
    <!-- 底部输入区域 -->
    <view class="input-container">
      <!-- 语音/文字输入切换按钮 -->
      <view class="input-type-btn" @click="toggleInputMode">
        <u-icon :name="isVoiceMode ? 'list-dot' : 'mic'" size="46" color="#4B7BEC"></u-icon>
      </view>
      
      <!-- 文本输入框 -->
      <view class="input-box" v-if="!isVoiceMode">
        <textarea 
          class="input-textarea" 
          v-model="inputText" 
          :disabled="isSending"
          auto-height 
          :maxlength="-1"
          :cursor-spacing="20"
          @confirm="sendMessage"
          :placeholder="isSending ? '发送中...' : '请输入问题...'"
          confirm-type="send"
        />
      </view>
      
      <!-- 语音按钮 -->
      <view 
        class="voice-btn" 
        v-else 
        @touchstart="startRecording" 
        @touchend="stopRecording"
        @touchcancel="cancelRecording"
      >
        <text>{{ recording ? '松开发送' : '按住说话' }}</text>
      </view>
      
      <!-- 发送按钮 -->
      <view class="send-btn" :class="{'send-btn-active': canSend}" @click="sendMessage">
        <u-icon name="arrow-upward" color="#FFFFFF" size="48"></u-icon>
      </view>
    </view>
    
    <!-- 录音提示层 -->
    <view class="recording-mask" v-if="recording">
      <view class="recording-indicator">
        <view class="recording-wave" :class="{'recording-wave-active': recordingVolume > 0}">
          <view class="wave-item" v-for="i in 5" :key="i" :style="{height: 10 + recordingVolume * 2 + 'rpx'}"></view>
        </view>
        <text>{{ recordingTime }}s</text>
        <text class="cancel-text">上滑取消</text>
      </view>
    </view>
  </view>
</template>

<script>
import configs from '@/config/config'
export default {
  data() {
    return {
      // 页面数据
      configs,
      userImage:configs.defaultUserPhoto,
      userInfo: {},
      messages: [],
      inputText: '',
      scrollUpdateTimer: null,
      scrollTimer: null, // 新增：滚动计时器
      isAutoScrollEnabled: true,
      isVoiceMode: false,
      recording: false,
      recordingTime: 0,
      recordingVolume: 0,
      recordTimer: null,
      volumeTimer: null,
      isSending: false,
      recorderManager: null,
	  scrollIntoViewId: '', // 用于定位的元素ID
	  isScrolling: false, // 滚动状态标记
      
      // 示例推荐问题
      suggestedQuestions: [
        '你能做什么？',
        '请讲个笑话',
        '今天天气怎么样？',
        '你是谁？'
      ],
      
      // 测试数据（用于展示）
      testMessages: [
        {
          role: 'assistant',
          content: '你好！我是小叶同学，很高兴认识你。有什么我可以帮助你的吗？',
          timestamp: Date.now() - 24 * 60 * 60 * 1000 // 昨天
        },
        {
          role: 'user',
          content: '你好，我想了解一下你能做什么？',
          timestamp: Date.now() - 24 * 60 * 60 * 1000 + 1000
        },
        {
          role: 'assistant',
          content: '我可以帮助你回答问题、提供信息、进行日常对话、讲笑话、提供学习帮助等。你有什么具体需要吗？',
          timestamp: Date.now() - 24 * 60 * 60 * 1000 + 5000
        },
        {
          role: 'user',
          content: '给我讲个笑话吧',
          timestamp: Date.now() - 5 * 60 * 1000 // 5分钟前
        },
        {
          role: 'assistant',
          content: '好的，这是一个关于程序员的笑话：\n\n程序员去面试，面试官问："你有5年经验吗？"\n程序员："我有1年经验用了5年。"',
          timestamp: Date.now() - 5 * 60 * 1000 + 3000
        }
      ]
    }
  },
  computed: {
    // 判断是否可以发送消息
    canSend() {
      return this.inputText.trim().length > 0 && !this.isSending;
    },
	// 优化后的消息列表（可以添加虚拟滚动支持）
	  optimizedMessages() {
	    // 如果消息过多，只渲染最近的N条
	    const maxVisible = 50;
	    if (this.messages.length > maxVisible) {
	      return this.messages.slice(-maxVisible);
	    }
	    return this.messages;
	  }
  },
  watch: {
    // 监听消息数组变化，自动滚动到底部
    messages: {
      deep: true,
      handler(val) {
        if (val.length > 0) {
          this.scrollToBottom();
        }
      }
    }
  },
  mounted() {
    this.userInfo = this.$options.filters.isLogin() || {};

    // 加载测试数据
    // this.loadTestData();
    
    // 初始化录音管理器
    this.initRecorderManager();
	
	// 创建防抖的滚动处理函数
	  this.debouncedScroll = this.debounce((e) => {
	    const scrollHeight = e.detail.scrollHeight;
	    const scrollTop = e.detail.scrollTop;
	    const clientHeight = e.detail.clientHeight;
	    
	    // 当用户滚动到接近底部时，启用自动滚动
	    this.isAutoScrollEnabled = (scrollHeight - scrollTop - clientHeight < 100);
	  }, 100);
	  
	  // 监听页面显示事件，确保在页面显示时滚动到底部
	  uni.$on('updateScrollPosition', () => {
	    setTimeout(() => {
	      this.scrollToBottom(true);
	    }, 300);
	  });
	  
	  // 在页面显示时触发滚动
	  uni.$emit('updateScrollPosition');
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 加载测试数据
    loadTestData() {
      setTimeout(() => {
        this.messages = [...this.testMessages];
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }, 500);
    },

    // 清空对话记录
    clearMessages() {
      this.messages = [];
    },
    
    // 发送消息
	sendMessage() {
	  if (!this.canSend) return;
	  
	  const content = this.inputText.trim();
	  this.inputText = '';
	  
	  // 批量更新消息，减少渲染次数
	  const userMessage = {
	    role: 'user',
	    content,
	    timestamp: Date.now()
	  };
	  
	  const assistantMessage = {
	    role: 'assistant',
	    content: '',
	    isTyping: true,
	    timestamp: Date.now()
	  };
	  
	  // 一次性添加两条消息
	  this.messages.push(userMessage, assistantMessage);
	  
	  this.isSending = true;
	  
	  // 确保滚动到底部，使用延时确保DOM已更新
	  this.$nextTick(() => {
	    setTimeout(() => {
	      this.scrollToBottom(true);
	    }, 50);
	  });
	  
	  // 生成回复
	  const response = this.generateResponse(content);
	  this.simulateTypeWriting(assistantMessage, response);
	},
    
    // 模拟生成回复
    generateResponse(question) {
      // 这里是模拟的回复逻辑，实际项目中应该调用API
      question = question.toLowerCase();
      
      if (question.includes('你是谁') || question.includes('你叫什么') || question.includes('你的名字')) {
        return '我是小叶同学，您的AI智能助手。我随时准备为您提供帮助和服务！';
      } else if (question.includes('笑话') || question.includes('段子')) {
        return '今天给您带来一个有趣的故事：\n\n有一位老人，每天都去公园散步，每天都带着帽子。有一天，一位小朋友问他："爷爷，您为什么每天都戴帽子呢？"\n老人微笑着回答："因为我昨天忘戴了，找了一天也没找到自己。"';
      } else if (question.includes('天气')) {
        return '目前小叶同学不能获取实时天气数据，建议您查看天气预报APP或网站获取最准确的天气信息。未来我会继续升级，为您提供更多服务！';
      } else if (question.includes('能做什么') || question.includes('功能') || question.includes('帮我')) {
        return '我是您的AI助手小叶同学，可以：\n1. 回答您的问题和疑惑\n2. 提供信息和知识\n3. 和您进行日常对话\n4. 讲笑话和有趣的故事\n5. 未来会支持更多功能\n\n请问有什么我可以帮助您的吗？';
      } else {
        return '感谢您的提问。作为您的AI助手，我会尽力为您提供帮助。您的问题很有价值，希望我的回答能够对您有所帮助。如果需要更多信息，请随时告诉我！';
      }
    },
    
    // 模拟流式打字效果
    simulateTypeWriting(message, fullText) {
      this.isAutoScrollEnabled = true;
      
      let index = 0;
      let lastScrollTime = 0;
      const scrollThrottle = 100; // 减少滚动节流时间，提高滚动频率
      
      const typing = setInterval(() => {
        if (index < fullText.length) {
          // 每次添加2-5个字符
          const step = Math.floor(Math.random() * 4) + 2;
          index = Math.min(index + step, fullText.length);
          message.content = fullText.substring(0, index);
          
          // 使用节流控制滚动频率
          const now = Date.now();
          if (now - lastScrollTime > scrollThrottle) {
            this.scrollToBottom(true); // 强制滚动
            lastScrollTime = now;
          }
        } else {
          clearInterval(typing);
          message.isTyping = false;
          this.isSending = false;
          
          // 最后强制滚动多次确保到底，延迟执行确保DOM已更新
          setTimeout(() => {
            this.scrollToBottom(true);
            // 再延迟200ms再次滚动一次，确保最后内容显示
            setTimeout(() => {
              this.scrollToBottom(true);
            }, 500);
          }, 100);
        }
      }, 30); // 减少更新间隔，让文字输出更流畅
    },
    
    // 添加平滑滚动方法
    scrollToBottomSmooth() {
      // 使用 requestAnimationFrame 优化性能
      requestAnimationFrame(() => {
        this.scrollIntoViewId = '';
        this.$nextTick(() => {
          // 使用延时确保DOM已更新
          setTimeout(() => {
            this.scrollIntoViewId = 'chat-bottom';
          }, 50);
        });
      });
    },
    
    // 滚动到底部
    // 优化滚动到底部的方法
    scrollToBottom(force = false) {
      if (!this.isAutoScrollEnabled && !force) return;
      
      // 清除之前的计时器（如果存在）
      if (this.scrollTimer) {
        clearTimeout(this.scrollTimer);
      }
      
      // 使用多重延时确保DOM完全更新后再滚动
      this.$nextTick(() => {
        // 先清空ID
        this.scrollIntoViewId = '';
        
        // 第一次延时：等待视图更新
        this.scrollTimer = setTimeout(() => {
          // 第二次延时：确保渲染完成
          setTimeout(() => {
            // 设置滚动ID
            this.scrollIntoViewId = 'chat-bottom';
            
            // 第三次延时：确保滚动完成
            setTimeout(() => {
              // 再次触发滚动以防万一
              this.scrollIntoViewId = '';
              setTimeout(() => {
                this.scrollIntoViewId = 'chat-bottom';
              }, 50);
            }, 100);
          }, 50);
        }, 50);
      });
    },
    
    // 监听用户滚动事件，决定是否继续自动滚动
    onScroll(e) {
      this.debouncedScroll(e);
    },
    
    // 加载更多历史消息
    loadMoreMessages() {
      // 实际项目中这里应该加载历史消息
      console.log('加载更多历史消息');
    },
    
    // 格式化消息时间
    formatMessageTime(timestamp) {
      const date = new Date(timestamp);
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      
      // 判断是今天、昨天还是更早
      if (date.toDateString() === today.toDateString()) {
        return this.formatTime(date); // 今天，显示时间
      } else if (date.toDateString() === yesterday.toDateString()) {
        return '昨天 ' + this.formatTime(date); // 昨天
      } else {
        return `${date.getMonth() + 1}月${date.getDate()}日 ${this.formatTime(date)}`; // 更早
      }
    },
    
    // 格式化时间为 HH:MM
    formatTime(date) {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    },
    
    // 判断是否显示时间分隔线
    showTimeDivider(message, index) {
      if (index === 0) return true;
      
      // 与上一条消息相比，如果时间间隔超过5分钟，则显示时间分隔线
      const prevMessage = this.messages[index - 1];
      return message.timestamp - prevMessage.timestamp > 5 * 60 * 1000;
    },
    
    // 切换输入模式（语音/文字）
    toggleInputMode() {
      this.isVoiceMode = !this.isVoiceMode;
    },
    
    // 初始化录音管理器
    initRecorderManager() {
      // #ifdef APP-PLUS || MP-WEIXIN
      this.recorderManager = uni.getRecorderManager();
      
      this.recorderManager.onStart(() => {
        console.log('录音开始');
        this.recordingTime = 0;
        this.recordTimer = setInterval(() => {
          this.recordingTime++;
          
          // 最长录音60秒
          if (this.recordingTime >= 60) {
            this.stopRecording();
          }
        }, 1000);
        
        // 模拟录音音量
        this.volumeTimer = setInterval(() => {
          this.recordingVolume = Math.floor(Math.random() * 10);
        }, 200);
      });
      
      this.recorderManager.onStop((res) => {
        console.log('录音结束', res);
        clearInterval(this.recordTimer);
        clearInterval(this.volumeTimer);
        this.recording = false;
        
        // 模拟语音识别
        setTimeout(() => {
          // 随机选择一个示例问题作为语音识别结果
          const recognizedText = this.suggestedQuestions[Math.floor(Math.random() * this.suggestedQuestions.length)];
          this.inputText = recognizedText;
          this.sendMessage();
        }, 1000);
      });
      // #endif
    },
    
    // 开始录音
    startRecording() {
      // #ifdef APP-PLUS || MP-WEIXIN
      this.recording = true;
      this.recorderManager.start({
        duration: 60000, // 最长60s
        sampleRate: 16000,
        numberOfChannels: 1,
        encodeBitRate: 64000,
        format: 'mp3'
      });
      // #endif
      
      // #ifdef H5
      // H5环境下模拟录音行为
      this.recording = true;
      this.recordingTime = 0;
      this.recordTimer = setInterval(() => {
        this.recordingTime++;
        if (this.recordingTime >= 60) {
          this.stopRecording();
        }
      }, 1000);
      
      this.volumeTimer = setInterval(() => {
        this.recordingVolume = Math.floor(Math.random() * 10);
      }, 200);
      // #endif
    },
    
    // 停止录音
    stopRecording() {
      // #ifdef APP-PLUS || MP-WEIXIN
      if (this.recording) {
        this.recorderManager.stop();
      }
      // #endif
      
      // #ifdef H5
      // H5环境下模拟停止录音
      clearInterval(this.recordTimer);
      clearInterval(this.volumeTimer);
      this.recording = false;
      
      // 模拟语音识别
      setTimeout(() => {
        // 随机选择一个示例问题作为语音识别结果
        const recognizedText = this.suggestedQuestions[Math.floor(Math.random() * this.suggestedQuestions.length)];
        this.inputText = recognizedText;
        this.sendMessage();
      }, 1000);
      // #endif
    },
    
    // 取消录音
    cancelRecording() {
      // #ifdef APP-PLUS || MP-WEIXIN
      if (this.recording) {
        this.recorderManager.stop();
        clearInterval(this.recordTimer);
        clearInterval(this.volumeTimer);
        this.recording = false;
      }
      // #endif
      
      // #ifdef H5
      clearInterval(this.recordTimer);
      clearInterval(this.volumeTimer);
      this.recording = false;
      uni.showToast({
        title: '录音已取消',
        icon: 'none'
      });
      // #endif
    },
    
    // 询问推荐问题
    askSuggestedQuestion(question) {
      this.inputText = question;
      this.sendMessage();
    },
	
	debounce(func, wait) {
	  let timeout;
	  return function executedFunction(...args) {
	    const later = () => {
	      clearTimeout(timeout);
	      func(...args);
	    };
	    clearTimeout(timeout);
	    timeout = setTimeout(later, wait);
	  };
	},
  }
}
</script>

<style lang="scss" scoped>
/* 页面整体样式 */
page {
  height: 100%;
  overflow: hidden; /* 防止页面整体滚动 */
}

/* 解决小程序和app滚动条的问题 */
/* #ifdef MP-WEIXIN || APP-PLUS */
::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
/* #endif */

/* 解决H5的问题 */
/* #ifdef H5 */
uni-scroll-view .uni-scroll-view::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
/* #endif */

/* 根容器样式 */
.root-container {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #F2F7FF 0%, #FFFFFF 50%);
}

/* 导航栏样式 */
.nav-bar {
  width: 100%;
  padding: 20rpx 32rpx;
  padding-top: calc(20rpx + var(--status-bar-height));
  box-sizing: border-box;
  background: #FFFFFF;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 100;
  
  .nav-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80rpx;
    
    .back-btn {
      width: 80rpx;
      height: 80rpx;
      display: flex;
      align-items: center;
    }
    
    .nav-title {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      
      text {
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
      }
      
      .online-indicator {
        display: flex;
        align-items: center;
        margin-top: 4rpx;
        
        .dot {
          width: 16rpx;
          height: 16rpx;
          border-radius: 50%;
          background-color: #4CD964;
          margin-right: 8rpx;
        }
        
        text {
          font-size: 22rpx;
          color: #666;
          font-weight: normal;
        }
      }
    }
    
    .nav-right {
      width: 80rpx;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }
}

/* 聊天容器样式 */
.chat-container {
  flex: 1;
  width: 100%;
  padding: 20rpx 32rpx;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
  height: 0;
  /* 优化滚动性能 */
    -webkit-overflow-scrolling: touch;
    will-change: scroll-position;
    
    /* 优化渲染性能 */
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* 欢迎提示样式 */
.welcome-tips {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx;
  margin: 40rpx 0;
  
  .ai-avatar-large {
    width: 120rpx;
    height: 120rpx;
    border-radius: 60rpx;
    margin-bottom: 20rpx;
    background-color: #fff;
  }
  
  .welcome-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 16rpx;
  }
  
  .welcome-desc {
    font-size: 28rpx;
    color: #666;
    text-align: center;
    margin-bottom: 30rpx;
    line-height: 1.5;
  }
}

/* 推荐问题样式 */
.suggested-questions {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-top: 20rpx;
  
  .question-item {
    background: #F2F7FF;
    border: 2rpx solid rgba(75, 123, 236, 0.2);
    padding: 24rpx;
    border-radius: 16rpx;
    text-align: left;
    font-size: 28rpx;
    color: #4B7BEC;
    position: relative;
    transition: all 0.3s;
    
    &:active {
      transform: scale(0.98);
      background: rgba(75, 123, 236, 0.1);
    }
    
    &::after {
      content: "";
      position: absolute;
      width: 16rpx;
      height: 16rpx;
      border-top: 4rpx solid #4B7BEC;
      border-right: 4rpx solid #4B7BEC;
      transform: rotate(45deg);
      top: 50%;
      right: 24rpx;
      margin-top: -8rpx;
    }
  }
}

/* 时间分割线 */
.time-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 30rpx 0;
  
  text {
    font-size: 24rpx;
    color: #999;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 4rpx 16rpx;
    border-radius: 10rpx;
  }
}

/* 消息项样式 */
.message-item {
  display: flex;
  margin-bottom: 30rpx;
  position: relative;
  
  .avatar-container {
    width: 80rpx;
    height: 80rpx;
    flex-shrink: 0;
    
    .avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      border: 2rpx solid #f5f5f5;
    }
  }
  
  .message-content {
    max-width: 70%;
    padding: 16rpx;
    border-radius: 16rpx;
    font-size: 28rpx;
    line-height: 1.5;
    word-break: break-all;
    position: relative;
	/* 防止重排 */
	  contain: layout style paint;
	  
	  /* 优化文字渲染 */
	  text-rendering: optimizeSpeed;
  }
}

/* 用户消息样式 */
.user-message {
  flex-direction: row-reverse;
  
  .message-content {
    margin-right: 20rpx;
    background-color: #4B7BEC;
    color: #fff;
    
    &::after {
      content: '';
      position: absolute;
      right: -12rpx;
      top: 20rpx;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 8rpx 0 8rpx 14rpx;
      border-color: transparent transparent transparent #4B7BEC;
    }
  }
}

/* AI消息样式 */
.ai-message {
  flex-direction: row;
  
  .message-content {
    margin-left: 20rpx;
    background-color: #f5f7fa;
    color: #333;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    
    &::after {
      content: '';
      position: absolute;
      left: -12rpx;
      top: 20rpx;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 8rpx 14rpx 8rpx 0;
      border-color: transparent #f5f7fa transparent transparent;
    }
  }
}

/* 打字指示器样式 */
.typing-indicator {
  display: flex;
  align-items: center;
  margin-top: 8rpx;
  
  .typing-dot {
    width: 12rpx;
    height: 12rpx;
    border-radius: 50%;
    background-color: #999;
    margin-right: 6rpx;
    animation: typing-animation 1.5s infinite ease-in-out;
    
    &:nth-child(1) {
      animation-delay: 0s;
    }
    
    &:nth-child(2) {
      animation-delay: 0.2s;
    }
    
    &:nth-child(3) {
      animation-delay: 0.4s;
      margin-right: 0;
    }
  }
  
  @keyframes typing-animation {
    0%, 60%, 100% {
      transform: translateY(0);
      opacity: 0.6;
    }
    30% {
      transform: translateY(-8rpx);
      opacity: 1;
    }
  }
}

/* 底部留白 */
.bottom-space {
  height: 10rpx;
  width: 100%;
}

.message-padding {
  height: 30rpx;
  width: 100%;
}

/* 输入区域样式 */
.input-container {
  padding: 20rpx 32rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
  display: flex;
  align-items: center;
  position: relative;
  
  .input-type-btn {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
  }
  
  .input-box {
    flex: 1;
    background-color: #F5F7FA;
    border-radius: 36rpx;
    padding: 16rpx 24rpx;
    margin: 0 20rpx;
    max-height: 200rpx;
    overflow-y: auto;
    
    .input-textarea {
      width: 100%;
      min-height: 40rpx;
      max-height: 160rpx;
      font-size: 28rpx;
      line-height: 1.5;
    }
  }
  
  .voice-btn {
    flex: 1;
    height: 72rpx;
    background-color: #F5F7FA;
    border-radius: 36rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 20rpx;
    
    text {
      font-size: 28rpx;
      color: #666;
    }
    
    &:active {
      background-color: #E0E3E8;
    }
  }
  
  .send-btn {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background-color: #CCCCCC;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    transition: all 0.3s;
  }
  
  .send-btn-active {
    background: linear-gradient(135deg, #4B7BEC, #3867D6);
    box-shadow: 0 4rpx 12rpx rgba(56, 103, 214, 0.3);
  }
}

/* 录音提示蒙层 */
.recording-mask {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
  
  .recording-indicator {
    width: 300rpx;
    height: 300rpx;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 24rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    
    .recording-wave {
      display: flex;
      align-items: flex-end;
      height: 100rpx;
      margin-bottom: 30rpx;
      
      .wave-item {
        width: 8rpx;
        height: 20rpx;
        background-color: #4B7BEC;
        margin: 0 6rpx;
        border-radius: 4rpx;
        transition: height 0.2s ease-in-out;
      }
    }
    
    .recording-wave-active {
      .wave-item {
        animation: wave-animation 1s infinite ease-in-out;
        
        &:nth-child(1) {
          animation-delay: 0s;
        }
        
        &:nth-child(2) {
          animation-delay: 0.2s;
        }
        
        &:nth-child(3) {
          animation-delay: 0.4s;
        }
        
        &:nth-child(4) {
          animation-delay: 0.6s;
        }
        
        &:nth-child(5) {
          animation-delay: 0.8s;
        }
      }
    }
    
    @keyframes wave-animation {
      0%, 100% {
        height: 20rpx;
      }
      50% {
        height: 60rpx;
      }
    }
    
    text {
      color: #FFFFFF;
      font-size: 32rpx;
      margin-bottom: 20rpx;
    }
    
    .cancel-text {
      color: #CCCCCC;
      font-size: 24rpx;
    }
  }
}
</style>
