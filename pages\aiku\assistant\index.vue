<template>
  <view class="root-container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="nav-content">
        <view class="back-btn" @click="goBack">
          <u-icon name="arrow-left" color="#4B7BEC" size="36"></u-icon>
        </view>
        <view class="nav-title">
          <text>小叶同学</text>
          <view class="online-indicator">
            <view class="dot"></view>
            <text>在线</text>
          </view>
        </view>
        <view class="nav-right">
          <u-icon name="plus" color="#4B7BEC" size="40" @click="clearMessages"></u-icon>
        </view>
      </view>
    </view>
    
    <!-- 聊天消息区域 -->
    <scroll-view
      class="chat-container"
      scroll-y
      :scroll-into-view="scrollIntoViewId"
      :scroll-with-animation="true"
      :enable-back-to-top="false"
      :enhanced="true"
      :bounces="false"
      :show-scrollbar="false"
      :fast-deceleration="false"
      :scroll-anchoring="true"
      :refresher-enabled="false"
      :scroll-top="scrollTop"
      @scrolltoupper="loadMoreMessages"
      @scroll="onScroll"
      upper-threshold="200"
      lower-threshold="50"
    >
      <!-- 欢迎消息和提示 -->
      <view class="welcome-tips" v-if="messages.length === 0">
        <image src="/static/aiku/ai-assistant.png" class="ai-avatar-large" mode="aspectFit"></image>
        <text class="welcome-title">我是小叶同学</text>
        <text class="welcome-desc">您的智能AI助手，我可以回答问题、提供建议，陪您聊天</text>
        
        <!-- 推荐问题 -->
        <view class="suggested-questions">
          <view class="question-item" v-for="(item, index) in suggestedQuestions" :key="index" @click="askSuggestedQuestion(item)">
            <text>{{item}}</text>
          </view>
        </view>
      </view>
      
      <!-- 消息列表 -->
      <block v-for="(item, index) in messages" :key="index">
        <!-- 时间分割线 -->
        <view class="time-divider" v-if="showTimeDivider(item, index)">
          <text>{{ formatMessageTime(item.timestamp) }}</text>
        </view>
        
        <!-- 用户消息 -->
        <view class="message-item user-message" v-if="item.role === 'user'">
          <view class="avatar-container">
            <image :src="userInfo.face || userImage" class="avatar" mode="aspectFill"></image>
          </view>
          <view class="message-content">
            <text>{{ item.content }}</text>
          </view>
        </view>
        
        <!-- AI消息 -->
        <view class="message-item ai-message" v-else>
          <view class="avatar-container">
            <image src="/static/aiku/ai-assistant.png" class="avatar" mode="aspectFill"></image>
          </view>
          <view class="message-content">
            <!-- 根据是否包含特殊格式切换呈现方式 -->
            <view v-if="hasSpecialFormat(item.content)">
              <rich-text :nodes="convertMarkdownToHtml(item.content)" class="markdown-content"></rich-text>
            </view>
            <text v-else selectable>{{ item.content }}</text>
            
            <view class="typing-indicator" v-if="item.isTyping">
              <view class="typing-dot"></view>
              <view class="typing-dot"></view>
              <view class="typing-dot"></view>
            </view>
          </view>
        </view>
      </block>
      
      <!-- 底部滚动锚点 - 增加高度确保可见 -->
      <view id="chat-bottom" class="bottom-anchor"></view>
    </scroll-view>
    
    <!-- 底部输入区域 -->
    <view class="input-container">
      <!-- 语音/文字输入切换按钮 -->
      <view class="input-type-btn" @click="toggleInputMode">
        <u-icon :name="isVoiceMode ? 'list-dot' : 'mic'" size="46" color="#4B7BEC"></u-icon>
      </view>
      
      <!-- 文本输入框 -->
      <view class="input-box" v-if="!isVoiceMode">
        <textarea 
          class="input-textarea" 
          v-model="inputText" 
          :disabled="isSending"
          auto-height 
          :maxlength="-1"
          :cursor-spacing="20"
          @confirm="sendMessage"
          :placeholder="isSending ? '发送中...' : '请输入问题...'"
          confirm-type="send"
        />
      </view>
      
      <!-- 语音按钮 -->
      <view 
        class="voice-btn" 
        v-else 
        @touchstart="startRecording" 
        @touchend="stopRecording"
        @touchcancel="cancelRecording"
      >
        <text>{{ recording ? '松开发送' : '按住说话' }}</text>
      </view>
      
      <!-- 发送按钮 -->
      <view class="send-btn" :class="{'send-btn-active': canSend}" @click="sendMessage">
        <u-icon name="arrow-upward" color="#FFFFFF" size="48"></u-icon>
      </view>
    </view>
    
    <!-- 录音提示层 -->
    <view class="recording-mask" v-if="recording">
      <view class="recording-indicator">
        <view class="recording-wave" :class="{'recording-wave-active': recordingVolume > 0}">
          <view class="wave-item" v-for="i in 5" :key="i" :style="{height: 10 + recordingVolume * 2 + 'rpx'}"></view>
        </view>
        <text>{{ recordingTime }}s</text>
        <text class="cancel-text">上滑取消</text>
      </view>
    </view>
  </view>
</template>

<script>
import configs from '@/config/config'
import aiChatService from '@/api/aiChatService.js'

export default {
  data() {
    return {
      // 页面数据
      configs,
      userImage:configs.defaultUserPhoto,
      userInfo: {},
      messages: [],
      inputText: '',

      // 豆包式滚动控制系统
      scrollIntoViewId: '', // 用于定位的元素ID
      scrollTop: 0, // 滚动位置控制
      lastSmoothScrollTime: 0, // 上次平滑滚动时间
      isAutoScrollEnabled: true, // 是否启用自动滚动
      isUserScrolling: false, // 用户是否正在手动滚动
      lastScrollTop: 0, // 上次滚动位置
      scrollEndTimer: null, // 滚动结束检测计时器
      autoScrollTimer: null, // 自动滚动计时器
      scrollAnimationFrame: null, // 滚动动画帧
      isNearBottom: true, // 是否接近底部
      scrollThreshold: 100, // 底部阈值（rpx）
      userScrollTimeout: 2000, // 用户滚动后多久恢复自动滚动（ms）

      // 平滑滚动控制
      smoothScrollTimer: null, // 平滑滚动计时器
      scrollTargetTop: 0, // 目标滚动位置
      currentScrollTop: 0, // 当前滚动位置
      scrollSpeed: 0.3, // 滚动速度系数（0-1）
      isSmoothing: false, // 是否正在平滑滚动

      // 打字效果控制
      typingScrollTimer: null, // 打字时滚动计时器
      isTyping: false, // 是否正在打字
      realTimeScrollTimer: null, // 实时滚动计时器

      // 其他状态
      isVoiceMode: false,
      recording: false,
      recordingTime: 0,
      recordingVolume: 0,
      recordTimer: null,
      volumeTimer: null,
      isSending: false,
      recorderManager: null,
      
      // 示例推荐问题
      suggestedQuestions: [
        '你能做什么？',
        '请讲个笑话',
        '今天天气怎么样？',
        '你是谁？'
      ],
      
      // 测试数据（用于展示）
      testMessages: [
        {
          role: 'assistant',
          content: '你好！我是小叶同学，很高兴认识你。有什么我可以帮助你的吗？',
          timestamp: Date.now() - 24 * 60 * 60 * 1000 // 昨天
        },
        {
          role: 'user',
          content: '你好，我想了解一下你能做什么？',
          timestamp: Date.now() - 24 * 60 * 60 * 1000 + 1000
        },
        {
          role: 'assistant',
          content: '我可以帮助你回答问题、提供信息、进行日常对话、讲笑话、提供学习帮助等。你有什么具体需要吗？',
          timestamp: Date.now() - 24 * 60 * 60 * 1000 + 5000
        },
        {
          role: 'user',
          content: '给我讲个笑话吧',
          timestamp: Date.now() - 5 * 60 * 1000 // 5分钟前
        },
        {
          role: 'assistant',
          content: '好的，这是一个关于程序员的笑话：\n\n程序员去面试，面试官问："你有5年经验吗？"\n程序员："我有1年经验用了5年。"',
          timestamp: Date.now() - 5 * 60 * 1000 + 3000
        }
      ]
    }
  },
  computed: {
    // 判断是否可以发送消息
    canSend() {
      return this.inputText.trim().length > 0 && !this.isSending;
    },
	// 优化后的消息列表（可以添加虚拟滚动支持）
	  optimizedMessages() {
	    // 如果消息过多，只渲染最近的N条
	    const maxVisible = 50;
	    if (this.messages.length > maxVisible) {
	      return this.messages.slice(-maxVisible);
	    }
	    return this.messages;
	  }
  },
  watch: {
    // 监听消息数组变化，智能滚动到底部
    messages: {
      deep: true,
      handler(newVal, oldVal) {
        if (newVal.length > 0) {
          // 如果是新增消息，且用户没有在滚动，则自动滚动
          if (!oldVal || newVal.length > oldVal.length) {
            this.$nextTick(() => {
              setTimeout(() => {
                this.smoothScrollToBottom('new_message');
              }, 50);
            });
          }

          // 如果是消息内容变化（打字过程），使用平滑滚动跟随
          if (oldVal && newVal.length === oldVal.length) {
            // 检查最后一条消息是否有内容变化
            const lastNew = newVal[newVal.length - 1];
            const lastOld = oldVal[oldVal.length - 1];
            if (lastNew && lastOld && lastNew.content !== lastOld.content) {
              // 内容变化时，如果是AI消息且正在打字，使用平滑滚动
              if (lastNew.role === 'assistant' && this.isTyping) {
                this.smoothScrollToFollow();
              }
            }
          }
        }
      }
    },

    // 监听打字状态变化
    isTyping: {
      handler(newVal) {
        if (newVal) {
          // 开始打字时，启用实时滚动监控
          this.startRealTimeScroll();
        } else {
          // 打字结束时，停止监控并确保滚动到底部
          this.stopRealTimeScroll();
          this.$nextTick(() => {
            setTimeout(() => {
              this.smoothScrollToBottom('typing_end');
            }, 200);
          });
        }
      }
    }
  },
  mounted() {
    this.userInfo = this.$options.filters.isLogin() || {};

    // 初始化AI聊天服务
    this.initAiChatService();

    // 初始化录音管理器
    this.initRecorderManager();

    // 初始化豆包式滚动系统
    this.initScrollSystem();

    // 监听页面显示事件
    uni.$on('pageShow', this.handlePageShow);
    uni.$on('pageHide', this.handlePageHide);

    // 页面显示时确保滚动到底部
    this.$nextTick(() => {
      setTimeout(() => {
        this.smartScrollToBottom('page_mount');
      }, 300);
    });
  },

  beforeDestroy() {
    // 清理定时器和事件监听
    this.clearAllTimers();
    uni.$off('pageShow', this.handlePageShow);
    uni.$off('pageHide', this.handlePageHide);
    
    // 关闭AI聊天服务连接
    aiChatService.closeConnection();
  },
  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    
    // 显示欢迎消息
    showWelcomeMessage() {
      // 如果消息列表为空，添加一条欢迎消息
      if(this.messages.length === 0) {
        const welcomeMessage = {
          role: 'assistant',
          content: '你好！我是小叶同学，很高兴能与你交流。有什么我能帮你的吗？',
          timestamp: Date.now()
        };
        this.messages.push(welcomeMessage);
        
        this.$nextTick(() => {
          setTimeout(() => {
            this.smartScrollToBottom('welcome_message');
          }, 200);
        });
      }
    },

    // 清空对话记录
    clearMessages() {
      this.messages = [];
      aiChatService.clearHistory();
      uni.showToast({
        title: '对话已清空',
        icon: 'none'
      });
    },
    
    // 初始化AI聊天服务
    initAiChatService() {
      try {
        // 调用handleAiStreamResponse配置回调
        this.handleAiStreamResponse();
        
        // 成功后显示欢迎消息
        if (this.messages.length === 0) {
          this.showWelcomeMessage();
        }
      } catch (error) {
        console.error('初始化AI聊天服务失败:', error);
        uni.showToast({
          title: '连接AI服务失败',
          icon: 'none'
        });
      }
    },
    
    // 重新初始化AI聊天服务
    reinitAiChatService() {
      // 关闭已有连接
      aiChatService.closeConnection();
      // 重新初始化
      this.initAiChatService();
    },
    
    // 发送消息
	sendMessage() {
	  if (!this.canSend) return;
	  
	  const content = this.inputText.trim();
	  this.inputText = '';
	  
	  // 批量更新消息，减少渲染次数
	  const userMessage = {
	    role: 'user',
	    content,
	    timestamp: Date.now()
	  };
	  
	  const assistantMessage = {
	    role: 'assistant',
	    content: '',
	    isTyping: true,
	    timestamp: Date.now()
	  };
	  
	  // 一次性添加两条消息
	  this.messages.push(userMessage, assistantMessage);
	  
	  this.isSending = true;
	  
	  // 确保滚动到底部，使用新的智能滚动
	  this.$nextTick(() => {
	    setTimeout(() => {
	      this.smartScrollToBottom('new_message');
	    }, 50);
	  });
	  
	  // 重要：在发送消息前设置回调，确保能正确处理响应
	  this.handleAiStreamResponse();
	  
	  // 调用星火认知大模型API获取回复
	  aiChatService.sendMessage(content)
	    .catch(error => {
	      console.error('AI回复出错:', error);
	      // 出错时更新消息状态
	      assistantMessage.isTyping = false;
	      assistantMessage.content = '抱歉，我遇到了一些问题，请稍后再试。';
	      this.isSending = false;
	    });
	},
    
    // 模拟生成回复
    generateResponse(question) {
      // 这里是模拟的回复逻辑，实际项目中应该调用API
      question = question.toLowerCase();
      
      if (question.includes('你是谁') || question.includes('你叫什么') || question.includes('你的名字')) {
        return '我是小叶同学，您的AI智能助手。我随时准备为您提供帮助和服务！';
      } else if (question.includes('笑话') || question.includes('段子')) {
        return '今天给您带来一个有趣的故事：\n\n有一位老人，每天都去公园散步，每天都带着帽子。有一天，一位小朋友问他："爷爷，您为什么每天都戴帽子呢？"\n老人微笑着回答："因为我昨天忘戴了，找了一天也没找到自己。"';
      } else if (question.includes('天气')) {
        return '目前小叶同学不能获取实时天气数据，建议您查看天气预报APP或网站获取最准确的天气信息。未来我会继续升级，为您提供更多服务！';
      } else if (question.includes('能做什么') || question.includes('功能') || question.includes('帮我')) {
        return '我是您的AI助手小叶同学，可以：\n1. 回答您的问题和疑惑\n2. 提供信息和知识\n3. 和您进行日常对话\n4. 讲笑话和有趣的故事\n5. 未来会支持更多功能\n\n请问有什么我可以帮助您的吗？';
      } else {
        return '感谢您的提问。作为您的AI助手，我会尽力为您提供帮助。您的问题很有价值，希望我的回答能够对您有所帮助。如果需要更多信息，请随时告诉我！';
      }
    },
    
    // 处理AI流式输出的打字效果 - 优化版
    handleAiStreamResponse() {
      this.isTyping = true;

      // 获取最后一条AI消息(应该是正在等待回复的消息)
      const latestMessage = this.messages[this.messages.length - 1];
      if (!latestMessage || latestMessage.role !== 'assistant') {
        console.error('找不到要更新的AI消息');
        return;
      }

      // 配置星火AI聊天服务回调
      aiChatService.setCallbacks({
        onMessageStart: () => {
          console.log('AI开始生成回复...');
          this.isTyping = true;
        },
        onMessageStream: (content, isComplete) => {
          // 立即更新消息内容
          latestMessage.content = content;

          // 每次内容更新都尝试滚动跟随
          this.smoothScrollToFollow();

          if (isComplete) {
            console.log('AI消息完成，开始最终滚动确保');
            latestMessage.isTyping = false;
            this.isTyping = false;
            this.isSending = false;

            // 消息完成后多次确保滚动到底部
            this.$nextTick(() => {
              this.ensureScrollToBottom();

              // 延时再次确保
              setTimeout(() => {
                this.ensureScrollToBottom();
              }, 200);

              // 最后再次确保
              setTimeout(() => {
                this.ensureScrollToBottom();
              }, 500);
            });
          }
        },
        onMessageComplete: (finalContent) => {
          // 确保消息内容完整更新
          latestMessage.content = finalContent;
          latestMessage.isTyping = false;
          this.isTyping = false;
          this.isSending = false;

          console.log('onMessageComplete 触发，开始最终滚动');

          // 最终滚动确保 - 多重保险
          this.$nextTick(() => {
            this.ensureScrollToBottom();

            setTimeout(() => {
              this.ensureScrollToBottom();
            }, 100);

            setTimeout(() => {
              this.emergencyScrollToBottom();
            }, 300);

            // 最后的最后，再次确保
            setTimeout(() => {
              this.ensureScrollToBottom();
            }, 600);
          });
        },
        onError: (error) => {
          console.error('AI聊天服务错误:', error);
          latestMessage.isTyping = false;
          latestMessage.content = '抱歉，我遇到了一些问题，请稍后再试。';
          this.isTyping = false;
          this.isSending = false;
        }
      });
    },
    
    // ==================== 豆包式滚动系统核心方法 ====================

    // 初始化滚动系统
    initScrollSystem() {
      try {
        // 确保 handleScroll 方法存在
        if (typeof this.handleScroll === 'function' && typeof this.throttle === 'function') {
          // 创建节流的滚动处理函数
          this.throttledScrollHandler = this.throttle(this.handleScroll.bind(this), 16); // 60fps
        } else {
          console.error('滚动系统初始化失败：缺少必要的方法');
        }
      } catch (error) {
        console.error('滚动系统初始化出错:', error);
      }
    },

    // 平滑滚动到底部 - 核心方法
    smoothScrollToBottom(trigger = 'default') {
      // 如果用户正在滚动且不是强制触发，则不自动滚动
      if (this.isUserScrolling && !['typing_complete', 'new_message', 'page_mount'].includes(trigger)) {
        return;
      }

      // 使用 scroll-into-view 方式进行平滑滚动
      this.performSmoothScroll();
    },

    // 执行平滑滚动
    performSmoothScroll() {
      // 清除之前的滚动计时器
      this.clearScrollTimers();

      // 重置滚动ID
      this.scrollIntoViewId = '';

      // 使用 requestAnimationFrame 确保在下一帧执行
      this.scrollAnimationFrame = requestAnimationFrame(() => {
        this.$nextTick(() => {
          // 延时设置滚动ID，确保DOM更新
          this.autoScrollTimer = setTimeout(() => {
            this.scrollIntoViewId = 'chat-bottom';
          }, 16); // 减少延时，提高响应速度
        });
      });
    },

    // 检查并滚动到底部（用于打字过程中）
    checkAndScrollToBottom() {
      if (this.isAutoScrollEnabled && this.isNearBottom && !this.isUserScrolling) {
        // 使用直接滚动方法，确保流畅跟随
        this.directScrollToBottom();
      }
    },

    // 智能滚动到底部（兼容旧方法名）
    smartScrollToBottom(trigger = 'default') {
      if (['typing_complete', 'new_message', 'page_mount', 'page_show'].includes(trigger)) {
        // 对于关键时刻的滚动，使用直接滚动确保可靠性
        this.directScrollToBottom();
      } else {
        this.smoothScrollToBottom(trigger);
      }
    },

    // 执行滚动到底部（兼容旧方法名）
    performScrollToBottom() {
      this.performSmoothScroll();
    },

    // 处理用户滚动事件
    handleScroll(e) {
      const { scrollTop, scrollHeight, clientHeight } = e.detail;

      // 计算是否接近底部
      const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
      this.isNearBottom = distanceFromBottom <= this.scrollThreshold;

      // 检测用户是否在主动滚动
      const isScrollingUp = scrollTop < this.lastScrollTop;
      const isScrollingDown = scrollTop > this.lastScrollTop;

      if (isScrollingUp || (isScrollingDown && !this.isNearBottom)) {
        // 用户主动滚动，暂停自动滚动
        this.setUserScrolling(true);
      } else if (this.isNearBottom) {
        // 用户滚动到底部附近，恢复自动滚动
        this.setUserScrolling(false);
      }

      this.lastScrollTop = scrollTop;
    },

    // 设置用户滚动状态
    setUserScrolling(isScrolling) {
      this.isUserScrolling = isScrolling;
      this.isAutoScrollEnabled = !isScrolling || this.isNearBottom;

      // 清除之前的恢复计时器
      if (this.scrollEndTimer) {
        clearTimeout(this.scrollEndTimer);
      }

      // 如果用户停止滚动，设置延时恢复自动滚动
      if (isScrolling) {
        this.scrollEndTimer = setTimeout(() => {
          if (this.isNearBottom) {
            this.isUserScrolling = false;
            this.isAutoScrollEnabled = true;
          }
        }, this.userScrollTimeout);
      }
    },
    
    // 加载更多历史消息
    loadMoreMessages() {
      // 实际项目中这里应该加载历史消息
      console.log('加载更多历史消息');
    },
    
    // 格式化消息时间
    formatMessageTime(timestamp) {
      const date = new Date(timestamp);
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      
      // 判断是今天、昨天还是更早
      if (date.toDateString() === today.toDateString()) {
        return this.formatTime(date); // 今天，显示时间
      } else if (date.toDateString() === yesterday.toDateString()) {
        return '昨天 ' + this.formatTime(date); // 昨天
      } else {
        return `${date.getMonth() + 1}月${date.getDate()}日 ${this.formatTime(date)}`; // 更早
      }
    },
    
    // 格式化时间为 HH:MM
    formatTime(date) {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    },
    
    // 判断是否显示时间分隔线
    showTimeDivider(message, index) {
      if (index === 0) return true;
      
      // 与上一条消息相比，如果时间间隔超过5分钟，则显示时间分隔线
      const prevMessage = this.messages[index - 1];
      return message.timestamp - prevMessage.timestamp > 5 * 60 * 1000;
    },
    
    // 切换输入模式（语音/文字）
    toggleInputMode() {
      this.isVoiceMode = !this.isVoiceMode;
    },
    
    // 初始化录音管理器
    initRecorderManager() {
      // #ifdef APP-PLUS || MP-WEIXIN
      this.recorderManager = uni.getRecorderManager();
      
      this.recorderManager.onStart(() => {
        console.log('录音开始');
        this.recordingTime = 0;
        this.recordTimer = setInterval(() => {
          this.recordingTime++;
          
          // 最长录音60秒
          if (this.recordingTime >= 60) {
            this.stopRecording();
          }
        }, 1000);
        
        // 模拟录音音量
        this.volumeTimer = setInterval(() => {
          this.recordingVolume = Math.floor(Math.random() * 10);
        }, 200);
      });
      
      this.recorderManager.onStop((res) => {
        console.log('录音结束', res);
        clearInterval(this.recordTimer);
        clearInterval(this.volumeTimer);
        this.recording = false;
        
        // 模拟语音识别
        setTimeout(() => {
          // 随机选择一个示例问题作为语音识别结果
          const recognizedText = this.suggestedQuestions[Math.floor(Math.random() * this.suggestedQuestions.length)];
          this.inputText = recognizedText;
          this.sendMessage();
        }, 1000);
      });
      // #endif
    },
    
    // 开始录音
    startRecording() {
      // #ifdef APP-PLUS || MP-WEIXIN
      this.recording = true;
      this.recorderManager.start({
        duration: 60000, // 最长60s
        sampleRate: 16000,
        numberOfChannels: 1,
        encodeBitRate: 64000,
        format: 'mp3'
      });
      // #endif
      
      // #ifdef H5
      // H5环境下模拟录音行为
      this.recording = true;
      this.recordingTime = 0;
      this.recordTimer = setInterval(() => {
        this.recordingTime++;
        if (this.recordingTime >= 60) {
          this.stopRecording();
        }
      }, 1000);
      
      this.volumeTimer = setInterval(() => {
        this.recordingVolume = Math.floor(Math.random() * 10);
      }, 200);
      // #endif
    },
    
    // 停止录音
    stopRecording() {
      // #ifdef APP-PLUS || MP-WEIXIN
      if (this.recording) {
        this.recorderManager.stop();
      }
      // #endif
      
      // #ifdef H5
      // H5环境下模拟停止录音
      clearInterval(this.recordTimer);
      clearInterval(this.volumeTimer);
      this.recording = false;
      
      // 模拟语音识别
      setTimeout(() => {
        // 随机选择一个示例问题作为语音识别结果
        const recognizedText = this.suggestedQuestions[Math.floor(Math.random() * this.suggestedQuestions.length)];
        this.inputText = recognizedText;
        this.sendMessage();
      }, 1000);
      // #endif
    },
    
    // 取消录音
    cancelRecording() {
      // #ifdef APP-PLUS || MP-WEIXIN
      if (this.recording) {
        this.recorderManager.stop();
        clearInterval(this.recordTimer);
        clearInterval(this.volumeTimer);
        this.recording = false;
      }
      // #endif
      
      // #ifdef H5
      clearInterval(this.recordTimer);
      clearInterval(this.volumeTimer);
      this.recording = false;
      uni.showToast({
        title: '录音已取消',
        icon: 'none'
      });
      // #endif
    },
    
    // 询问推荐问题
    askSuggestedQuestion(question) {
      this.inputText = question;
      this.sendMessage();
    },

    // ==================== 豆包式滚动辅助方法 ====================

    // 启动实时滚动监控 - 高频检查确保跟随
    startRealTimeScroll() {
      // 清除之前的计时器
      this.stopRealTimeScroll();

      // 每100ms检查一次，确保及时跟随
      this.realTimeScrollTimer = setInterval(() => {
        if (this.isTyping && this.isAutoScrollEnabled && !this.isUserScrolling) {
          this.smoothScrollToFollow();
        }
      }, 100);
    },
    
    // 强制实时滚动跟随 - 重新设计
    smoothScrollToFollow() {
      // 降低节流限制，确保及时响应
      const now = Date.now();
      if (this.lastSmoothScrollTime && now - this.lastSmoothScrollTime < 50) {
        return;
      }
      this.lastSmoothScrollTime = now;

      this.$nextTick(() => {
        const query = uni.createSelectorQuery().in(this);
        query.select('.chat-container').scrollOffset().exec((res) => {
          if (res && res[0]) {
            const { scrollTop, scrollHeight, clientHeight } = res[0];
            const maxScrollTop = Math.max(0, scrollHeight - clientHeight);

            // 如果有内容超出可视区域，就需要滚动
            if (scrollHeight > clientHeight) {
              const currentBottom = scrollTop + clientHeight;
              const distanceFromBottom = scrollHeight - currentBottom;

              console.log('滚动信息:', {
                scrollTop,
                scrollHeight,
                clientHeight,
                maxScrollTop,
                distanceFromBottom
              });

              // 任何超出的内容都要滚动，确保跟随
              if (distanceFromBottom > 10) {
                // 计算滚动步长：距离越远步长越大，但有上限
                let scrollStep;
                if (distanceFromBottom > 300) {
                  scrollStep = Math.min(distanceFromBottom * 0.4, 200); // 大距离快速跟进
                } else if (distanceFromBottom > 100) {
                  scrollStep = Math.min(distanceFromBottom * 0.6, 150); // 中距离适中跟进
                } else {
                  scrollStep = distanceFromBottom; // 小距离直接到底
                }

                const newScrollTop = Math.min(scrollTop + scrollStep, maxScrollTop);
                this.scrollTop = newScrollTop;

                console.log('执行滚动:', { scrollStep, newScrollTop });
              }
            }
          }
        });
      });
    },

    // 强制滚动到最底部 - 多重保险机制
    ensureScrollToBottom() {
      console.log('开始强制滚动到底部');

      // 方法1: scroll-into-view
      this.scrollIntoViewId = '';
      this.$nextTick(() => {
        this.scrollIntoViewId = 'chat-bottom';
        console.log('设置scroll-into-view: chat-bottom');
      });

      // 方法2: 使用scrollTop直接控制
      setTimeout(() => {
        const query = uni.createSelectorQuery().in(this);
        query.select('.chat-container').scrollOffset().exec((res) => {
          if (res && res[0]) {
            const { scrollHeight, clientHeight } = res[0];
            const maxScrollTop = Math.max(0, scrollHeight - clientHeight);

            console.log('强制滚动信息:', {
              scrollHeight,
              clientHeight,
              maxScrollTop
            });

            // 设置到最大滚动位置
            this.scrollTop = maxScrollTop;

            // 方法3: 延时再次确保，多滚动一些
            setTimeout(() => {
              this.scrollTop = maxScrollTop + 100;
              console.log('最终滚动位置:', maxScrollTop + 100);

              // 方法4: 最后再用scroll-into-view确保
              setTimeout(() => {
                this.scrollIntoViewId = '';
                this.$nextTick(() => {
                  this.scrollIntoViewId = 'chat-bottom';
                  console.log('最终scroll-into-view确保');
                });
              }, 100);
            }, 100);
          }
        });
      }, 50);
    },

    // 紧急强制滚动 - 用于处理极端情况
    emergencyScrollToBottom() {
      console.log('执行紧急强制滚动');

      // 立即设置一个很大的scrollTop值
      this.scrollTop = 999999;

      // 同时使用scroll-into-view
      this.scrollIntoViewId = '';
      this.$nextTick(() => {
        this.scrollIntoViewId = 'chat-bottom';

        // 再次设置大值确保
        setTimeout(() => {
          this.scrollTop = 999999;
        }, 50);
      });
    },

    // 直接控制滚动位置到底部（保留兼容性）
    directScrollToBottom() {
      this.ensureScrollToBottom();
    },

    // 强制滚动到底部（保留兼容性）
    forceScrollToBottom() {
      this.ensureScrollToBottom();
    },

    // 停止实时滚动监控
    stopRealTimeScroll() {
      if (this.realTimeScrollTimer) {
        clearInterval(this.realTimeScrollTimer);
        this.realTimeScrollTimer = null;
      }
    },

    // 启用打字时的实时滚动（保留兼容性）
    enableTypingScroll() {
      this.startRealTimeScroll();
    },

    // 禁用打字滚动（保留兼容性）
    disableTypingScroll() {
      this.stopRealTimeScroll();
    },

    // 处理打字时的滚动（保留兼容性）
    handleTypingScroll() {
      this.checkAndScrollToBottom();
    },

    // 页面显示处理
    handlePageShow() {
      this.$nextTick(() => {
        setTimeout(() => {
          this.smartScrollToBottom('page_show');
        }, 200);
      });
    },

    // 页面隐藏处理
    handlePageHide() {
      this.clearAllTimers();
    },

    // 清理所有计时器
    clearAllTimers() {
      if (this.autoScrollTimer) {
        clearTimeout(this.autoScrollTimer);
        this.autoScrollTimer = null;
      }
      if (this.scrollEndTimer) {
        clearTimeout(this.scrollEndTimer);
        this.scrollEndTimer = null;
      }
      if (this.typingScrollTimer) {
        clearInterval(this.typingScrollTimer);
        this.typingScrollTimer = null;
      }
      if (this.realTimeScrollTimer) {
        clearInterval(this.realTimeScrollTimer);
        this.realTimeScrollTimer = null;
      }
      if (this.smoothScrollTimer) {
        clearInterval(this.smoothScrollTimer);
        this.smoothScrollTimer = null;
      }
      if (this.scrollAnimationFrame) {
        cancelAnimationFrame(this.scrollAnimationFrame);
        this.scrollAnimationFrame = null;
      }
    },

    // 清理滚动相关计时器
    clearScrollTimers() {
      if (this.autoScrollTimer) {
        clearTimeout(this.autoScrollTimer);
        this.autoScrollTimer = null;
      }
    },

    // 监听滚动事件 - 入口方法
    onScroll(e) {
      // 确保节流处理函数已初始化
      if (!this.throttledScrollHandler) {
        // 如果还没初始化，先初始化滚动系统
        this.initScrollSystem();
      }

      // 使用节流处理函数处理滚动事件
      if (this.throttledScrollHandler && typeof this.throttledScrollHandler === 'function') {
        try {
          this.throttledScrollHandler(e);
        } catch (error) {
          console.error('滚动处理出错:', error);
          // 重新初始化滚动系统
          this.initScrollSystem();
        }
      } else {
        // 备用处理：直接调用 handleScroll
        if (typeof this.handleScroll === 'function') {
          try {
            this.handleScroll(e);
          } catch (error) {
            console.error('备用滚动处理出错:', error);
          }
        }
      }
    },

    // ==================== 工具方法 ====================

    // 节流函数
    throttle(func, limit) {
      let inThrottle;
      return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
          func.apply(context, args);
          inThrottle = true;
          setTimeout(() => inThrottle = false, limit);
        }
      }
    },

	// 防抖函数（保留原有的，以防其他地方使用）
	debounce(func, wait) {
	  let timeout;
	  return function executedFunction(...args) {
	    const later = () => {
	      clearTimeout(timeout);
	      func(...args);
	    };
	    clearTimeout(timeout);
	    timeout = setTimeout(later, wait);
	  };
	},
    
    // 判断是否包含特殊格式（表格、代码块等）
    hasSpecialFormat(text) {
      if (!text) return false;
      return text.includes('|') || // 表格
             text.includes('```') || // 代码块
             text.includes('###') || // 标题
             text.includes('- ') || // 列表
             text.includes('> ') || // 引用
             text.includes('![') || // 图片
             text.includes('[') && text.includes(']('); // 链接
    },
    
    // 将Markdown转换为HTML（自定义简单实现）
    convertMarkdownToHtml(markdown) {
      if (!markdown) return '';
      try {
        let html = markdown;
        
        // 处理表格
        if (html.includes('|')) {
          // 分割行
          const lines = html.split('\n');
          const tableLines = [];
          let inTable = false;
          let tableHtml = '<table>';
          
          for (const line of lines) {
            // 判断是否是表格行
            if (line.trim().startsWith('|') && line.trim().endsWith('|')) {
              if (!inTable) {
                inTable = true;
              }
              
              // 提取单元格内容
              const cells = line.split('|')
                .filter(cell => cell.trim() !== '')
                .map(cell => cell.trim());
              
              // 判断是否是分隔行（如 |-----|-----|）
              const isSeparator = cells.every(cell => /^[-:]+$/.test(cell));
              
              if (!isSeparator) {
                // 添加行
                const isHeader = tableHtml.indexOf('<tr>') === -1;
                tableHtml += '<tr>';
                
                for (const cell of cells) {
                  if (isHeader) {
                    tableHtml += `<th>${cell}</th>`;
                  } else {
                    tableHtml += `<td>${cell}</td>`;
                  }
                }
                
                tableHtml += '</tr>';
              }
            } else if (inTable) {
              // 表格结束
              inTable = false;
              tableHtml += '</table>';
              tableLines.push(tableHtml);
              tableHtml = '<table>';
            }
          }
          
          // 如果表格未闭合
          if (inTable) {
            tableHtml += '</table>';
            tableLines.push(tableHtml);
          }
          
          // 替换原始文本中的表格
          for (const table of tableLines) {
            html = html.replace(/\|.*?\|\n\|.*?\|\n\|.*?\|([\s\S]*?)(?=\n\n|$)/m, table);
          }
        }
        
        // 处理代码块
        html = html.replace(/```([\s\S]*?)```/g, (match, code) => {
          return `<pre><code>${code.trim()}</code></pre>`;
        });
        
        // 处理标题
        html = html.replace(/^### (.*)$/gm, '<h3>$1</h3>');
        html = html.replace(/^## (.*)$/gm, '<h2>$1</h2>');
        html = html.replace(/^# (.*)$/gm, '<h1>$1</h1>');
        
        // 处理列表
        html = html.replace(/^- (.*)$/gm, '<li>$1</li>');
        html = html.replace(/(<li>.*<\/li>\n)+/g, '<ul>$&</ul>');
        
        // 处理段落
        html = html.replace(/^(?!<[a-z])(.+)$/gm, '<p>$1</p>');
        
        return html;
      } catch (error) {
        console.error('自定义Markdown转HTML失败:', error);
        return markdown; // 如果转换失败，返回原始文本
      }
    },
  }
}
</script>

<style lang="scss" scoped>
/* 页面整体样式 */
page {
  height: 100%;
  overflow: hidden; /* 防止页面整体滚动 */
}

/* 解决小程序和app滚动条的问题 */
/* #ifdef MP-WEIXIN || APP-PLUS */
::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
/* #endif */

/* 解决H5的问题 */
/* #ifdef H5 */
uni-scroll-view .uni-scroll-view::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
/* #endif */

/* 根容器样式 */
.root-container {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #F2F7FF 0%, #FFFFFF 50%);
}

/* 导航栏样式 */
.nav-bar {
  width: 100%;
  padding: 20rpx 32rpx;
  padding-top: calc(20rpx + var(--status-bar-height));
  box-sizing: border-box;
  background: #FFFFFF;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 100;
  
  .nav-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80rpx;
    
    .back-btn {
      width: 80rpx;
      height: 80rpx;
      display: flex;
      align-items: center;
    }
    
    .nav-title {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      
      text {
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
      }
      
      .online-indicator {
        display: flex;
        align-items: center;
        margin-top: 4rpx;
        
        .dot {
          width: 16rpx;
          height: 16rpx;
          border-radius: 50%;
          background-color: #4CD964;
          margin-right: 8rpx;
        }
        
        text {
          font-size: 22rpx;
          color: #666;
          font-weight: normal;
        }
      }
    }
    
    .nav-right {
      width: 80rpx;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }
}

/* 聊天容器样式 - 豆包式滚动优化 */
.chat-container {
  flex: 1;
  width: 100%;
  padding: 20rpx 26rpx 0 26rpx;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
  height: 0;

  /* 豆包式滚动性能优化 */
  -webkit-overflow-scrolling: touch;
  will-change: scroll-position;
  transform: translateZ(0);
  backface-visibility: hidden;

  /* 滚动条隐藏 */
  scrollbar-width: none;
  -ms-overflow-style: none;

  /* 超平滑滚动优化 */
  scroll-behavior: smooth;
  scroll-snap-type: none;

  /* 防止滚动时的闪烁和抖动 */
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);

  /* 优化滚动动画曲线 */
  transition: scroll-behavior 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  /* 优化触摸滚动 */
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  /* 防止过度滚动 */
  overscroll-behavior: contain;
}

/* 欢迎提示样式 */
.welcome-tips {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx;
  margin: 40rpx 0;
  
  .ai-avatar-large {
    width: 120rpx;
    height: 120rpx;
    border-radius: 60rpx;
    margin-bottom: 20rpx;
    background-color: #fff;
  }
  
  .welcome-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 16rpx;
  }
  
  .welcome-desc {
    font-size: 28rpx;
    color: #666;
    text-align: center;
    margin-bottom: 30rpx;
    line-height: 1.5;
  }
}

/* 推荐问题样式 */
.suggested-questions {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-top: 20rpx;
  
  .question-item {
    background: #F2F7FF;
    border: 2rpx solid rgba(75, 123, 236, 0.2);
    padding: 24rpx;
    border-radius: 16rpx;
    text-align: left;
    font-size: 28rpx;
    color: #4B7BEC;
    position: relative;
    transition: all 0.3s;
    
    &:active {
      transform: scale(0.98);
      background: rgba(75, 123, 236, 0.1);
    }
    
    &::after {
      content: "";
      position: absolute;
      width: 16rpx;
      height: 16rpx;
      border-top: 4rpx solid #4B7BEC;
      border-right: 4rpx solid #4B7BEC;
      transform: rotate(45deg);
      top: 50%;
      right: 24rpx;
      margin-top: -8rpx;
    }
  }
}

/* 时间分割线 */
.time-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 30rpx 0;
  
  text {
    font-size: 24rpx;
    color: #999;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 4rpx 16rpx;
    border-radius: 10rpx;
  }
}

/* 消息项样式 */
.message-item {
  display: flex;
  position: relative;
  margin-bottom: 30rpx;
  
  .avatar-container {
    width: 80rpx;
    height: 80rpx;
    flex-shrink: 0;
    
    .avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      border: 2rpx solid #f5f5f5;
    }
  }
  
  .message-content {
    max-width: 70%;
    padding: 16rpx;
    border-radius: 16rpx;
    font-size: 28rpx;
    line-height: 1.5;
    word-break: break-all;
    position: relative;

    /* 豆包式渲染优化 */
    contain: layout style paint;
    will-change: contents;

    /* 优化文字渲染 */
    text-rendering: optimizeSpeed;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    /* 防止打字时的重排 */
    min-height: 1.5em;

    /* 优化动画性能 */
    transform: translateZ(0);
    backface-visibility: hidden;
  }
}

/* 用户消息样式 */
.user-message {
  flex-direction: row-reverse;
  
  .message-content {
    margin-right: 20rpx;
    background-color: #4B7BEC;
    color: #fff;
    
    &::after {
      content: '';
      position: absolute;
      right: -12rpx;
      top: 20rpx;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 8rpx 0 8rpx 14rpx;
      border-color: transparent transparent transparent #4B7BEC;
    }
  }
}

/* AI消息样式 */
.ai-message {
  flex-direction: row;
  
  .message-content {
    margin-left: 20rpx;
    background-color: #f5f7fa;
    color: #333;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
    
    &::after {
      content: '';
      position: absolute;
      left: -12rpx;
      top: 20rpx;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 8rpx 14rpx 8rpx 0;
      border-color: transparent #f5f7fa transparent transparent;
    }
  }
}

/* 打字指示器样式 */
.typing-indicator {
  display: flex;
  align-items: center;
  margin-top: 8rpx;
  
  .typing-dot {
    width: 12rpx;
    height: 12rpx;
    border-radius: 50%;
    background-color: #999;
    margin-right: 6rpx;
    animation: typing-animation 1.5s infinite ease-in-out;
    
    &:nth-child(1) {
      animation-delay: 0s;
    }
    
    &:nth-child(2) {
      animation-delay: 0.2s;
    }
    
    &:nth-child(3) {
      animation-delay: 0.4s;
      margin-right: 0;
    }
  }
  
  @keyframes typing-animation {
    0%, 60%, 100% {
      transform: translateY(0);
      opacity: 0.6;
    }
    30% {
      transform: translateY(-8rpx);
      opacity: 1;
    }
  }
}

/* 底部滚动锚点 - 确保可靠滚动 */
.bottom-anchor {
  height: 10rpx; /* 增加高度确保滚动可见 */
  width: 100%;
  background: transparent;
  pointer-events: none;
  /* 确保锚点始终可见 */
  display: block;
  flex-shrink: 0;
}

.message-padding {
  height: 30rpx;
  width: 100%;
}

/* 输入区域样式 */
.input-container {
  padding: 20rpx 32rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
  display: flex;
  align-items: center;
  position: relative;
  
  .input-type-btn {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
  }
  
  .input-box {
    flex: 1;
    background-color: #F5F7FA;
    border-radius: 36rpx;
    padding: 16rpx 24rpx;
    margin: 0 20rpx;
    max-height: 200rpx;
    overflow-y: auto;
    
    .input-textarea {
      width: 100%;
      min-height: 40rpx;
      max-height: 160rpx;
      font-size: 28rpx;
      line-height: 1.5;
    }
  }
  
  .voice-btn {
    flex: 1;
    height: 72rpx;
    background-color: #F5F7FA;
    border-radius: 36rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 20rpx;
    
    text {
      font-size: 28rpx;
      color: #666;
    }
    
    &:active {
      background-color: #E0E3E8;
    }
  }
  
  .send-btn {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background-color: #CCCCCC;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    transition: all 0.3s;
  }
  
  .send-btn-active {
    background: linear-gradient(135deg, #4B7BEC, #3867D6);
    box-shadow: 0 4rpx 12rpx rgba(56, 103, 214, 0.3);
  }
}

/* 录音提示蒙层 */
.recording-mask {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
  
  .recording-indicator {
    width: 300rpx;
    height: 300rpx;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 24rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    
    .recording-wave {
      display: flex;
      align-items: flex-end;
      height: 100rpx;
      margin-bottom: 30rpx;
      
      .wave-item {
        width: 8rpx;
        height: 20rpx;
        background-color: #4B7BEC;
        margin: 0 6rpx;
        border-radius: 4rpx;
        transition: height 0.2s ease-in-out;
      }
    }
    
    .recording-wave-active {
      .wave-item {
        animation: wave-animation 1s infinite ease-in-out;
        
        &:nth-child(1) {
          animation-delay: 0s;
        }
        
        &:nth-child(2) {
          animation-delay: 0.2s;
        }
        
        &:nth-child(3) {
          animation-delay: 0.4s;
        }
        
        &:nth-child(4) {
          animation-delay: 0.6s;
        }
        
        &:nth-child(5) {
          animation-delay: 0.8s;
        }
      }
    }
    
    @keyframes wave-animation {
      0%, 100% {
        height: 20rpx;
      }
      50% {
        height: 60rpx;
      }
    }
    
    text {
      color: #FFFFFF;
      font-size: 32rpx;
      margin-bottom: 20rpx;
    }
    
    .cancel-text {
      color: #CCCCCC;
      font-size: 24rpx;
    }
  }
}

/* ==================== 豆包式滚动专用动画 ==================== */

/* 消息出现动画 */
@keyframes message-appear {
  0% {
    opacity: 0;
    transform: translateY(20rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 打字内容变化时的微动画 */
@keyframes content-update {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2rpx);
  }
  100% {
    transform: translateY(0);
  }
}

/* 滚动指示器动画 */
@keyframes scroll-hint {
  0%, 100% {
    opacity: 0.3;
    transform: translateY(0);
  }
  50% {
    opacity: 0.8;
    transform: translateY(-10rpx);
  }
}

/* 新消息时的消息项动画 */
.message-item {
  animation: message-appear 0.3s ease-out;
}

/* 打字时内容的微动画 */
.ai-message .message-content {
  transition: all 0.1s ease-out;
}

/* 滚动容器的平滑过渡 */
.chat-container {
  transition: scroll-behavior 0.3s ease;
}

/* 优化滚动时的性能 */
.chat-container::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
}

/* 确保在不同平台下滚动条都隐藏 */
.chat-container {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* 豆包式滚动的关键帧优化 */
@media (prefers-reduced-motion: no-preference) {
  .chat-container {
    scroll-behavior: smooth;
  }

  .message-content {
    transition: transform 0.1s ease-out;
  }
}

/* 减少动画的用户偏好设置 */
@media (prefers-reduced-motion: reduce) {
  .chat-container {
    scroll-behavior: auto;
  }

  .message-item {
    animation: none;
  }

  .message-content {
    transition: none;
  }
}
/* Markdown富文本样式 */
::v-deep .markdown-content {
  width: 100%;
  overflow-x: auto;
  
  /* 表格样式 */
  table {
    width: 100%;
    margin: 10rpx 0;
    border-collapse: collapse;
    background-color: #fff;
    font-size: 28rpx;
    border: 1px solid #e0e0e0;
  }
  
  th, td {
    padding: 16rpx 20rpx;
    border: 1px solid #e0e0e0;
    text-align: left;
  }
  
  th {
    background-color: #f5f5f5;
    color: #333;
    font-weight: 500;
  }
  
  tr:nth-child(even) {
    background-color: #f9f9f9;
  }
  
  /* 代码块样式 */
  pre {
    background-color: #f5f5f5;
    border-radius: 8rpx;
    padding: 20rpx;
    margin: 16rpx 0;
    overflow-x: auto;
  }
  
  code {
    font-family: Consolas, Monaco, 'Andale Mono', monospace;
    font-size: 28rpx;
    color: #333;
  }
  
  /* 行内代码 */
  p code {
    background-color: #f0f0f0;
    padding: 4rpx 8rpx;
    border-radius: 6rpx;
  }
  
  /* 标题样式 */
  h1, h2, h3, h4, h5, h6 {
    margin: 30rpx 0 20rpx 0;
    font-weight: 500;
    color: #333;
  }
  
  /* 引用样式 */
  blockquote {
    border-left: 8rpx solid #e0e0e0;
    padding: 16rpx 30rpx;
    margin: 20rpx 0;
    background-color: #f9f9f9;
    color: #666;
  }
  
  /* 列表样式 */
  ul, ol {
    padding-left: 40rpx;
    margin: 16rpx 0;
  }
  
  li {
    margin: 10rpx 0;
  }
}
</style>
