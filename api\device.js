/**
 * 设备相关API
 */

import { http, Method } from "@/utils/request.js";
import api from '@/config/api.js';


export function getDictList(type, status) {
  return http.request({
    url: `${api.buyer}/trade/device/app/type/list`,
    method: Method.GET,
    params: {type, status},
	needToken: true,
  });
}

export function getAppsByRemarkAndHeadphoneType(params) {
  return http.request({
    url: `${api.buyer}/trade/device/app/type/apps`,
    method: Method.POST,
    data: params,
	needToken: true,
  });
}

export function getAppsByRemark(params) {
  return http.request({
    url: `${api.buyer}/trade/device/app/explain/getAppsByRemark`,
    method: Method.POST,
    data: params,
	needToken: true,
  });
}

export function getPop() {
  return http.request({
    url: `${api.buyer}/trade/device/app/system/getPop`,
    method: Method.GET,
	needToken: true,
  });
}

export function miniConfig() {
  return http.request({
    url: `${api.buyer}/trade/device/app/system/miniConfig`,
    method: Method.GET,
	needToken: true,
  });
}