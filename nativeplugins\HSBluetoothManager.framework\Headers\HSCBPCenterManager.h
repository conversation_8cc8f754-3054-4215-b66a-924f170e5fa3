//
//  HSCBPCenterManager.h
//  WKBlueToothDemo
//
//  Created by hs on 2024/3/26.
//

#import <Foundation/Foundation.h>
#import <CoreBluetooth/CoreBluetooth.h>
#import <UIKit/UIKit.h>

@protocol HSCBPCenterDelegate;
@interface HSCBPCenterManager : NSObject<CBCentralManagerDelegate, CBPeripheralDelegate>

@property(assign,nonatomic)id<HSCBPCenterDelegate>delegate;
//手机设备
@property (nonatomic, strong) CBCentralManager *mCentral;
//外设设备
@property (nonatomic, strong) CBPeripheral *mPeripheral;
//特征值
@property (nonatomic, strong) CBCharacteristic *mCharacteristic;
//特征值 for tablet data
@property (nonatomic, strong) CBCharacteristic *mDataCharacteristic;
//特征值 for command
@property (nonatomic, strong) CBCharacteristic *mCommandCharacteristic;
//服务
@property (nonatomic, strong) CBService *mService;
//描述
@property (nonatomic, strong) CBDescriptor *mDescriptor;
//扫描到的HS蓝牙设备
@property (nonatomic, strong) NSMutableArray *deviceArray;
//已连接上了的设备
@property (nonatomic, strong) CBPeripheral *contectPeripheral;

+(HSCBPCenterManager *)shareInstance;
- (void)contectHSCBPCenter;
- (void)contectBluetooth:(CBPeripheral *)peripheral;
- (void)closeHSCBPCenter;
- (CBPeripheral*)connectSuccessPeripheral;
@end

@protocol HSCBPCenterDelegate  <NSObject>
- (void)HSDrawConnectSuccess;//和HS耳机连接成功
- (void)HSDrawConnectOff;//和HS耳机断开了链接
- (void)HSBluetoothDevices:(NSMutableArray *)deviceArray;//扫描到的HS耳机设备

@end


