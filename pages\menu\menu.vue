<template>
		<view class="content">
			<u-navbar title="菜单" leftIcon="arrow-left" :autoBack="true"  :placeholder="true" bgColor="#f3f4f6" ></u-navbar>
			<view class="tap">
				<view class="taptit1">
					<view class="taptit1l">翻译耳机</view>
					<view class="taptit1r">已连接</view>
				</view>
				<view class="taptit2">
					<view class="taptit1l">设备有效期:未授权</view>
					<view class="taptit1r">20073645</view>
				</view>
			</view>
			<view class="tabbox">
				<view class="tab" @click="tosm">
					<image src="/static/aiku/menu1.svg" mode=""></image>
					<view class="on">使用说明</view>
					<image src="/static/aiku/right.svg" mode="" class="right"></image>
				</view>
				<view class="tab" @click="totk">
					<image src="/static/aiku/menu2.svg" mode="" style="width: 58rpx; height: 58rpx;"></image>
					<view class="on">条款及细则</view>
					<image src="/static/aiku/right.svg" mode="" class="right"></image>
				</view>
				<view class="tab" @click="toys">
					<image src="/static/aiku/menu3.svg" mode=""></image>
					<view class="on">隐私政策</view>
					<image src="/static/aiku/right.svg" mode="" class="right"></image>
				</view>
				<view class="tab" @click="tosz">
					<image src="/static/aiku/menu4.svg" mode=""></image>
					<view class="on">设置</view>
					<image src="/static/aiku/right.svg" mode="" class="right"></image>
				</view>
			</view>
		</view>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		onLoad() {
	
		},
		methods: {
			tosm(){
				uni.navigateTo({
					url:'/pages/menu/sm'
				})
			},
			toys(){
				uni.navigateTo({
					url:'/pages/menu/ys'
				})
			},
			totk(){
				uni.navigateTo({
					url:'/pages/menu/tk'
				})
			},
			tosz(){
				uni.navigateTo({
					url:'/pages/menu/sz'
				})
			}
		}
	}
</script>

<style lang="scss">
	page{
		height: 100%;
		background-color: #fff;
	}
	.content{
		.tabbox{
			width: 100%;
			margin-top: 50rpx;
			.tab{
				width: 100%;
				height: 90rpx;
				display: flex;
				    align-items: center;
				    justify-content: space-evenly;
				    flex-direction: row;
					font-size: 26rpx;
				background-color: #f9f9f9;
				.on{
					width: 500rpx;
				}
				image{
					width: 50rpx;
					height: 50rpx;
				}
				.right{
					width: 30rpx;
					height: 30rpx;
				}
			}
		}
		.tap{
			width: 700rpx;
			height: 200rpx;
			border-radius: 30rpx;
			display: flex;
			flex-direction: column;
		    align-items: center;
			justify-content: space-evenly;
			margin: 0 auto;
			margin-top: 40rpx;
			background-image: linear-gradient(to bottom, #cfe9f9, #fff);
			.taptit1{
				width: 600rpx;
				padding: 0rpx 20rpx;
				font-size: 38rpx;
				font-weight: 500;
				height: 80rpx;
				line-height: 50rpx;
				color: #030502;
				display: flex;
			    justify-content: space-between;
				align-items: center;
				border-bottom: 1rpx solid #ade8e2;
			}
			.taptit2{
				width: 600rpx;
				font-size: 25rpx;
				color: #adadad;
				display: flex;
				justify-content: space-between;
				align-items: center;
			}
		}
	}
</style>